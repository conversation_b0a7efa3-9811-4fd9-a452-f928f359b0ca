<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>transaction-auditing</artifactId>
        <groupId>me.socure</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>transaction-auditing-storage</artifactId>
    <dependencies>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
            <version>4.0</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws.secretsmanager</groupId>
            <artifactId>aws-secretsmanager-jdbc</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>ion-java</artifactId>
                    <groupId>software.amazon.ion</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson.core.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.core.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-clock</artifactId>
            <version>${socure.common.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>transaction-audit-storage-mysql-schema</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
	    <groupId>com.mysql</groupId>
	    <artifactId>mysql-connector-j</artifactId>
	    <version>${mysql.connector.j.version}</version> 
            <exclusions>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>protobuf-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_2.11</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-jmx-service</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-s3-v2</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>transaction-encryption-decryptor</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>transaction-encryption-encryptor</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>transaction-auditing-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- SOCURE COMMON -->
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-docker-mysql-service</artifactId>
            <version>${socure.common.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-docker-postgres-service</artifactId>
            <version>${socure.common.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-docker-client-factory</artifactId>
            <version>${socure.common.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-sql</artifactId>
            <version>${socure.common.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- AUDIT STORAGE -->
        <!-- SCALATRA -->
        <dependency>
            <groupId>org.scalatra</groupId>
            <artifactId>scalatra_2.11</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scalatra</groupId>
            <artifactId>scalatra-scalatest_2.11</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- JETTY SERVER -->
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-webapp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-servlet</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-server</artifactId>
        </dependency>
        <!-- SOCURE COMMON LIBRARY-->
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-config</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-logs</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-metrics</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-scalatraresponse-factory</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-environment</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>transaction-auditing-sqs</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-executioncontext-factory</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-jettythreadpool-factory</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-healthcheck-servlet</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-c3p0-factory</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-slick</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-data-core</artifactId>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>dynamodb</artifactId>
            <version>${aws.java.sdk.v2.version}</version>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>dynamodb</artifactId>
            <version>${aws.java.sdk.v2.version}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
