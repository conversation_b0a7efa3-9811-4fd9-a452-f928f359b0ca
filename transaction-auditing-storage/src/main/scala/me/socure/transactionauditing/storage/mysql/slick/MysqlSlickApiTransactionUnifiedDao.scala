package me.socure.transactionauditing.storage.mysql.slick

import com.github.tototoshi.slick.GenericJodaSupport
import com.github.tototoshi.slick.MySQLJodaSupport._
import me.socure.common.data.core.provider._
import me.socure.common.metrics.models.MetricTags
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.slick.Extensions._
import me.socure.common.slick.domain.Pagination
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration, Slave}
import me.socure.transaction.auditing.encryption.common.AuditDataPii
import me.socure.transaction.auditing.encryption.decryptor.v2.AuditDataDecryptorV2
import me.socure.transactionauditing.common.domain.dynamodb.DynamoDbAuditTableDetails
import me.socure.transactionauditing.common.domain.v2.Constants._
import me.socure.transactionauditing.common.domain.v2.{Records, RequestParams, TransactionSearchRequestV2, TransactionSearchResponseV2}
import me.socure.transactionauditing.common.domain.{TransactionSearchRequest, TransactionSearchRequestDynamo, TransactionSearchResponseData}
import me.socure.transactionauditing.common.product.Products
import me.socure.transactionauditing.common.transaction.TransactionColumns._
import me.socure.transactionauditing.common.transaction.{LongTextColumns, TransactionColumns}
import me.socure.transactionauditing.common.util.SlickDomainConversion._
import me.socure.transactionauditing.storage.TransactionDao
import me.socure.transactionauditing.storage.mysql.slick.table.ApiTransactionExtnTable
import me.socure.transactionauditing.storage.service.CustomFilterProcessor
import me.socure.transactionauditing.storage.util.DynamoDbUtility.{ApiTransactionUnifiedTuple, toApiTransactionUnifiedTuple}
import me.socure.transactionauditing.storage.util.LongTextColumnsReader
import me.socure.transactionauditstorage.mysqlschema.{ApiTransactionUnified, DaoTblAPITransaction, RequestProps, ScoringProps, ApiTransaction => SlickApiTransaction}
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, DateTimeZone}
import org.slf4j.LoggerFactory
import slick.driver.{JdbcProfile, MySQLDriver}
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.model._

import java.util
import javax.sql.DataSource
import scala.collection.JavaConverters._
import scala.collection.Seq
import scala.compat.java8.FutureConverters.CompletionStageOps
import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.concurrent.{ExecutionContext, Future, Promise, TimeoutException}

class MysqlSlickApiTransactionUnifiedDao(
                                          val profile: JdbcProfile,
                                          db: DB[Slave],
                                          dynamoDbTableDetails: DynamoDbAuditTableDetails,
                                          dynamoDbAsyncClient: DynamoDbAsyncClient,
                                          auditDataDecryptor: AuditDataDecryptorV2,
                                          longTextColumnsReader: LongTextColumnsReader
                                        )(implicit exe: ExecutionContext) extends TransactionDao with DaoTblAPITransaction with ApiTransactionExtnTable {

  import me.socure.common.slick.masterslave.defaultprivileges._
  import profile.api._

  override val jodaSupport: GenericJodaSupport = new GenericJodaSupport(profile)


  val NullColumnOption = new LiteralColumn[Option[String]](None)
  val NullColumnOptionLong: LiteralColumn[Option[Long]] = new LiteralColumn[Option[Long]](None)
  val NullColumnOptionBoolean = new LiteralColumn[Option[Boolean]](None)
  val NullColumnOptionDouble = new LiteralColumn[Option[Double]](None)
  val NullColumnOptionDateTime = new LiteralColumn[Option[DateTime]](None)

  val DefaultPagination = Pagination(0, MaxPaginationSize)

  private val metrics: Metrics = JavaMetricsFactory.get(MetricTags.dbMetricPrefix)
  private val logger = LoggerFactory.getLogger(getClass)

  val productSet: Set[String] = Set(Products.EmailAuthScore.V3_0.toString, Products.EmailAuthScore.V2_5.toString, Products.EmailAuthScore.V2.toString, Products.AuthScore.V2_5.toString, Products.AuthScore.V2.toString, Products.Watchlist.V1.toString, Products.EncryptedEmailAuthScore.V3.toString)

  val formatter = DateTimeFormat.forPattern("YYYY-MM").withZone(DateTimeZone.UTC)

  override def findByTransactionId(txId: String, options: Set[TransactionColumn]): Future[Option[SlickApiTransaction]] = {
    val resultsFuture = handleTransactions(options)(TblApiTransactionUnified.filter(_.transactionid === txId))
    resultsFuture.map(_.headOption).withMetricTags(
      metrics = metrics,
      baseTags = DbMetricsTags.getBaseTag(DbMetricsTags.TblApiTransactionUnified, "select", Some("type_single_trx"))
    )()
  }

  override def findByTransactionIdDynamo(transactionId: String, columns: Set[TransactionColumn]): Future[Option[SlickApiTransaction]] = {
    val getItemRequest = GetItemRequest.builder()
      .tableName(dynamoDbTableDetails.tableName)
      .key(Map(dynamoDbTableDetails.partitionKey -> AttributeValue.builder().s(transactionId).build()).asJava)
      .build
    handleDynamoTransactions(columns)(getItemRequest).map(_.headOption)
  }

  override def findByTransactionIdWithInternalData(txId: String, options: Set[TransactionColumn]): Future[Option[SlickApiTransaction]] = {
    for {
      transaction <- findByTransactionId(txId, options)
      internalWorkLogs <- fetchInternalWorkLogs(transaction.flatMap(_.accountId), txId)
    } yield {
      transaction.map(_.copy(internalWorkLogs = internalWorkLogs))
    }
  }

  override def findByTransactionIdDynamoWithInternalData(txId: String, options: Set[TransactionColumn]): Future[Option[SlickApiTransaction]] = {
    for {
      transaction <- findByTransactionIdDynamo(txId, options)
      internalWorkLogs <- fetchInternalWorkLogs(transaction.flatMap(_.accountId), txId)
    } yield {
      transaction.map(_.copy(internalWorkLogs = internalWorkLogs))
    }
  }

  override def findByTransactionIds(txIds: Set[String], options: Set[TransactionColumn]): Future[Seq[SlickApiTransaction]] = {
    val resultsFuture: Future[Seq[SlickApiTransaction]] = handleTransactions(options)(TblApiTransactionUnified.filter { columns =>
      columns.transactionid.inSet(txIds)
    })
    resultsFuture.withMetricTags(
      metrics = metrics,
      baseTags = DbMetricsTags.getBaseTag(DbMetricsTags.TblApiTransactionUnified, "select", Some("type_bulk_trx"))
    )()
  }

  override def findByTransactionIdsDynamo(txIds: Set[String], columns: Set[TransactionColumn]): Future[Seq[SlickApiTransaction]] = {
    val keys = txIds.map(txId =>
      Map(dynamoDbTableDetails.partitionKey -> AttributeValue.builder().s(txId).build()).asJava
    ).asJava
    val batchGetItemRequest: BatchGetItemRequest = BatchGetItemRequest.builder()
      .requestItems(Map(dynamoDbTableDetails.tableName -> KeysAndAttributes.builder().keys(keys).build()).asJava)
      .build()
    handleDynamoTransactions(columns)(batchGetItemRequest)
  }

  override def findByTrxIdOrCuid(accountIds: Set[Long], id: String, options: Set[TransactionColumn]): Future[Seq[SlickApiTransaction]] = {
    handleTransactions(options)(TblApiTransactionUnified.filter(trx => trx.accountid.inSet(accountIds) && trx.transactionid === id)
      .union(TblApiTransactionUnified.filter(trx => trx.accountid.inSet(accountIds) && trx.customeruserid === id))).withMetricTags(
      metrics = metrics,
      baseTags = DbMetricsTags.getBaseTag(DbMetricsTags.TblApiTransactionUnified, "select", Some("type_by_trx_or_cuid"))
    )()
  }

  override def findByTrxIdOrCuidDynamo(accountIds: Set[Long], id: String, columns: Set[TransactionColumn]): Future[Seq[SlickApiTransaction]] = {
    val customerUserIdResponseFutures = accountIds.map(accountId => s"${id}_$accountId").toSeq map { cuid =>
      val queryRequest: QueryRequest = QueryRequest.builder()
        .tableName(dynamoDbTableDetails.tableName)
        .indexName(dynamoDbTableDetails.customerUserIdAccountIdIndexName)
        .keyConditionExpression("customerUserIdAccountId = :cuid")
        .expressionAttributeValues(Map(":cuid" -> AttributeValue.builder().s(cuid).build()).asJava)
        .build()
      handleDynamoTransactions(columns)(queryRequest)
    }
    for {
      result1 <- findByTransactionIdsDynamo(Set(id), columns)
      result2 <- Future.sequence(customerUserIdResponseFutures).map(_.flatten)
    } yield result1 ++ result2
  }

  override def searchTransactions(tsr: TransactionSearchRequest): Future[Seq[SlickApiTransaction]] = {
    handleTransactions(tsr.columns)(generateSearchQuery(tsr)).withMetricTags(
      metrics = metrics,
      baseTags = DbMetricsTags.getBaseTag(DbMetricsTags.TblApiTransactionUnified, "select", Some("type_by_tsr"))
    )()
  }

  override def searchTransactionsDynamo(tsr: TransactionSearchRequestDynamo): Future[TransactionSearchResponseData] = {
    val initialRequestBuilder: QueryRequest.Builder = constructQueryRequest(tsr)

    def recursiveFetch(lastEvaluatedKey: Option[java.util.Map[String, AttributeValue]], acc: Seq[SlickApiTransaction] = Seq.empty, limit: Option[Int]): Future[TransactionSearchResponseData] = {
      val requestBuilder = initialRequestBuilder
      lastEvaluatedKey.foreach(requestBuilder.exclusiveStartKey)
      limit.foreach(requestBuilder.limit(_))
      val request = requestBuilder.build()
      dynamoDbAsyncClient.query(request).toScala.flatMap { response =>
        val items: Seq[ApiTransactionUnifiedTuple] = response.items.asScala.map(item => toApiTransactionUnifiedTuple(item.asScala.toMap, tsr.columns))
        val itemFutures: Seq[Future[SlickApiTransaction]] = items.map(convertDBResultToResponse(_, tsr.columns))
        Future.sequence(itemFutures).flatMap { newItems =>
          val newAccumulated: Seq[SlickApiTransaction] = acc ++ newItems
          if (limit.isDefined && newAccumulated.size >= limit.get) {
            val res: Seq[SlickApiTransaction] = newAccumulated.take(limit.get)
            Future.successful(TransactionSearchResponseData(res, Some(generateLastEvaluatedKey(res.last))))
          } else {
            Option(response.lastEvaluatedKey()).filterNot(_.isEmpty) match {
              case Some(nextKey) =>
                recursiveFetch(Some(nextKey), newAccumulated, limit)
              case None => Future.successful(TransactionSearchResponseData(newAccumulated, None))
            }
          }
        }
      }
    }

    withTimeout(recursiveFetch(None, Seq.empty, tsr.pageSize), 1.minute)
  }

  private def generateLastEvaluatedKey(apiTransaction: SlickApiTransaction): Map[String, String] = {
    val accountId = apiTransaction.accountId.getOrElse(Some(0L))
    val transactionYearMonth = apiTransaction.transactionDate.map(formatter.print(_)).getOrElse("NA")
    val accountIdYearMonth: String = accountId + "_" + transactionYearMonth
    val transactionDateTime = apiTransaction.transactionDate.map(_.getMillis.toString).getOrElse("0")
    Map(
      "accountIdYearMonth" -> accountIdYearMonth,
      "transactionDateTime" -> transactionDateTime,
      "transactionId" -> apiTransaction.transactionId
    )
  }

  private def convertToAttributeValueMap(stringMap: Map[String, String]): util.Map[String, AttributeValue] = {
    val attributeValueMap = new util.HashMap[String, AttributeValue]()
    stringMap.foreach { tuple =>
      attributeValueMap.put(tuple._1, AttributeValue.builder().s(tuple._2).build())
    }
    attributeValueMap
  }

  override def searchTransactionsV2(tsrV2: TransactionSearchRequestV2): Future[TransactionSearchResponseV2] = {
    val requestParams = tsrV2.requestParams
    val pagination = requestParams.pagination.getOrElse(DefaultPagination)
    fetchFromDB(tsrV2.responseParams.columns)(generateSearchQueryV2(requestParams)) map {
      dbTransactionsSet =>
        val customFilteredTransactions = CustomFilterProcessor.applyCustomFilters(dbTransactionsSet, requestParams.filters.custom)
        val nextPage = if(dbTransactionsSet.size >= pagination.size) pagination.page + 1 else -1
        TransactionSearchResponseV2(
          pagination = Some(Pagination(nextPage, pagination.size)),
          records = Records(
            count = customFilteredTransactions.size,
            transactions = customFilteredTransactions
          )
        )
    }
  }

  override def countTransactions(tsr: TransactionSearchRequest): Future[Int] = {
    val finalQuery = generateSearchQuery(tsr)
    db.run(finalQuery.length.result).withMetricTags(
      metrics = metrics,
      baseTags = DbMetricsTags.getBaseTag(DbMetricsTags.TblApiTransactionUnified, "count", Some("type_by_tsr"))
    )()
  }

  override def countTransactionsDynamo(tsr: TransactionSearchRequestDynamo): Future[Int] = {
    val initialRequestBuilder: QueryRequest.Builder = constructQueryRequest(tsr).select(Select.COUNT)
    def recursiveCount(lastEvaluatedKey: Option[java.util.Map[String, AttributeValue]], acc: Int = 0): Future[Int] = {
      val requestBuilder = initialRequestBuilder
      lastEvaluatedKey.foreach(requestBuilder.exclusiveStartKey)
      val request = requestBuilder.build()

      dynamoDbAsyncClient.query(request).toScala.flatMap { response =>
        val newAccumulatedCount = acc + response.count().intValue()
        Option(response.lastEvaluatedKey()).filterNot(_.isEmpty) match {
          case Some(nextKey) => recursiveCount(Some(nextKey), newAccumulatedCount)
          case None => Future.successful(newAccumulatedCount)
        }
      }
    }
    withTimeout(recursiveCount(None), 1.minute)
  }

  private def constructQueryRequest(tsr: TransactionSearchRequestDynamo): QueryRequest.Builder = {
    tsr.accountId match {
      case Some(accountId) =>
        constructQueryForSingleAccount(tsr, accountId)
      case _ =>
        throw new Exception("Request should contain an accountId")
    }
  }

  private def constructQueryForSingleAccount(tsr: TransactionSearchRequestDynamo, accountId: Long) = {
    val expressionAttributeValues = new util.HashMap[String, AttributeValue]()
    val formattedKey = formatAccountIdYearMonthKey(accountId, tsr.start)
    expressionAttributeValues.put(":pk", AttributeValue.builder.s(formattedKey).build())
    tsr.customerUserId.foreach(customerUserId => expressionAttributeValues.put(":customerUserIdAccountId", AttributeValue.builder.s(customerUserId + "_" + accountId).build()))

    tsr.start.foreach(start => expressionAttributeValues.put(":start", AttributeValue.builder.s(start.getMillis.toString).build()))
    tsr.end.foreach(end => expressionAttributeValues.put(":end", AttributeValue.builder.s(end.getMillis.toString).build()))
    tsr.isError.foreach(error => expressionAttributeValues.put(":error", AttributeValue.builder.s(if (error) "1" else "0").build()))
    tsr.environment.foreach(envs => envs.map(_.id.toString).foreach(env => expressionAttributeValues.put(":env" + env.hashCode.abs, AttributeValue.builder.s(env).build())))
    tsr.isKyc.foreach(isKyc => expressionAttributeValues.put(":kyc", AttributeValue.builder.s(if (isKyc) "1" else "0").build()))
    tsr.hasRiskCode.foreach(hrc => expressionAttributeValues.put(":hasRiskCode", AttributeValue.builder.s(if (hrc) "1" else "0").build()))
    tsr.product.foreach { products =>
      products.map(_.toString).foreach(product => expressionAttributeValues.put(":product" + product.hashCode.abs, AttributeValue.builder.s(product).build()))
    }
    tsr.runId.foreach(runId => expressionAttributeValues.put(":runId", AttributeValue.builder.s(runId).build()))

    val expressionAttributeNames = Map(
      "#pk" -> "accountIdYearMonth",
      "#sk" -> "transactionDateTime"
    ).asJava

    val fullFilterExpression = Seq(
      tsr.isError.map(_ => "isError = :error"),
      tsr.environment.map { envs => "(" + envs.map(env => s"environmentId = :env${env.id.toString.hashCode}").mkString(" OR ") + " ) " },
      tsr.product.map { products => products.map(product => s"apiName = :product${product.toString.hashCode.abs}").mkString(" OR ") },
      tsr.isKyc.map(_ => "kyc = :kyc"),
      tsr.hasRiskCode.map(_ => "hasRiskCode = :hasRiskCode"),
      tsr.customerUserId.map(_ => "customerUserIdAccountId = :customerUserIdAccountId"),
      tsr.runId.map(_ => "runId = :runId")
    ).flatten.filter(_.nonEmpty).mkString(" AND ")

    val queryRequestBuilder = QueryRequest.builder()
      .tableName(dynamoDbTableDetails.tableName)
      .indexName(dynamoDbTableDetails.accountIdYearMonthIndexName)
      .keyConditionExpression("#pk = :pk AND #sk BETWEEN :start AND :end")
      .expressionAttributeNames(expressionAttributeNames)

    tsr.exclusiveStartKey.foreach { keyMap =>
      queryRequestBuilder.exclusiveStartKey(convertToAttributeValueMap(keyMap))
    }

    val finalQueryRequestBuilder = if (fullFilterExpression.nonEmpty) queryRequestBuilder.filterExpression(fullFilterExpression) else queryRequestBuilder
    finalQueryRequestBuilder.expressionAttributeValues(expressionAttributeValues)
  }

  private def withTimeout[T](future: Future[T], duration: FiniteDuration): Future[T] = {
    val promise = Promise[T]()
    Future {
      Thread.sleep(duration.toMillis)
      promise.tryFailure(new TimeoutException(s"Operation timed out after ${duration.toMillis} milliseconds"))
    }
    future.onComplete(promise.tryComplete)
    promise.future
  }

  private def formatAccountIdYearMonthKey(accountId: Long, startDate: Option[DateTime]): String = {
    startDate.map { date =>
      f"${accountId}_${date.getYear}-${date.getMonthOfYear}%02d"
    }.getOrElse(throw new IllegalArgumentException("Start date required"))
  }

  private def fetchFromDB(columns: Set[TransactionColumn])(baseQuery: => Query[TblApiTransactionUnified, TblApiTransactionUnified#TableElementType, Seq]) = {
    val query = baseQuery.map(trx => selectColumns(trx, columns))
    val dbResult = db.run(query.result).flatMap(toSlickTransaction(_, columns))
    val transformer = (toUtc _).andThen(removeAccountIdAndApiKeyIfNecessary(_, columns))
    dbResult.flatMap(decryptIfNecessary).map(_.map(transformer))
  }

  private def fetchInternalWorkLogs(accountId: Option[Long], transactionId: String): Future[Option[String]] = longTextColumnsReader.getS3Content(s"${accountId.getOrElse(0L)}/$transactionId/internalWorkLogs.gz")

  private def selectColumns(slickTransaction: TblApiTransactionUnified, options: Set[TransactionColumn]) = {
    (
      slickTransaction.id,
      slickTransaction.transactionid,
      slickTransaction.accountid,
      if (options.contains(ACCOUNT_IP_ADDRESS)) slickTransaction.accountipaddress else NullColumnOption,
      if (options.contains(API)) slickTransaction.api else NullColumnOption,
      slickTransaction.apikey,
      if (options.contains(API_NAME)) slickTransaction.apiname else NullColumnOption,
      if (options.contains(ERROR)) slickTransaction.error else NullColumnOptionBoolean,
      NullColumnOption,
      if (options.contains(TRANSACTION_DATE)) slickTransaction.transactiondate else NullColumnOptionDateTime,
      if (options.contains(PROCESSING_TIME)) slickTransaction.processingtime else NullColumnOptionLong,
      if (options.contains(UUID)) slickTransaction.uuid else NullColumnOption,
      if (options.contains(CACHES_USED)) slickTransaction.cachesused else NullColumnOption,
      if (options.contains(CUSTOMER_USER_ID)) slickTransaction.customeruserid else NullColumnOption,
      slickTransaction.environmentType,
      (
        NullColumnOption,
        if (options.contains(RUN_ID)) slickTransaction.runid else NullColumnOption,
        if (options.contains(KYC)) slickTransaction.kyc else NullColumnOptionBoolean,
        if (options.contains(OFAC)) slickTransaction.ofac else NullColumnOptionBoolean
      ),
      (
        if (options.contains(AUTH_SCORE)) slickTransaction.authscore else NullColumnOptionDouble,
        if (options.contains(CONFIDENCE)) slickTransaction.confidence else NullColumnOptionDouble,
        if (options.contains(HAS_RISK_CODE)) slickTransaction.hasriskcode else NullColumnOptionBoolean
      )
    )
  }

  private def generateSearchQuery(tsr: TransactionSearchRequest): Query[TblApiTransactionUnified, ApiTransactionUnified, Seq] = {
    import me.socure.common.slick.Extensions._

    val query = TblApiTransactionUnified
      .filterWithFallback(tsr.accountIds)(_.accountid inSetBind _)(row => {
        tsr.accountId match {
          case Some(id) => row.accountid === id
          case None => throw new IllegalArgumentException("Did not provide accountId or set of accountIds for query")
        }
      })
      .maybeFilter(tsr.start)(_.transactiondate >= _)
      .maybeFilter(tsr.end)(_.transactiondate <= _)
      .maybeFilter(tsr.environment)(_.environmentType inSetBind _.map(_.id.toLong))
      .filterWithFallback(tsr.product)(_.apiname inSetBind _.map(_.toString))(_.apiname inSetBind productSet)
      .maybeFilter(tsr.customerUserId)(_.customeruserid === _)
      .maybeFilter(tsr.startTransactionId)(_.id >= _)
      .maybeFilter(tsr.isError)((row, isError) => row.error.isDefined && row.error === isError)
      .maybeFilter(tsr.isKyc)((row, isKyc) => row.kyc.isDefined && row.kyc === isKyc)
      .maybeFilter(tsr.runId)(_.runid === _)
      .withSorting(tsr.sorting) { (table, col) =>
        col match {
          case TransactionColumns.ID => table.id
          case TransactionColumns.API => table.api
          case TransactionColumns.API_NAME => table.apiname
          case TransactionColumns.ACCOUNT_ID => table.accountid
          case TransactionColumns.API_KEY => table.apikey
          case TransactionColumns.ACCOUNT_IP_ADDRESS => table.accountipaddress
          case TransactionColumns.TRANSACTION_DATE => table.transactiondate
          case TransactionColumns.AUTH_SCORE => table.authscore
          case TransactionColumns.PROCESSING_TIME => table.processingtime
          case TransactionColumns.CUSTOMER_USER_ID => table.customeruserid
          case TransactionColumns.KYC => table.kyc
          case TransactionColumns.OFAC => table.ofac
          case TransactionColumns.HAS_RISK_CODE => table.hasriskcode
          case TransactionColumns.ERROR => table.error
          case TransactionColumns.CACHES_USED => table.cachesused
          case TransactionColumns.RUN_ID => table.runid
          case TransactionColumns.CONFIDENCE => table.confidence
          case TransactionColumns.ENVIRONMENT_ID => table.environmentType
          case TransactionColumns.UUID => table.uuid
        }
      }
      .withPagination(tsr.pagination)
    query
  }

  private def generateSearchQueryV2(requestParams: RequestParams): Query[TblApiTransactionUnified, ApiTransactionUnified, Seq] = {

    val filters = requestParams.filters
    val pagination = requestParams.pagination.getOrElse(DefaultPagination)
    val sorting = requestParams.sorting

    val columnFilters = filters.columns

    val query = TblApiTransactionUnified
      .filterWithFallback(columnFilters.accountIds)(_.accountid inSetBind _)(row => {
        columnFilters.accountId match {
          case Some(id) => row.accountid === id
          case None => throw new IllegalArgumentException("Did not provide accountId or set of accountIds for query")
        }
      })
      .maybeFilter(columnFilters.startDate)(_.transactiondate >= _)
      .maybeFilter(columnFilters.endDate)(_.transactiondate <= _)
      .maybeFilter(columnFilters.environment)(_.environmentType inSetBind _.map(_.id.toLong))
      .filterWithFallback(columnFilters.product)(_.apiname inSetBind _.map(_.toString))(_.apiname inSetBind productSet)
      .maybeFilter(columnFilters.customerUserId)(_.customeruserid === _)
      .maybeFilter(columnFilters.startRecordId)(_.id >= _)
      .maybeFilter(columnFilters.isError)((row, isError) => row.error.isDefined && row.error === isError)
      .maybeFilter(columnFilters.isKyc)((row, isKyc) => row.kyc.isDefined && row.kyc === isKyc)
      .maybeFilter(columnFilters.runId)(_.runid === _)
      .withSorting(sorting) { (table, col) =>
        col match {
          case TransactionColumns.ID => table.id
          case TransactionColumns.API => table.api
          case TransactionColumns.API_NAME => table.apiname
          case TransactionColumns.ACCOUNT_ID => table.accountid
          case TransactionColumns.API_KEY => table.apikey
          case TransactionColumns.ACCOUNT_IP_ADDRESS => table.accountipaddress
          case TransactionColumns.TRANSACTION_DATE => table.transactiondate
          case TransactionColumns.AUTH_SCORE => table.authscore
          case TransactionColumns.PROCESSING_TIME => table.processingtime
          case TransactionColumns.CUSTOMER_USER_ID => table.customeruserid
          case TransactionColumns.KYC => table.kyc
          case TransactionColumns.OFAC => table.ofac
          case TransactionColumns.HAS_RISK_CODE => table.hasriskcode
          case TransactionColumns.ERROR => table.error
          case TransactionColumns.CACHES_USED => table.cachesused
          case TransactionColumns.RUN_ID => table.runid
          case TransactionColumns.CONFIDENCE => table.confidence
          case TransactionColumns.ENVIRONMENT_ID => table.environmentType
          case TransactionColumns.UUID => table.uuid
        }
      }
      .withPagination(pagination)
    query
  }

  private def handleTransactions(columns: Set[TransactionColumn])(baseQuery: => Query[TblApiTransactionUnified, TblApiTransactionUnified#TableElementType, Seq]): Future[Seq[SlickApiTransaction]] = {
    val query = baseQuery.map(tx => selectColumns(tx, columns))
    val dbResult: Future[Seq[ApiTransactionUnifiedTuple]] = db.run(query.result)
    convertDBResultToResponse(dbResult, columns)
  }

  private def handleDynamoTransactions(columns: Set[TransactionColumn])(request: => DynamoDbRequest): Future[Seq[SlickApiTransaction]] = {
    val dynamoDbResult = request match {
      case getItemRequest: GetItemRequest =>
        dynamoDbAsyncClient.getItem(getItemRequest).toScala.map { resp =>
          if (resp.hasItem) Seq(toApiTransactionUnifiedTuple(resp.item().asScala.toMap, columns)) else Seq.empty[ApiTransactionUnifiedTuple]
        }
      case queryRequest: QueryRequest =>
        dynamoDbAsyncClient.query(queryRequest).toScala.map { response =>
          Option(response.items()).fold(Seq.empty[ApiTransactionUnifiedTuple]) { items =>
            items.asScala.map(item => toApiTransactionUnifiedTuple(item.asScala.toMap, columns))
          }
        }
      case batchGetItemRequest: BatchGetItemRequest =>
        dynamoDbAsyncClient.batchGetItem(batchGetItemRequest).toScala.map { resp =>
          val items: util.List[util.Map[String, AttributeValue]] = resp.responses().getOrDefault(dynamoDbTableDetails.tableName, java.util.Collections.emptyList())
          items.asScala.map(item => toApiTransactionUnifiedTuple(item.asScala.toMap, columns))
        }
      case _ => Future.successful(Seq.empty[ApiTransactionUnifiedTuple])
    }
    convertDBResultToResponse(dynamoDbResult, columns)
  }

  private def convertDBResultToResponse(dbResult: Future[Seq[ApiTransactionUnifiedTuple]], columns: Set[TransactionColumn]): Future[Seq[SlickApiTransaction]] = {
    val res: Future[Seq[SlickApiTransaction]] = dbResult.flatMap(toSlickTransaction(_, columns))
    val transformer: SlickApiTransaction => SlickApiTransaction = (toUtc _).andThen(removeAccountIdAndApiKeyIfNecessary(_, columns = columns))
    res.flatMap(decryptIfNecessary).map(_.map(transformer))
  }

  private def convertDBResultToResponse(apiTransactionUnifiedTuple: ApiTransactionUnifiedTuple, columns: Set[TransactionColumn]): Future[SlickApiTransaction] = {
    val res: Future[Seq[SlickApiTransaction]] = toSlickTransaction(Seq(apiTransactionUnifiedTuple), columns)
    val transformer: SlickApiTransaction => SlickApiTransaction = (toUtc _).andThen(removeAccountIdAndApiKeyIfNecessary(_, columns = columns))
    res.flatMap(decryptIfNecessary).map(_.map(transformer)).map(_.head)
  }


  private def shouldDecrypt(slickApiTransaction: SlickApiTransaction): Boolean = {
    List(
      slickApiTransaction.requestUri,
      slickApiTransaction.parameters,
      slickApiTransaction.details,
      slickApiTransaction.response,
      slickApiTransaction.customerUserId
    ).exists(_.isDefined)
  }

  private def decrypt(slickTransaction: SlickApiTransaction): Future[SlickApiTransaction] = {
    val auditDataPii = AuditDataPii(
      requestUri = slickTransaction.requestUri,
      parameters = slickTransaction.parameters,
      details = slickTransaction.details,
      response = slickTransaction.response,
      customerUserId = slickTransaction.customerUserId
    )
    auditDataDecryptor.decrypt(
      accountId = slickTransaction.accountId,
      apiKey = slickTransaction.apiKey,
      auditDataPii = auditDataPii
    ).map { decryptedAuditDataPii =>
      slickTransaction.copy(
        requestProps = slickTransaction.requestProps.copy(
          requestUri = decryptedAuditDataPii.requestUri,
          parameters = decryptedAuditDataPii.parameters
        ),
        scoringProps = slickTransaction.scoringProps.copy(
          details = decryptedAuditDataPii.details
        ),
        response = decryptedAuditDataPii.response,
        customerUserId = decryptedAuditDataPii.customerUserId
      )
    }
  }

  private def decryptIfNecessary(slickTransactions: Seq[SlickApiTransaction]): Future[Seq[SlickApiTransaction]] = {
    val futures = slickTransactions.map(transaction => {
      if (shouldDecrypt(transaction)) {
        decrypt(transaction)
      } else {
        Future.successful(transaction)
      }
    })
    Future.sequence(futures)
  }

  def toSlickTransaction(p: Seq[ApiTransactionUnifiedTuple], requestedColumns: Set[TransactionColumn]): Future[Seq[SlickApiTransaction]] = {
    Future.sequence(p.map(tupleToSlick(_, requestedColumns)))
  }

  def tupleToSlick(p: ApiTransactionUnifiedTuple, requestedColumns: Set[TransactionColumn]): Future[SlickApiTransaction] = {
    p match {
      case (id, transactionId, accountId, accountIpAddress, api, apiKey, apiName, error, geocode,
      transactionDate, processingTime, uuid, cachesUsed, customerUserId, environmentId,
      (originofinvocation, runid, kyc, ofac),
      (authScore, confidence, hasRiskCode)) =>
        longTextColumnsReader.getLongTextColumns(
          accountId.getOrElse(0L),
          transactionId,
          LongTextColumnsReader.toMap(LongTextColumns(Option.empty[String], Option.empty[String], Option.empty[String], Option.empty[String], Option.empty[String], Option.empty[String], Option.empty[String])),
          requestedColumns
        ) map { longTextColumns =>
          SlickApiTransaction(
            id = id,
            transactionId = transactionId,
            accountId = accountId,
            accountIpAddress = accountIpAddress,
            api = api,
            apiKey = apiKey,
            apiName = apiName,
            error = error,
            errorMsg = longTextColumns.errorMsg,
            geocode = geocode,
            response = longTextColumns.response,
            transactionDate = transactionDate.map(_.withZoneRetainFields(DateTimeZone.UTC)),
            processingTime = processingTime,
            uuid = uuid,
            cachesUsed = cachesUsed,
            customerUserId = customerUserId,
            environmentType = environmentId,
            requestProps = RequestProps(
              originOfInvocation = originofinvocation,
              parameters = longTextColumns.parameters,
              runId = runid,
              requestUri = longTextColumns.requestURI,
              kyc = kyc,
              ofac = ofac
            ),
            scoringProps = ScoringProps(
              authScore = authScore,
              confidence = confidence,
              debug = longTextColumns.debug,
              details = longTextColumns.details,
              reasonCodes = longTextColumns.reasonCodes,
              hasRiskCode = hasRiskCode
            ),
            internalWorkLogs = None
          )
        }
    }
  }

  type SlickApiTransactionTuple = (Option[Long], String, Option[Long], Option[String], Option[String], Option[String], Option[String], Option[Boolean], Option[String], Option[String], Option[String], Option[DateTime], Option[Long], Option[String], Option[String], Option[String], Option[Long], (Option[String], Option[String], Option[String], Option[String], Option[Boolean], Option[Boolean]), (Option[Double], Option[Double], Option[String], Option[String], Option[String], Option[Boolean]))
}

object MysqlSlickApiTransactionUnifiedDao {

  def apply(
             dataSource: DataSource,
             dynamoDbTableDetails: DynamoDbAuditTableDetails,
             dynamoDbAsyncClient: DynamoDbAsyncClient,
             auditDataDecryptor: AuditDataDecryptorV2,
             longTextColumnsReader: LongTextColumnsReader
           )(implicit exe: ExecutionContext): MysqlSlickApiTransactionUnifiedDao = {
    val profile = MySQLDriver
    val databaseConfiguration = DatabaseConfiguration.slave(
      profile = profile,
      dataSource = dataSource
    )
    val db = DB.slave(
      databaseConfiguration = databaseConfiguration
    )
    new MysqlSlickApiTransactionUnifiedDao(
      profile = profile,
      db = db,
      dynamoDbTableDetails = dynamoDbTableDetails,
      dynamoDbAsyncClient = dynamoDbAsyncClient,
      auditDataDecryptor = auditDataDecryptor,
      longTextColumnsReader = longTextColumnsReader
    )
  }
}


