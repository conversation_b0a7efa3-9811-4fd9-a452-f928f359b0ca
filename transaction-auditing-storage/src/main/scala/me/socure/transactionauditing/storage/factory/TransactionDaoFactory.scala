package me.socure.transactionauditing.storage.factory

import me.socure.transaction.auditing.encryption.decryptor.v2.AuditDataDecryptorV2
import me.socure.transactionauditing.common.domain.dynamodb.DynamoDbAuditTableDetails
import me.socure.transactionauditing.storage.mysql.slick.MysqlSlickApiTransactionUnifiedDao
import me.socure.transactionauditing.storage.util.LongTextColumnsReader
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient

import javax.sql.DataSource
import scala.concurrent.ExecutionContext

object TransactionDaoFactory {
  def createUnified(ds: DataSource, auditDataDecryptor: AuditDataDecryptorV2, longTextColumnsReader: LongTextColumnsReader, dynamoDbTableDetails: DynamoDbAuditTableDetails, dynamoDbAsyncClient: DynamoDbAsyncClient)(implicit ec: ExecutionContext): MysqlSlickApiTransactionUnifiedDao = {
    MysqlSlickApiTransactionUnifiedDao(ds, dynamoDbTableDetails, dynamoDbAsyncClient, auditDataDecryptor, longTextColumnsReader)
  }
}
