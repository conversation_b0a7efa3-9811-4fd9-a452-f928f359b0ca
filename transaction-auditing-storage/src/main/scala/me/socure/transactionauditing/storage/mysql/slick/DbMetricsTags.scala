package me.socure.transactionauditing.storage.mysql.slick

import me.socure.common.metrics.models.MetricTags
import me.socure.types.scala.{ByMember, Enum}

object DbMetricsTags extends Enum with ByMember {

  type DbMetricsTags = EnumVal

  case class EnumVal(name: String) extends Value

  val TblApiTransaction = new DbMetricsTags("tbl_api_transaction")
  val TblApiTransactionNew = new DbMetricsTags("tbl_api_transaction_1")
  val TblApiTransactionUnion = new DbMetricsTags("tbl_api_transaction_union")
  val TblApiTransactionUnified = new DbMetricsTags("tbl_api_transaction_unified")
  val TblThirdPartyAuditStats = new DbMetricsTags("tbl_third_party_audit_stats")
  val TblThirdPartyAuditStatsTPCluster = new DbMetricsTags("tbl_third_party_audit_stats_tp_cluster")
  val TblWatchlistMonitoringOperations = new DbMetricsTags("tbl_watchlist_monitoring_operations")
  val TblWatchlistInfo = new DbMetricsTags("tbl_watchlist_info")
  val TblApiTransactionExtn1 = new DbMetricsTags("tbl_api_transaction_extn_1")


  def getBaseTag(table: DbMetricsTags, action: String, queryType: Option[String] = None): MetricTags = {
    MetricTags(
      serviceName = Some("transaction-auditing-service"),
      dbName = Some("socureaudit"),
      tableName = Some(table.name),
      tableAction = Some(action),
      queryType = queryType
    )
  }

}
