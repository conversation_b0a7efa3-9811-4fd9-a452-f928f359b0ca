package me.socure.transactionauditing.storage.service
import me.socure.transactionauditing.common.domain.v2.CustomFilter
import me.socure.transactionauditing.common.transaction.CustomFilterTypes
import me.socure.transactionauditstorage.mysqlschema.ApiTransaction
import org.json4s.{DefaultFormats, JValue}
import org.json4s.native.JsonMethods.parse
import org.slf4j.LoggerFactory

import scala.util.{Failure, Success, Try}

/**
 * Created by sumitkumar on 18/03/22
 **/
class ModulesFilterHandler extends CustomFilterHandler{

  private val logger = LoggerFactory.getLogger(getClass)
  implicit val formats = DefaultFormats

  override def filter(transaction: ApiTransaction, filterParams: CustomFilter): Boolean = {
    filterOnModules(transaction, filterParams)
  }

  private def filterOnModules(transaction: ApiTransaction, moduleFilter: CustomFilter): Boolean = {
    val modulesFromInput = moduleFilter.values
    val modulesFilterType = moduleFilter.filterType.getOrElse(CustomFilterTypes.Any)
    parseParameters(transaction.parameters, transaction.transactionId) match {
      case Some(parameters) =>
        parameters.get("modules") match {
          case Some(txnModulesOpt) =>
            txnModulesOpt match {
              case Some(txnModules) =>
                val txnModulesList = txnModules.asInstanceOf[List[String]].toSet
                if(modulesFilterType.equals(CustomFilterTypes.All)){
                  filterOnAll(txnModulesList, modulesFromInput)
                }else if(modulesFilterType.equals(CustomFilterTypes.Any)){
                  filterOnAny(txnModulesList, modulesFromInput)
                }else true
              case None => false
            }
          case None => false
        }
      case None => false
    }
  }

  private def parseParameters(parameters: Option[String], trxId: String): Option[Map[String,Option[Any]]] = {
    parameters match {
      case Some(x) =>
        Try(parse(x)) match {
          case Success(extractedJson: JValue) =>
            Some(extractedJson.extract[Map[String,Option[Any]]])
          case Failure(ex) =>
            logger.error(s"Error while parsing parameters ${parameters} for transaction $trxId", ex)
            None
        }
      case None => None
    }
  }
}
