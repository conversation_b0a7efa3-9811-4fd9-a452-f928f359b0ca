package me.socure.transactionauditing.storage.util

import java.nio.CharBuffer
import java.nio.charset.{CodingErrorAction, StandardCharsets}

object ValueEncoder {

  private val Iso88591Encoder = StandardCharsets.ISO_8859_1.newEncoder()
    .onMalformedInput(CodingErrorAction.REPLACE)
    .onUnmappableCharacter(CodingErrorAction.REPLACE)
    .replaceWith(Array('?'))

  def encodeAsIso88591(input: String): String = {
    new String(Iso88591Encoder.encode(CharBuffer.wrap(input)).array(), StandardCharsets.ISO_8859_1)
  }

}
