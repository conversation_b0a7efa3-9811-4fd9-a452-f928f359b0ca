package me.socure.transactionauditing.storage.dynamo.model

case class TransactionMetadata(
                                accountIpAddress: Option[String],
                                api: Option[String],
                                apiKey: Option[String],
                                geocode: Option[String],
                                originOfInvocation: Option[String],
                                processingTime: Option[Long],
                                UUID: Option[String],
                                cachesUsed: Option[String],
                                authScore: Option[Double],
                                confidence: Option[Double]
                              )