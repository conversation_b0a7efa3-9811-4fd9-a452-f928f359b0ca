package me.socure.transactionauditing.storage

import me.socure.transactionauditing.sqs.model.SqsApiTransaction
import me.socure.transactionauditing.storage.QueueTransactionDao.{InsertResult, InsertResultVerbose}

import scala.concurrent.{ExecutionContext, Future}

trait QueueTransactionDao {
  def insertAPITransaction(transaction: SqsApiTransaction)(implicit ec: ExecutionContext): Future[InsertResult]

  def insertAPITransactions(transactions: Seq[SqsApiTransaction])(implicit ec: ExecutionContext): Future[Seq[InsertResultVerbose]]

  def transactionExists(transactionId: String)(implicit ec: ExecutionContext): Future[Boolean]
}

object QueueTransactionDao {
  case class InsertResult(id: Option[Long], addedNow: Boolean)
  case class InsertResultVerbose(id: Option[Long], trxId: String, addedNow: Boolean)
}
