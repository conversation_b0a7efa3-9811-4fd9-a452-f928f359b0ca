package me.socure.transactionauditing.storage.dynamo.dao

import me.socure.account.client.GenericClient.logger
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.transactionauditing.sqs.model.SqsApiTransaction
import me.socure.transactionauditing.storage.dynamo.dao.DynamoTransactionDao.InsertResult
import me.socure.transactionauditing.storage.dynamo.model.{DynamoTableConfig, TransactionMetadata}
import org.json4s.DefaultFormats
import org.json4s.jackson.Json
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.dynamodb.model._

import java.time.format.DateTimeFormatter
import java.time.{Instant, ZoneId}
import java.util
import scala.collection.JavaConverters._
import scala.compat.java8.FutureConverters._
import scala.concurrent.{ExecutionContext, Future}

class DynamoTransactionDaoImpl(dynamoDbClient: DynamoDbAsyncClient, dynamoTableConfig: DynamoTableConfig)
    extends DynamoTransactionDao {

  private implicit val Formats: DefaultFormats.type = DefaultFormats
  private val metrics: Metrics = JavaMetricsFactory.get("transaction.auditing." + this.getClass.getSimpleName)
  private val YearMonthFormat = DateTimeFormatter.ofPattern("yyyy-MM").withZone(ZoneId.of("UTC"))
  private val DateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd").withZone(ZoneId.of("UTC"))

  override def insertAPITransaction(
    transaction: SqsApiTransaction
  )(implicit ec: ExecutionContext): Future[InsertResult] = {
    val itemValues: util.Map[String, AttributeValue] = getValuesFromSqsApiTransaction(transaction)
    val putItemRequest: PutItemRequest = PutItemRequest
      .builder()
      .tableName(dynamoTableConfig.tableName)
      .item(itemValues)
      .build()

    dynamoDbClient
      .putItem(putItemRequest)
      .toScala
      .map { response =>
        InsertResult(trxId = transaction.transactionId, success = response.sdkHttpResponse().isSuccessful())
      }
      .recover {
        case ex: Exception =>
          metrics.increment("dynamo.insert.error")
          logger.error("Exception occurred while inserting to DyanamoDB", ex)
          InsertResult(trxId = transaction.transactionId, success = false)
      }
  }

  override def insertAPITransactions(
    transactions: Seq[SqsApiTransaction]
  )(implicit ec: ExecutionContext): Future[Seq[InsertResult]] = {
    val writeRequests = transactions.map { transaction =>
      val itemValues: util.Map[String, AttributeValue] = getValuesFromSqsApiTransaction(transaction)
      WriteRequest.builder().putRequest(PutRequest.builder().item(itemValues).build()).build()
    }
    val batchWriteItemRequest = BatchWriteItemRequest
      .builder()
      .requestItems(Map(dynamoTableConfig.tableName -> writeRequests.asJava).asJava)
      .build()

    dynamoDbClient
      .batchWriteItem(batchWriteItemRequest)
      .toScala
      .flatMap { response =>
        val unprocessed =
          response.unprocessedItems().getOrDefault(dynamoTableConfig.tableName, List.empty[WriteRequest].asJava).asScala
        Future.successful(transactions.map { transaction =>
          if (unprocessed
                .exists(req => req.putRequest().item().get("transactionId").s() == transaction.transactionId)) {
            InsertResult(transaction.transactionId, success = false)
          } else {
            InsertResult(transaction.transactionId, success = true)
          }
        })
      }
      .recover {
        case e: Exception =>
          metrics.increment("dynamo.batch.insert.error")
          logger.error("Exception occurred while saving results to dynamo", e)
          transactions.map(transaction => InsertResult(transaction.transactionId, success = false))
      }
  }

  private def getValuesFromSqsApiTransaction(transaction: SqsApiTransaction): util.Map[String, AttributeValue] = {
    val dateTime = Instant.ofEpochMilli(transaction.transactionDate)
    val yearMonth: String = YearMonthFormat.format(dateTime)
    val accountIdYearMonth = s"${transaction.accountId}_$yearMonth"
    val isError: String = if (transaction.error) "1" else "0"
    val kyc = if (transaction.kyc) "1" else "0"
    val hasRiskCode = if (transaction.hasRiskCode) "1" else "0"
    val customerUserId = transaction.customerUserId.getOrElse("NA")
    val customerUserIdAccountId = s"${customerUserId}_${transaction.accountId}"
    val transactionMetadata: TransactionMetadata = getTransactionMetadata(transaction)
    Map(
      "transactionId" -> AttributeValue.builder().s(transaction.transactionId).build(),
      "accountIdYearMonth" -> AttributeValue.builder().s(accountIdYearMonth).build(),
      "apiName" -> AttributeValue.builder().s(transaction.apiName).build(),
      "isError" -> AttributeValue.builder().s(isError).build(),
      "environmentId" -> AttributeValue.builder().s(transaction.environmentType.toString).build(),
      "kyc" -> AttributeValue.builder().s(kyc).build(),
      "hasRiskCode" -> AttributeValue.builder().s(hasRiskCode).build(),
      "transactionDateTime" -> AttributeValue.builder().s(transaction.transactionDate.toString).build(),
      "runId" -> AttributeValue.builder().s(transaction.runId.getOrElse("NA")).build(),
      "transactionDate" -> AttributeValue.builder().s(DateFormat.format(dateTime)).build(),
      "customerUserIdAccountId" -> AttributeValue.builder().s(customerUserIdAccountId).build(),
      "metadata" -> AttributeValue.builder().s(Json(Formats).write(transactionMetadata)).build()
    ).asJava
  }

  private def getTransactionMetadata(transaction: SqsApiTransaction): TransactionMetadata = {
    TransactionMetadata(
      accountIpAddress = Option(transaction.accountIpAddress),
      api = Option(transaction.api),
      apiKey = Option(transaction.apiKey),
      geocode = transaction.geocode,
      originOfInvocation = transaction.originOfInvocation,
      processingTime = Option(transaction.processingTime),
      UUID = Option(transaction.UUID),
      cachesUsed = transaction.cachesUsed.filter(_ != "[]"),
      authScore = Option(transaction.authScore),
      confidence = Option(transaction.confidence)
    )
  }
}
