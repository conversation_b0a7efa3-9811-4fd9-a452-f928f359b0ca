package me.socure.transactionauditing.storage.service
import me.socure.transactionauditing.common.domain.v2.CustomFilter
import me.socure.transactionauditing.common.transaction.CustomFilterTypes
import me.socure.transactionauditstorage.mysqlschema.ApiTransaction

/**
 * Created by sumit<PERSON> on 18/03/22
 **/
class ReasonCodeFilterHandler extends CustomFilterHandler {
  override def filter(transaction: ApiTransaction, filterParams: CustomFilter): Boolean = {
    filterOnReasonCodes(transaction, filterParams)
  }

  private def filterOnReasonCodes(transaction: ApiTransaction, reasonCodeFilter: CustomFilter): Boolean = {
    val reasonCodesFromInput = reasonCodeFilter.values
    val reasonCodeFilterType = reasonCodeFilter.filterType.getOrElse(CustomFilterTypes.Any)
    transaction.scoringProps.reasonCodes match {
      case None => false
      case Some(reasonCodesFromTxnAsStr) =>
        val reasonCodesFromTxnSet = cleanReasonCodes(reasonCodesFromTxnAsStr)
        if(reasonCodeFilterType.equals(CustomFilterTypes.All)){
          filterOnAll(reasonCodesFromTxnSet, reasonCodesFromInput)
        }else if(reasonCodeFilterType.equals(CustomFilterTypes.Any)){
          filterOnAny(reasonCodesFromTxnSet, reasonCodesFromInput)
        }else true
    }
  }

  private def cleanReasonCodes(reasonCodes: String): Set[String] = {
    reasonCodes
      .trim
      .stripPrefix("[")
      .stripSuffix("]")
      .replaceAll("\"","")
      .split(",")
      .toSet
  }

}
