package me.socure.transactionauditing.storage.service

import me.socure.transactionauditing.common.domain.v2.CustomFilter
import me.socure.transactionauditstorage.mysqlschema.ApiTransaction

abstract class CustomFilterHandler {
  def filter(transaction: ApiTransaction, filterParams: CustomFilter): <PERSON><PERSON><PERSON>

  def filterOnAny(valuesFromDB: Set[String], valuesFromInput: Set[String]): Boolean = {
    valuesFromDB.intersect(valuesFromInput).nonEmpty
  }

  def filterOnAll(valuesFromDB: Set[String], valuesFromInput: Set[String]): Boolean = {
    valuesFromInput.forall(reasonCode => valuesFromDB.contains(reasonCode))
  }
}
