package me.socure.transactionauditing.storage.model

import org.joda.time.DateTime

case class WatchlistInfo(
                          transactionId: String,
                          transactionDate: DateTime,
                          accountId: Long,
                          environmentId: Long,
                          tier: String,
                          watchlistPii: Option[WatchlistPii],
                          watchlistSettings: Option[WatchlistSettings]
                        )

case class WatchlistPii(firstName: Option[String], lastName: Option[String], dob: Option[String])

case class WatchlistSettings(
                              searchId: Option[Int],
                              dobAndName: Option[Boolean],
                              dobTolerance: Option[Boolean],
                              dobMatchLogic: Option[String],
                              fuzzinessTolerance: Option[Double],
                              screeningCategory: Option[Set[String]]
                            ) {
  def isEmpty: Boolean =
    searchId.isEmpty &&
      dobAndName.isEmpty &&
      dobTolerance.isEmpty &&
      dobMatchLogic.isEmpty &&
      fuzzinessTolerance.isEmpty &&
      screeningCategory.isEmpty
}
