package me.socure.transactionauditing.storage.service

import me.socure.transactionauditing.common.domain.v2.Constants._
import me.socure.transactionauditing.common.domain.v2.{Constants, CustomFilter}
import me.socure.transactionauditstorage.mysqlschema.ApiTransaction
import org.json4s.{DefaultFormats, JValue}
import org.slf4j.LoggerFactory


object CustomFilterProcessor {

  private val logger = LoggerFactory.getLogger(getClass)
  implicit val formats = DefaultFormats

  private val filterToImplMapping: Map[String, CustomFilterHandler] = Map(
    Modules -> new ModulesFilterHandler(),
    ReasonCodes -> new ReasonCodeFilterHandler()
  )

  def applyCustomFilters(dbTransactions: Seq[ApiTransaction], customFilterOpt: Option[Map[String, CustomFilter]]): Seq[ApiTransaction] = {
    customFilterOpt match {
      case None => dbTransactions
      case Some(customFilter) =>
        if(customFilter.isEmpty) dbTransactions
        else {
          dbTransactions.filter(transaction => {
            applyCustomFilters(transaction, customFilter)
          })
        }
    }
  }

  private def applyCustomFilters(transaction: ApiTransaction, customFiltersMap: Map[String, CustomFilter]): Boolean = {
    val requiredFilterImpl = loadRequiredFilters(customFiltersMap)
    requiredFilterImpl.forall(filterConfig =>{
      val filterParams = filterConfig._1
      val filterHandler = filterConfig._2
      filterHandler.filter(transaction, filterParams)
    }
    )
  }

  private def loadRequiredFilters(customFiltersMap: Map[String, CustomFilter]): Map[CustomFilter, CustomFilterHandler] = {
    customFiltersMap.map(entry =>
      entry._2 -> filterToImplMapping.getOrElse(entry._1, throw new UnsupportedOperationException("Invalid filter provided"))
    )
  }

}
