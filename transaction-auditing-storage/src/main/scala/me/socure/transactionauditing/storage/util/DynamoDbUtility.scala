package me.socure.transactionauditing.storage.util

import me.socure.transactionauditing.common.transaction.TransactionColumns._
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.JsonAST.{JBool, JValue}
import org.json4s.jackson.JsonMethods
import org.json4s.{DefaultFormats, JString}
import software.amazon.awssdk.services.dynamodb.model.AttributeValue

object DynamoDbUtility {

  private implicit val formats: DefaultFormats.type = DefaultFormats

  type ApiTransactionUnifiedTuple = (Option[Long], String, Option[Long], Option[String], Option[String], Option[String], Option[String], Option[Boolean], Option[String], Option[DateTime], Option[Long], Option[String], Option[String], Option[String], Option[Long], (Option[String], Option[String], Option[Boolean], Option[Boolean]), (Option[Double], Option[Double], Option[Boolean]))

  def toApiTransactionUnifiedTuple(item: Map[String, AttributeValue],
                                   columns: Set[TransactionColumn]
                                  ): ApiTransactionUnifiedTuple = {

    val metadata: JValue = JsonMethods.parse(item("metadata").s())
    (
      Option.empty[Long],
      extractString(item, "transactionId"),
      extractStringOption(item, "accountIdYearMonth").flatMap(_.split("_").headOption).filterNot(_ == "NA").map(_.toLong),
      if (columns.contains(ACCOUNT_IP_ADDRESS)) extractStringOption(metadata, "accountIpAddress") else Option.empty[String],
      if (columns.contains(API)) extractStringOption(metadata, "api") else Option.empty[String],
      if (columns.contains(API_KEY)) extractStringOption(metadata, "apiKey") else Option.empty[String],
      if (columns.contains(API_NAME)) extractStringOption(item, "apiName") else Option.empty[String],
      if (columns.contains(ERROR)) extractBooleanOption(item, "isError") else Option.empty[Boolean],
      Option.empty[String], // geocode
      if (columns.contains(TRANSACTION_DATE)) extractLongOption(item, "transactionDateTime").map(dt => new DateTime(dt, DateTimeZone.UTC)) else Option.empty[DateTime],
      if (columns.contains(PROCESSING_TIME)) extractLongOption(metadata, "processingTime") else Option.empty[Long],
      if (columns.contains(UUID)) extractStringOption(metadata, "UUID") else Option.empty[String],
      if (columns.contains(CACHES_USED)) extractStringOption(metadata, "metadata.cachesUsed") else Option.empty[String],
      if (columns.contains(CUSTOMER_USER_ID)) extractStringOption(item, "customerUserIdAccountId").flatMap(_.split("_").headOption).filterNot(_ == "NA") else Option.empty[String],
      extractLongOption(item, "environmentId"),
      (
        Option.empty[String], // originOfInvocation
        if (columns.contains(RUN_ID)) extractStringOption(item, "runId") else Option.empty[String],
        if (columns.contains(KYC)) extractBooleanOption(item, "kyc") else Option.empty[Boolean],
        if (columns.contains(OFAC)) extractBooleanOption(metadata, "ofac") else Option.empty[Boolean]
      ),
      (
        if (columns.contains(AUTH_SCORE)) extractStringOption(metadata, "authScore").map(_.toDouble) else Option.empty[Double],
        if (columns.contains(CONFIDENCE)) extractDoubleOption(metadata, "confidence") else Option.empty[Double],
        if (columns.contains(HAS_RISK_CODE)) extractBooleanOption(item, "hasRiskCode") else Option.empty[Boolean]
      )
    )
  }

  private def extractStringOption(jValue: JValue, key: String): Option[String] = getOptionalJValue(jValue, key).flatMap(_.extractOpt[String])

  private def extractStringOption(map: Map[String, AttributeValue], key: String): Option[String] = map.get(key).map(_.s.trim).filterNot(_ == "NA")

  private def extractDoubleOption(jValue: JValue, key: String): Option[Double] = getOptionalJValue(jValue, key).flatMap(_.extractOpt[Double])

  private def extractString(map: Map[String, AttributeValue], key: String): String = extractStringOption(map, key).get

  private def extractLongOption(map: Map[String, AttributeValue], key: String): Option[Long] = extractStringOption(map, key).map(_.toLong)

  private def extractLongOption(jValue: JValue, key: String): Option[Long] = getOptionalJValue(jValue, key).flatMap(_.extractOpt[Long])

  private def extractBooleanOption(data: Map[String, AttributeValue], key: String): Option[Boolean] = {
    extractStringOption(data, key).map(interpretAsBoolean)
  }

  private def extractBooleanOption(json: JValue, key: String): Option[Boolean] = {
    getOptionalJValue(json, key) match {
      case Some(JBool(bool)) => Some(bool)
      case Some(JString(str)) => Some(interpretAsBoolean(str))
      case _ => None
    }
  }

  private def interpretAsBoolean(str: String): Boolean = {
    str.trim.toLowerCase match {
      case "1" | "true" => true
      case _ => false
    }
  }

  private def getOptionalJValue(jValue: JValue, path: String): Option[JValue] = {
    path.split("\\.").foldLeft(jValue) { (json, currentPath) =>
        json \ currentPath
      }.toOption
      .filterNot(_ == "NA")
  }
}
