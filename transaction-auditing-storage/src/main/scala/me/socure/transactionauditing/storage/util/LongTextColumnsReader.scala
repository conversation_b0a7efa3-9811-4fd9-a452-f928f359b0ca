package me.socure.transactionauditing.storage.util

import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.retry.Retry
import me.socure.common.retry.strategy.RetryStrategy
import me.socure.common.s3.v2.S3AsyncFiles
import me.socure.transactionauditing.common.transaction.TransactionColumns.TransactionColumn
import me.socure.transactionauditing.common.transaction.{LongTextColumns, TransactionColumns}
import me.socure.transactionauditing.sqs.marshaller.Gzip.getGzipInputStream
import me.socure.transactionauditing.storage.util.LongTextColumnsReader.{LongTextColumnsSet, toMap}
import me.socure.transactionauditstorage.mysqlschema.{ApiTransaction => SlickApiTransaction}
import org.slf4j.LoggerFactory
import me.socure.common.closeable._

import java.io.{BufferedInputStream, InputStream}
import scala.concurrent.duration.DurationInt
import scala.concurrent.{ExecutionContext, Future}

class LongTextColumnsReader(
                             dataBucketName: String,
                             s3AsyncFiles: S3AsyncFiles,
                             batchSize: Int
                           )(implicit exe: ExecutionContext) {

  private val logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get("transaction.auditing." + this.getClass.getSimpleName)
  implicit val retryStrategy: RetryStrategy = RetryStrategy.constantBackoff(500.milliseconds, 3)
  private val GzipExtension = ".gz"

  def replaceLongTextColumns(slickTransactions: Seq[SlickApiTransaction], requestedColumns: Set[TransactionColumn]): Future[Seq[SlickApiTransaction]] = {
    slickTransactions.grouped(batchSize).foldLeft(Future.successful(Seq.empty[SlickApiTransaction])) { (overall, current) =>
      overall.flatMap { prevResult =>
        Future.sequence(current.map { txn =>
          if (shouldReplaceColumns(txn)) {
            getLongTextColumns(
              txn.accountId.getOrElse(0L),
              txn.transactionId,
              toMap(LongTextColumns(txn.debug, txn.details, txn.errorMsg, txn.parameters, txn.reasonCodes, txn.requestUri, txn.response)),
              requestedColumns
            ) map { columnValues =>
              txn.copy(
                errorMsg = if (anyEmpty(txn.errorMsg)) columnValues.errorMsg else txn.errorMsg,
                response = if (anyEmpty(txn.response)) columnValues.response else txn.response,
                requestProps = txn.requestProps.copy(
                  parameters = if (anyEmpty(txn.parameters)) columnValues.parameters else txn.parameters,
                  requestUri = if (anyEmpty(txn.requestUri)) columnValues.requestURI else txn.requestUri
                ),
                scoringProps = txn.scoringProps.copy(
                  debug = if (anyEmpty(txn.debug)) columnValues.debug else txn.debug,
                  details = if (anyEmpty(txn.details)) columnValues.details else txn.details,
                  reasonCodes = if (anyEmpty(txn.reasonCodes)) columnValues.reasonCodes else txn.reasonCodes
                )
              )
            }
          } else Future.successful(txn)
        }).map(prevResult ++ _)
      }
    }
  }

  private def anyEmpty(values: Option[String]*): Boolean = values.exists(e => e.isEmpty || e.exists(_.trim.isEmpty))

  private def shouldReplaceColumns(slickTransaction: SlickApiTransaction): Boolean = {
    anyEmpty(
      slickTransaction.debug,
      slickTransaction.details,
      slickTransaction.errorMsg,
      slickTransaction.parameters,
      slickTransaction.reasonCodes,
      slickTransaction.requestUri,
      slickTransaction.response
    )
  }

  def getLongTextColumns(
                          accountId: Long,
                          transactionId: String,
                          existingLongTextColumns: Map[TransactionColumn, Option[String]],
                          requestedColumns: Set[TransactionColumn]
                        ): Future[LongTextColumns] = {

    def getColumnValue(column: TransactionColumn)(implicit newColumnsMap: Map[TransactionColumn, Option[String]]): Option[String] = {
      newColumnsMap.getOrElse(column, existingLongTextColumns.getOrElse(column, Option.empty[String]))
    }

    val longTextColumnsRequested: Set[TransactionColumn] = requestedColumns.intersect(LongTextColumnsSet)
    val auditFileKeysFuture: Future[Seq[String]] = if(longTextColumnsRequested.nonEmpty) {
      Retry {
        metrics.timeFuture("s3.list.objects") {
          s3AsyncFiles.listFiles(dataBucketName, s"$accountId/$transactionId").map(_.map(_.key()))
        }
      } recover {
        case ex: Throwable =>
          metrics.increment("s3.list.objects.failure")
          logger.error("Error occurred while listing audit files for a transaction", ex.getMessage)
          throw ex
      }
    } else Future.successful(Seq.empty[String])

    auditFileKeysFuture flatMap { auditFileKeys =>
      val longTextColumnKeysToBeRead: Set[LongTextS3FileDetails] = longTextColumnsRequested
        .filter(existingLongTextColumns.getOrElse(_, Option.empty[String]).fold(true)(_.trim.isEmpty))
        .flatMap { column =>
          val fileKey = s"$accountId/$transactionId/${column.name}"
          val compressedFileKey = fileKey + GzipExtension
          if (auditFileKeys.contains(fileKey)) {
            Some(LongTextS3FileDetails(column, fileKey))
          } else if(auditFileKeys.contains(compressedFileKey)) {
            Some(LongTextS3FileDetails(column, compressedFileKey))
          } else None
        }
      longTextColumnKeysToBeRead.foldLeft(Future.successful(Seq.empty[(TransactionColumn, Option[String])])) { (existing, current) =>
        existing.flatMap { prevResult =>
          getS3Content(current.key).map { currResult =>
            prevResult ++ Seq((current.column, currResult))
          }
        }
      } map { columnsData =>
        implicit val columnsMap: Map[TransactionColumn, Option[String]] = columnsData.toMap
        LongTextColumns(
          debug = getColumnValue(TransactionColumns.DEBUG),
          details = getColumnValue(TransactionColumns.DETAILS),
          errorMsg = getColumnValue(TransactionColumns.ERROR_MSG),
          parameters = getColumnValue(TransactionColumns.PARAMETERS),
          reasonCodes = getColumnValue(TransactionColumns.REASON_CODES),
          requestURI = getColumnValue(TransactionColumns.API_REQUEST),
          response = getColumnValue(TransactionColumns.API_RESPONSE)
        )
      }
    }
  }

  def getS3Content(key: String): Future[Option[String]] = {
    metrics.timeFuture("s3.download.object") {
      val s3Content: Future[Option[String]] = if(key.endsWith(GzipExtension)) {
        downloadToStream(dataBucketName, key)
      } else {
        downloadToString(dataBucketName, key)
      }
      s3Content recover {
        case t: Throwable =>
          logger.error(s"Error while reading S3 file $key", t)
          metrics.increment("s3.download.object.failure")
          Option.empty[String]
      }
    }
  }

  private def downloadToStream(dataBucketName: String, key: String): Future[Option[String]] = {
    s3AsyncFiles.downloadToStream(dataBucketName, key) map { stream =>
      val result = decompress(stream)
      if(result.trim.nonEmpty) Some(result) else Option.empty[String]
    }
  }

  private def downloadToString(dataBucketName: String, key: String): Future[Option[String]] = {
    s3AsyncFiles.downloadToString(dataBucketName, key).map(
      result => if(result.trim.nonEmpty) Some(result) else Option.empty[String]
    )
  }

  private def decompress(bis: InputStream): String = {
    new BufferedInputStream(bis).using { buffedIn =>
      getGzipInputStream(buffedIn).using { gzipIn =>
        new String(Stream.continually(gzipIn.read).takeWhile(_ != -1).map(_.toByte).toArray)
      }
    }
  }
}

object LongTextColumnsReader {

  val LongTextColumnsSet: Set[TransactionColumn] = Set(
    TransactionColumns.DEBUG,
    TransactionColumns.DETAILS,
    TransactionColumns.ERROR_MSG,
    TransactionColumns.PARAMETERS,
    TransactionColumns.REASON_CODES,
    TransactionColumns.API_REQUEST,
    TransactionColumns.API_RESPONSE
  )

  def toMap(longTextColumns: LongTextColumns): Map[TransactionColumn, Option[String]] = {
    Map(
      TransactionColumns.DEBUG -> longTextColumns.debug,
      TransactionColumns.DETAILS -> longTextColumns.details,
      TransactionColumns.ERROR_MSG -> longTextColumns.errorMsg,
      TransactionColumns.PARAMETERS -> longTextColumns.parameters,
      TransactionColumns.REASON_CODES -> longTextColumns.reasonCodes,
      TransactionColumns.API_REQUEST -> longTextColumns.requestURI,
      TransactionColumns.API_RESPONSE -> longTextColumns.response
    )
  }

}

case class LongTextS3FileDetails(
                                  column: TransactionColumn,
                                  key: String
                                )
