package me.socure.transactionauditing.storage.mysql.slick.sqs

import com.github.tototoshi.slick.GenericJodaSupport
import me.socure.common.data.core.provider._
import me.socure.common.metrics.models.MetricTags
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.transactionauditing.sqs.model.SqsApiTransaction
import me.socure.transactionauditing.storage.QueueTransactionDao
import me.socure.transactionauditing.storage.QueueTransactionDao.{InsertResult, InsertResultVerbose}
import me.socure.transactionauditing.storage.mysql.slick.DbMetricsTags
import me.socure.transactionauditstorage.mysqlschema.{ApiTransactionUnified, DaoTblAPITransaction, RequestProps, RequestPropsUnified, ScoringProps, ScoringPropsUnified, ApiTransaction => SlickApiTransaction}
import org.joda.time.DateTime
import slick.driver.{JdbcProfile, MySQLDriver}

import javax.sql.DataSource
import scala.concurrent.{ExecutionContext, Future}

class MysqlSlickQueueUnifiedTransactionDao(auditClusterUnifiedDS: DataSource)
  extends QueueTransactionDao
    with DaoTblAPITransaction {

  val profile: JdbcProfile = MySQLDriver

  import profile.api._

  override val jodaSupport: GenericJodaSupport = new GenericJodaSupport(profile)
  val auditDB: profile.backend.Database = profile.backend.Database.forDataSource(auditClusterUnifiedDS)

  private val metrics: Metrics = JavaMetricsFactory.get(MetricTags.dbMetricPrefix)

  override def insertAPITransaction(transaction: SqsApiTransaction)(implicit ec: ExecutionContext): Future[InsertResult] = {
    insertAPITransactionIfNotExists(transaction)
  }

  def insertAPITransactionIfNotExists(transaction: SqsApiTransaction)(implicit ec: ExecutionContext): Future[InsertResult] = {
    val existsCheck = TblApiTransactionUnified.filter(_.transactionid === transaction.transactionId).map(_.id)
    val insertAction = existsCheck.result.headOption.flatMap {
      case Some(id) =>
        DBIO.successful(InsertResult(id = id, addedNow = false))
      case None =>
        for {
          insertResult <- ((TblApiTransactionUnified returning TblApiTransactionUnified.map(_.id)) += toSlickApiTransactionUnified(transaction)).map { dbId =>
            InsertResult(id = dbId, addedNow = true)
          }
        } yield insertResult
    }.transactionally

    auditDB.run(insertAction).withMetricTags(
      metrics = metrics,
      baseTags = DbMetricsTags.getBaseTag(DbMetricsTags.TblApiTransactionUnified, "insert", Some("type_single_trx_insert"))
    )()
  }

  override def transactionExists(transactionId: String)(implicit ec: ExecutionContext): Future[Boolean] = {
    val query = TblApiTransactionUnified.filter(_.transactionid === transactionId).exists
    auditDB.run(query.result).withMetricTags(
      metrics = metrics,
      baseTags = DbMetricsTags.getBaseTag(DbMetricsTags.TblApiTransactionUnified, "select", Some("type_single_trx_exists"))
    )()
  }


  override def insertAPITransactions(transactions: Seq[SqsApiTransaction])(implicit ec: ExecutionContext): Future[Seq[InsertResultVerbose]] = {
    val trxList: Seq[SlickApiTransaction] = transactions.map(toSlickApiTransaction)

    val ids = trxList.map(_.transactionId)

    val queries = for {
      existing <- TblApiTransactionUnified.filter(_.transactionid inSet ids).map(r => (r.id, r.transactionid)).result
      filtered = toApiTransactionUnifiedSeq(trxList.filterNot(trx => existing.exists(_._2 == trx.transactionId)))
      indices <- TblApiTransactionUnified returning TblApiTransactionUnified.map(_.id) into ((trx, id) => trx.copy(id = id)) ++= filtered
      inserted = indices.map {
        a => InsertResultVerbose(a.id, a.transactionId, addedNow = true)
      }
      old = existing.map {
        case (id, trx) =>
          InsertResultVerbose(id, trx, addedNow = false)
      }
    } yield inserted ++ old

    auditDB.run(queries.transactionally).withMetricTags(
      metrics = metrics,
      baseTags = DbMetricsTags.getBaseTag(DbMetricsTags.TblApiTransactionUnified, "insert", Some("type_batch_trx_insert"))
    )()
  }

  private def toSlickApiTransaction(sqsApiTransaction: SqsApiTransaction): SlickApiTransaction = {
    SlickApiTransaction(
      id = None,
      transactionId = sqsApiTransaction.transactionId,
      accountId = Some(sqsApiTransaction.accountId),
      accountIpAddress = Some(sqsApiTransaction.accountIpAddress),
      api = Some(sqsApiTransaction.api),
      apiKey = Some(sqsApiTransaction.apiKey),
      apiName = Some(sqsApiTransaction.apiName),
      error = Some(sqsApiTransaction.error),
      errorMsg = sqsApiTransaction.errorMsg,
      geocode = sqsApiTransaction.geocode,
      response = None,
      transactionDate = Some(new DateTime(sqsApiTransaction.transactionDate)),
      processingTime = Some(sqsApiTransaction.processingTime),
      uuid = Some(sqsApiTransaction.UUID),
      cachesUsed = sqsApiTransaction.cachesUsed,
      customerUserId = sqsApiTransaction.customerUserId,
      environmentType = Some(sqsApiTransaction.environmentType),
      requestProps = RequestProps(
        originOfInvocation = sqsApiTransaction.originOfInvocation,
        parameters = Some(sqsApiTransaction.parameters),
        runId = sqsApiTransaction.runId,
        requestUri = Some(sqsApiTransaction.requestURI),
        kyc = Some(sqsApiTransaction.kyc),
        ofac = Some(sqsApiTransaction.ofac)
      ),
      scoringProps = ScoringProps(
        authScore = Option(sqsApiTransaction.authScore),
        confidence = Option(sqsApiTransaction.confidence),
        debug = None,
        details = Option(sqsApiTransaction.details),
        reasonCodes = Option(sqsApiTransaction.reasonCodes),
        hasRiskCode = Option(sqsApiTransaction.hasRiskCode)
      )
    )
  }

  private def toApiTransactionUnifiedSeq(transactions: Seq[SlickApiTransaction]): Seq[ApiTransactionUnified] = {
    transactions map { transaction =>
      new ApiTransactionUnified(
        id = None,
        transactionId = transaction.transactionId,
        accountId = transaction.accountId,
        accountIpAddress = transaction.accountIpAddress,
        api = transaction.api,
        apiKey = transaction.apiKey,
        apiName = transaction.apiName,
        error = transaction.error,
        geocode = transaction.geocode,
        transactionDate = transaction.transactionDate,
        processingTime = transaction.processingTime,
        uuid = transaction.uuid,
        cachesUsed = transaction.cachesUsed,
        customerUserId = transaction.customerUserId,
        environmentType = transaction.environmentType,
        requestProps = RequestPropsUnified(
          originOfInvocation = transaction.originOfInvocation,
          runId = transaction.runId,
          kyc = transaction.kyc,
          ofac = transaction.ofac
        ),
        scoringProps = ScoringPropsUnified(
          authScore = transaction.authScore,
          confidence = transaction.confidence,
          hasRiskCode = transaction.hasRiskCode
        )
      )
    }
  }

  private def toSlickApiTransactionUnified(sqsApiTransaction: SqsApiTransaction): ApiTransactionUnified = {
    new ApiTransactionUnified(
      id = None,
      transactionId = sqsApiTransaction.transactionId,
      accountId = Some(sqsApiTransaction.accountId),
      accountIpAddress = Some(sqsApiTransaction.accountIpAddress),
      api = Some(sqsApiTransaction.api),
      apiKey = Some(sqsApiTransaction.apiKey),
      apiName = Some(sqsApiTransaction.apiName),
      error = Some(sqsApiTransaction.error),
      geocode = sqsApiTransaction.geocode,
      transactionDate = Some(new DateTime(sqsApiTransaction.transactionDate)),
      processingTime = Some(sqsApiTransaction.processingTime),
      uuid = Some(sqsApiTransaction.UUID),
      cachesUsed = sqsApiTransaction.cachesUsed,
      customerUserId = sqsApiTransaction.customerUserId,
      environmentType = Some(sqsApiTransaction.environmentType),
      requestProps = RequestPropsUnified(
        originOfInvocation = sqsApiTransaction.originOfInvocation,
        runId = sqsApiTransaction.runId,
        kyc = Some(sqsApiTransaction.kyc),
        ofac = Some(sqsApiTransaction.ofac)
      ),
      scoringProps = ScoringPropsUnified(
        authScore = Option(sqsApiTransaction.authScore),
        confidence = Option(sqsApiTransaction.confidence),
        hasRiskCode = Option(sqsApiTransaction.hasRiskCode)
      )
    )
  }
}
