package me.socure.transactionauditing.storage.dynamo.factory

import me.socure.transactionauditing.storage.dynamo.dao.{DynamoTransactionDao, DynamoTransactionDaoImpl}
import me.socure.transactionauditing.storage.dynamo.model.DynamoTableConfig
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient

object DynamoTransactionDaoFactory {
  def create(dynamoDbAsyncClient: DynamoDbAsyncClient, dynamoTableConfig: DynamoTableConfig): DynamoTransactionDao = {
    new DynamoTransactionDaoImpl(dynamoDbAsyncClient, dynamoTableConfig)
  }
}
