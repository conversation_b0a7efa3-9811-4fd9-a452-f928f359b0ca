package me.socure.transactionauditing.storage.mysql.slick.model

import org.joda.time.DateTime

case class SlickThirdPartyTransaction(
                                       id: Option[Long],
                                       accountId: Option[Long],
                                       isCache: Option[Boolean],
                                       processingTime: Option[Long],
                                       serviceId: Option[Int],
                                       startTime: Option[DateTime],
                                       transactionId: String,
                                       uuid: Option[String],
                                       isError: Option[Boolean],
                                       callInstanceId: Option[String])
