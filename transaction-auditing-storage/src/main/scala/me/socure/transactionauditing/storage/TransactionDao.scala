package me.socure.transactionauditing.storage

import me.socure.transactionauditing.common.domain.v2.{TransactionSearchRequestV2, TransactionSearchResponseV2}
import me.socure.transactionauditing.common.domain.{TransactionSearchRequest, TransactionSearchRequestDynamo, TransactionSearchResponseData}
import me.socure.transactionauditing.common.transaction.TransactionColumns.TransactionColumn
import me.socure.transactionauditstorage.mysqlschema.{ApiTransaction => SlickApiTransaction}

import scala.concurrent.Future

trait TransactionDao {
  def findByTransactionId(txId: String, options: Set[TransactionColumn]): Future[Option[SlickApiTransaction]]

  def findByTransactionIdDynamo(txId: String, options: Set[TransactionColumn]): Future[Option[SlickApiTransaction]]

  def findByTransactionIdWithInternalData(txId: String, options: Set[TransactionColumn]): Future[Option[SlickApiTransaction]]

  def findByTransactionIdDynamoWithInternalData(txId: String, options: Set[TransactionColumn]): Future[Option[SlickApiTransaction]]

  def findByTransactionIds(txIds: Set[String], options: Set[TransactionColumn]): Future[Seq[SlickApiTransaction]]

   def findByTransactionIdsDynamo(txIds: Set[String], options: Set[TransactionColumn]): Future[Seq[SlickApiTransaction]]

  def findByTrxIdOrCuid(accountIds: Set[Long], id: String, options: Set[TransactionColumn]): Future[Seq[SlickApiTransaction]]

  def findByTrxIdOrCuidDynamo(accountIds: Set[Long], id: String, options: Set[TransactionColumn]): Future[Seq[SlickApiTransaction]]

  def searchTransactions(tsr: TransactionSearchRequest): Future[Seq[SlickApiTransaction]]

  def searchTransactionsDynamo(tsr: TransactionSearchRequestDynamo): Future[TransactionSearchResponseData]

  def searchTransactionsV2(tsr: TransactionSearchRequestV2): Future[TransactionSearchResponseV2]

  def countTransactions(tsr: TransactionSearchRequest): Future[Int]

  def countTransactionsDynamo(tsr: TransactionSearchRequestDynamo): Future[Int]
}
