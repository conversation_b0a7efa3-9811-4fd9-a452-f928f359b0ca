package me.socure.transactionauditing.storage.model

case class WatchlistMonitoringAction(
                                      accountId: Long,
                                      environmentId: Long,
                                      transactionId: Option[String],
                                      referenceId: Option[String],
                                      operation: Option[String],
                                      entityIds: Seq[String],
                                      updatedAt: Long
                                    ) {
  def isEmpty: Boolean = transactionId.isEmpty || referenceId.isEmpty || operation.isEmpty
}
