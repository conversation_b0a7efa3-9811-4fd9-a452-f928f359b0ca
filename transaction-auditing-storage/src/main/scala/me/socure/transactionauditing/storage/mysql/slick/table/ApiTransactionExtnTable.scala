package me.socure.transactionauditing.storage.mysql.slick.table

import me.socure.transactionauditing.common.domain.ApiTransactionExtn1
import slick.driver.JdbcProfile

trait ApiTransactionExtnTable {

  val profile: JdbcProfile

  import profile.api._

  class ApiTransactionExtn1Table(_tableTag: Tag) extends Table[ApiTransactionExtn1](_tableTag, "tbl_api_transaction_extn_1") {

    val id: Rep[Option[Long]] = column[Option[Long]]("id", O.AutoInc, O.PrimaryKey)
    val transactionId: Rep[String] = column[String]("transactionId")
    val internalWorkLogs: Rep[Option[String]] = column[Option[String]]("internalWorkLogs")

    def * = (id, transactionId, internalWorkLogs) <> (ApiTransactionExtn1.tupled, ApiTransactionExtn1.unapply)
  }

  lazy val ApiTransactionExtn1Query = new TableQuery(tag => new ApiTransactionExtn1Table(tag))
}