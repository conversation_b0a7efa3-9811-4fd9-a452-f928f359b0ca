package me.socure.transactionauditing.storage.dynamo.dao

import me.socure.transactionauditing.sqs.model.SqsApiTransaction
import DynamoTransactionDao.InsertResult

import scala.concurrent.{ExecutionContext, Future}

trait DynamoTransactionDao {
  def insertAPITransaction(transaction: SqsApiTransaction)(implicit ec: ExecutionContext): Future[InsertResult]

  def insertAPITransactions(transactions: Seq[SqsApiTransaction])(implicit ec: ExecutionContext): Future[Seq[InsertResult]]
}

object DynamoTransactionDao {
  case class InsertResult(trxId: String, success: <PERSON>olean)
}
