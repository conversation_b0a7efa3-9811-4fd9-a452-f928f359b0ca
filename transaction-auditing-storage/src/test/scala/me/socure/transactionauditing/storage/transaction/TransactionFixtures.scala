package me.socure.transactionauditing.storage.transaction

import me.socure.transactionauditing.common.transaction.TransactionColumns._

object TransactionFixtures {

   val AllTransactionColumns = Set(
      API,
      API_NAME,
      ACCOUNT_ID,
      API_KEY,
      ACCOUNT_IP_ADDRESS,
      T<PERSON><PERSON><PERSON><PERSON>ON_DATE,
      AUTH_SCORE,
      API_REQUEST,
      API_RESPONSE,
      PROCESSING_TIME,
      CUSTOMER_USER_ID,
      KYC,
      OFAC,
      REASO<PERSON>_CODES,
      HAS_RISK_CODE,
      ERROR,
      ERROR_MSG,
      CACHES_USED,
      PARAMETERS,
      RUN_ID,
      CONFIDENCE,
      ENVIRONMENT_ID,
      DETAILS,
      DEBUG,
      UUID
  )

   val StatsColumns = Set(
      API,
      API_NAME,
      ACCOUNT_ID,
      API_KEY,
      ACCOUNT_IP_ADDRESS,
      TRA<PERSON>ACTION_DATE,
      AUTH_SCORE,
      PROCESSING_TIME,
      CUSTOMER_USER_ID,
      KYC,
      OFAC,
      HAS_RISK_CODE,
      ERROR,
      CACHES_USED,
      RUN_<PERSON>,
      <PERSON><PERSON><PERSON><PERSON><PERSON>,
      ENVIRO<PERSON>MENT_ID,
      UUID
   )
}
