package me.socure.transactionauditing.storage.transaction

import com.github.tototoshi.slick.GenericJodaSupport
import me.socure.common.s3.v2.S3AsyncFiles
import me.socure.transaction.auditing.encryption.common.AuditDataPii
import me.socure.transaction.auditing.encryption.decryptor.v2.AuditDataDecryptorV2
import me.socure.transactionauditing.common.domain.dynamodb.DynamoDbAuditTableDetails
import me.socure.transactionauditing.common.transaction.TransactionColumns._
import me.socure.transactionauditing.common.util.SlickDomainConversion
import me.socure.transactionauditing.sqs.marshaller.Gzip
import me.socure.transactionauditing.storage.mysql.slick.MysqlSlickApiTransactionUnifiedDao
import me.socure.transactionauditing.storage.util.LongTextColumnsReader
import me.socure.transactionauditstorage.mysqlschema.DaoTblAPITransaction
import org.flywaydb.core.Flyway
import org.mockito.Matchers._
import org.mockito.Mockito._
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.scalatest.BeforeAndAfter
import slick.driver.{JdbcProfile, MySQLDriver}
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.s3.model.S3Object

import java.io.ByteArrayInputStream
import java.nio.charset.StandardCharsets
import scala.concurrent.{ExecutionContext, Future}

class SingleLookupTest extends BaseMysqlTest with DaoTblAPITransaction with BeforeAndAfter {
  private val auditDataDecryptor = mock(classOf[AuditDataDecryptorV2])
  implicit val ec: ExecutionContext = ExecutionContext.global
  private val s3AsyncFiles = mock(classOf[S3AsyncFiles])
  private val dynamoDbAuditTableDetails = DynamoDbAuditTableDetails(
    tableName = "tbl_api_transaction_dev",
    partitionKey = "transactionId",
    accountIdYearMonthIndexName = "accountIdYearMonth-index",
    customerUserIdAccountIdIndexName = "customerUserIdAccountId-index"
  )
  private val dynamoDbAsyncClient = mock(classOf[DynamoDbAsyncClient])
  lazy val transactionUnifiedDao: MysqlSlickApiTransactionUnifiedDao = MysqlSlickApiTransactionUnifiedDao(
    ds,
    dynamoDbAuditTableDetails,
    dynamoDbAsyncClient,
    auditDataDecryptor,
    new LongTextColumnsReader("dummy", s3AsyncFiles, 10)
  )
  override val profile: JdbcProfile = MySQLDriver
  override val jodaSupport: GenericJodaSupport = new GenericJodaSupport(profile)



  override def prepareDb(): Unit = {
    val flyway = new Flyway()
    flyway.setLocations("classpath:/db.migration/mysql")
    flyway.setBaselineOnMigrate(true)
    flyway.setPlaceholderReplacement(false)
    flyway.setDataSource(ds)
    flyway.setValidateOnMigrate(false)
    flyway.migrate()
  }

  before {
    reset(auditDataDecryptor)
  }

  test("test find one transaction") {
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.listFiles(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(Seq(S3Object.builder().build())))
    when(s3AsyncFiles.downloadToString(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(""))
    val row = transactionUnifiedDao.findByTransactionId("268746f9-ff80-44ec-a561-89d1c338913f", TransactionFixtures.AllTransactionColumns)
    whenReady(row) {
      p ⇒ {
        p should not be empty
        val transaction = SlickDomainConversion.toApiTransaction(p.get)
        transaction.accountId shouldBe Some(133)
        transaction.apiKey shouldBe Some("716501a8-2926-497a-8909-cd33eea072cf")
        transaction.transactionId shouldBe "268746f9-ff80-44ec-a561-89d1c338913f"
        transaction.productName.get shouldBe "EmailAuthScore (KYC)"
        transaction.apiName.get shouldBe "/api/2.5/EmailAuthScore"
        transaction.processingTime.get shouldBe 1585
        transaction.confidence.get shouldBe 0.31
        transaction.error.get shouldBe false
        transaction.internalWorkLogs shouldBe None
        verify(auditDataDecryptor, times(p.size)).decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))
      }
    }
  }

  test("test find one transaction with internalData") {
    val internalWorkLogs = "[{\"message\": \"Error message\",\"level\": \"error\",\"time\": \"2021-11-22 15:40:11.555\"}]"
    val compressed: Array[Byte] = Gzip.compress(internalWorkLogs.getBytes(StandardCharsets.UTF_8))
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.downloadToStream("dummy", "133/268746f9-ff80-44ec-a561-89d1c338913f/internalWorkLogs.gz"))
      .thenReturn(
        Future.successful(new ByteArrayInputStream(compressed))
      )
    val row = transactionUnifiedDao.findByTransactionIdWithInternalData("268746f9-ff80-44ec-a561-89d1c338913f", TransactionFixtures.AllTransactionColumns)
    whenReady(row) {
      p ⇒ {
        p should not be empty
        val transaction = SlickDomainConversion.toApiTransaction(p.get)
        transaction.accountId shouldBe Some(133)
        transaction.apiKey shouldBe Some("716501a8-2926-497a-8909-cd33eea072cf")
        transaction.transactionId shouldBe "268746f9-ff80-44ec-a561-89d1c338913f"
        transaction.productName.get shouldBe "EmailAuthScore (KYC)"
        transaction.apiName.get shouldBe "/api/2.5/EmailAuthScore"
        transaction.processingTime.get shouldBe 1585
        transaction.confidence.get shouldBe 0.31
        transaction.error.get shouldBe false
        transaction.internalWorkLogs shouldBe Some(internalWorkLogs)
        verify(auditDataDecryptor, times(p.size)).decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))
      }
    }
  }

  test("miss a transaction") {
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    val row = this.transactionUnifiedDao.findByTransactionId("42", TransactionFixtures.AllTransactionColumns)
    whenReady(row) {
      p ⇒ {
        assert(p.isEmpty)
        p shouldBe empty
        verify(auditDataDecryptor, times(p.size)).decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))
      }
    }
  }

  test("select only basic columns about the transaction") {
    val row = this.transactionUnifiedDao.findByTransactionId("268746f9-ff80-44ec-a561-89d1c338913f", Set(
      PARAMETERS,
      API,
      API_NAME,
      ACCOUNT_ID,
      API_KEY,
      TRANSACTION_DATE,
      AUTH_SCORE,
      ERROR,
      ENVIRONMENT_ID
    ))
    whenReady(row) {
      p ⇒ {
        p should not be empty
        val transaction = SlickDomainConversion.toApiTransaction(p.get)
        transaction.accountId shouldBe Some(133)
        transaction.apiKey shouldBe Some("716501a8-2926-497a-8909-cd33eea072cf")
        transaction.fraudScore shouldBe None
        transaction.transactionId shouldBe "268746f9-ff80-44ec-a561-89d1c338913f"
        transaction.requestUri shouldBe None
        transaction.productName.get shouldBe "EmailAuthScore"
        transaction.apiName.get shouldBe "/api/2.5/EmailAuthScore"
        transaction.processingTime shouldBe None
        transaction.confidence shouldBe None
        transaction.error.get shouldBe false
        transaction.apiResponse shouldBe None
      }
    }
  }

  test("select only basic columns about the transaction without account id") {
    val row = this.transactionUnifiedDao.findByTransactionId("268746f9-ff80-44ec-a561-89d1c338913f", Set(
      PARAMETERS,
      API_KEY,
      API,
      API_NAME,
      TRANSACTION_DATE,
      AUTH_SCORE,
      ERROR,
      ENVIRONMENT_ID
    ))
    whenReady(row) {
      p ⇒ {
        p should not be empty
        val transaction = SlickDomainConversion.toApiTransaction(p.get)
        transaction.accountId shouldBe None
        transaction.apiKey shouldBe Some("716501a8-2926-497a-8909-cd33eea072cf")
        transaction.fraudScore shouldBe None
        transaction.transactionId shouldBe "268746f9-ff80-44ec-a561-89d1c338913f"
        transaction.requestUri shouldBe None
        transaction.productName.get shouldBe "EmailAuthScore"
        transaction.apiName.get shouldBe "/api/2.5/EmailAuthScore"
        transaction.processingTime shouldBe None
        transaction.confidence shouldBe None
        transaction.error.get shouldBe false
        transaction.apiResponse shouldBe None
      }
    }
  }

  test("test find one transaction with basic info for the dashboard listing") {
    val row = this.transactionUnifiedDao.findByTransactionId("268746f9-ff80-44ec-a561-89d1c338913f", Set(
      API,
      API_NAME,
      TRANSACTION_DATE,
      PROCESSING_TIME,
      KYC,
      ERROR,
      PARAMETERS,
      ENVIRONMENT_ID,
      UUID
    ))
    whenReady(row) {
      p ⇒ {
        p should not be empty
        val transaction = SlickDomainConversion.toApiTransaction(p.get)
        transaction.accountId shouldBe None
        transaction.apiKey shouldBe None
        transaction.fraudScore shouldBe None
        transaction.transactionId shouldBe "268746f9-ff80-44ec-a561-89d1c338913f"
        transaction.requestUri shouldBe None
        transaction.productName.get shouldBe "EmailAuthScore (KYC)"
        transaction.apiName.get shouldBe "/api/2.5/EmailAuthScore"
        transaction.processingTime.get shouldBe 1585
        transaction.confidence shouldBe None
        transaction.error.get shouldBe false
        transaction.apiResponse shouldBe None
      }
    }
  }

  test("should not attempt to decrypt if we don't select any encryptable information") {
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    val row = this.transactionUnifiedDao.findByTransactionId("268746f9-ff80-44ec-a561-89d1c338913f", Set(
      API,
      API_NAME,
      ACCOUNT_ID,
      API_KEY,
      TRANSACTION_DATE,
      AUTH_SCORE,
      ERROR,
      ENVIRONMENT_ID
    ))
    whenReady(row) {
      p ⇒ {
        p should not be empty
        val transaction = SlickDomainConversion.toApiTransaction(p.get)
        transaction.accountId shouldBe Some(133)
        transaction.apiKey shouldBe Some("716501a8-2926-497a-8909-cd33eea072cf")
        transaction.fraudScore shouldBe None
        transaction.transactionId shouldBe "268746f9-ff80-44ec-a561-89d1c338913f"
        transaction.requestUri shouldBe None
        transaction.productName.get shouldBe "EmailAuthScore"
        transaction.apiName.get shouldBe "/api/2.5/EmailAuthScore"
        transaction.parameters shouldBe None
        transaction.processingTime shouldBe None
        transaction.confidence shouldBe None
        transaction.error.get shouldBe false
        transaction.apiResponse shouldBe None
        verify(auditDataDecryptor, times(0)).decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))
      }
    }
  }
}
