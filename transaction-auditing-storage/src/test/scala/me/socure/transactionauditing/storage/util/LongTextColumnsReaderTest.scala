package me.socure.transactionauditing.storage.util

import me.socure.common.s3.v2.S3AsyncFiles
import me.socure.transactionauditing.sqs.marshaller.Gzip
import me.socure.transactionauditstorage.mysqlschema.{ApiTransaction, RequestProps, ScoringProps}
import org.mockito.Mockito.{mock, reset, when}
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Milliseconds, Seconds, Span}
import org.scalatest.{BeforeAndAfter, FunSuite, Matchers}
import software.amazon.awssdk.services.s3.model._

import java.io.{ByteArrayInputStream, InputStream}
import java.nio.charset.StandardCharsets
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Random

class LongTextColumnsReaderTest extends FunSuite with BeforeAndAfter with Matchers with ScalaFutures {

  implicit val ec: ExecutionContext = ExecutionContext.global

  private val s3AsyncFiles: S3AsyncFiles = mock(classOf[S3AsyncFiles])

  private val Reader = new LongTextColumnsReader("test", s3AsyncFiles, 2)

  private val Random = new Random

  private implicit val patienceConf: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(50, Milliseconds))

  before {
    reset(s3AsyncFiles)
  }

  test("Should not replace long text columns if already present") {
    val txnsList = (1 to 10).toList.map(id => aAPITransaction(id))
    whenReady(Reader.replaceLongTextColumns(txnsList, LongTextColumnsReader.LongTextColumnsSet)) { result =>
      result.size shouldBe txnsList.size
      result.map(txnsList.contains).reduce(_ && _) shouldBe true
    }
  }

  test("Should replace long text columns if not present") {
    val txnsList = (1 to 1).toList.map(id => aAPITransaction(id = id, errorMsg = Some("  "), debug = Some(""), response = None))
    val s3Objects = Future.successful(
      Seq(
        S3Object.builder().key("123/abc/errorMsg").build(),
        S3Object.builder().key("123/abc/debug").build(),
        S3Object.builder().key("123/abc/apiResponse").build()
      )
    )

    when(s3AsyncFiles.listFiles("test", "123/abc")).thenReturn(s3Objects)
    when(s3AsyncFiles.downloadToString("test", "123/abc/errorMsg")).thenReturn(Future.successful("replaced errorMsg"))
    when(s3AsyncFiles.downloadToString("test", "123/abc/debug")).thenReturn(Future.successful("replaced debug"))
    when(s3AsyncFiles.downloadToString("test", "123/abc/apiResponse")).thenReturn(Future.successful("replaced response"))

    whenReady(Reader.replaceLongTextColumns(txnsList, LongTextColumnsReader.LongTextColumnsSet)) { result =>
      result.size shouldBe txnsList.size
      result.head.errorMsg.get shouldBe "replaced errorMsg"
      result.head.debug.get shouldBe "replaced debug"
      result.head.response.get shouldBe "replaced response"
      result.head.parameters shouldBe txnsList.head.parameters
      result.head.requestUri shouldBe txnsList.head.requestUri
      result.head.reasonCodes shouldBe txnsList.head.reasonCodes
      result.head.details shouldBe txnsList.head.details
      result.head.id.get shouldBe 1
    }
  }

  test("Should not replace long text columns if s3 file isn't present") {
    val txnsList = (1 to 1).toList.map(id => aAPITransaction(id = id, errorMsg = Some("  "), debug = Some(""), response = None))
    val s3Objects = Future.successful(
      Seq(
        S3Object.builder().key("123/abc/errorMsg").build(),
        S3Object.builder().key("123/abc/debug").build()
      )
    )

    when(s3AsyncFiles.listFiles("test", "123/abc")).thenReturn(s3Objects)
    when(s3AsyncFiles.downloadToString("test", "123/abc/errorMsg")).thenReturn(Future.successful("replaced errorMsg"))
    when(s3AsyncFiles.downloadToString("test", "123/abc/debug")).thenReturn(Future.successful("replaced debug"))

    whenReady(Reader.replaceLongTextColumns(txnsList, LongTextColumnsReader.LongTextColumnsSet)) { result =>
      result.size shouldBe txnsList.size
      result.head.errorMsg.get shouldBe "replaced errorMsg"
      result.head.debug.get shouldBe "replaced debug"
      result.head.response shouldBe None
      result.head.parameters shouldBe txnsList.head.parameters
      result.head.requestUri shouldBe txnsList.head.requestUri
      result.head.reasonCodes shouldBe txnsList.head.reasonCodes
      result.head.details shouldBe txnsList.head.details
      result.head.id.get shouldBe 1
    }
  }

  test("Should not replace long text columns if s3 file isn't present (in bulk)") {
    val txnsList = (1 to 100).toList.map(id => aAPITransaction(id = id, errorMsg = Some("  "), debug = Some(""), response = None))
    val s3Objects = Future.successful(
      Seq(
        S3Object.builder().key("123/abc/errorMsg").build(),
        S3Object.builder().key("123/abc/debug").build()
      )
    )

    when(s3AsyncFiles.listFiles("test", "123/abc")).thenReturn(s3Objects)
    when(s3AsyncFiles.downloadToString("test", "123/abc/errorMsg")).thenReturn(Future.successful("replaced errorMsg"))
    when(s3AsyncFiles.downloadToString("test", "123/abc/debug")).thenReturn(Future.successful("replaced debug"))

    whenReady(Reader.replaceLongTextColumns(txnsList, LongTextColumnsReader.LongTextColumnsSet)) { result =>
      result.size shouldBe txnsList.size
      result.map(_.errorMsg.get).forall(_ == "replaced errorMsg") shouldBe true
      result.map(_.debug.get).forall(_ == "replaced debug") shouldBe true
      result.map(_.response).forall(_.isEmpty) shouldBe true
      result.map(_.id.get).toList shouldBe (1 to 100).toList
      result.map(_.parameters.get).toList shouldBe txnsList.map(_.parameters.get)
      result.map(_.requestUri.get).toList shouldBe txnsList.map(_.requestUri.get)
      result.map(_.reasonCodes.get).toList shouldBe txnsList.map(_.reasonCodes.get)
      result.map(_.details.get).toList shouldBe txnsList.map(_.details.get)
    }
  }

  test("Should fetch gzip compressed data") {
    val txnsList = (1 to 10).toList.map(id => aAPITransaction(id = id, errorMsg = Some("  "), debug = Some(""), response = None))
    val s3Objects = Future.successful(
      Seq(
        S3Object.builder().key("123/abc/errorMsg.gz").build(),
        S3Object.builder().key("123/abc/debug.gz").build()
      )
    )
    val compressedErrorMsg: Array[Byte] = Gzip.compress("replaced errorMsg".getBytes(StandardCharsets.UTF_8))
    val compressedDebug: Array[Byte] = Gzip.compress("replaced debug".getBytes(StandardCharsets.UTF_8))

    when(s3AsyncFiles.listFiles("test", "123/abc")).thenReturn(s3Objects)
    when(s3AsyncFiles.downloadToStream("test", "123/abc/errorMsg.gz"))
      .thenAnswer(
        new Answer[Future[InputStream]]() {
          override def answer(invocationOnMock: InvocationOnMock): Future[InputStream] = Future.successful(
            new ByteArrayInputStream(compressedErrorMsg)
          )
        }
      )
    when(s3AsyncFiles.downloadToStream("test", "123/abc/debug.gz"))
      .thenAnswer(
        new Answer[Future[InputStream]]() {
          override def answer(invocationOnMock: InvocationOnMock): Future[InputStream] = Future.successful(
            new ByteArrayInputStream(compressedDebug)
          )
        }
      )

    whenReady(Reader.replaceLongTextColumns(txnsList, LongTextColumnsReader.LongTextColumnsSet)) { result =>
      result.size shouldBe txnsList.size
      result.map(_.errorMsg.get).forall(_ == "replaced errorMsg") shouldBe true
      result.map(_.debug.get).forall(_ == "replaced debug") shouldBe true
      result.map(_.response).forall(_.isEmpty) shouldBe true
      result.map(_.id.get).toList shouldBe (1 to 10).toList
      result.map(_.parameters.get).toList shouldBe txnsList.map(_.parameters.get)
      result.map(_.requestUri.get).toList shouldBe txnsList.map(_.requestUri.get)
      result.map(_.reasonCodes.get).toList shouldBe txnsList.map(_.reasonCodes.get)
      result.map(_.details.get).toList shouldBe txnsList.map(_.details.get)
    }
  }

  private def aAPITransaction(
                               id: Int,
                               errorMsg: Option[String] = Some(Random.nextString(10)),
                               parameters: Option[String] = Some(Random.nextString(10)),
                               requestUri: Option[String] = Some(Random.nextString(10)),
                               response: Option[String] = Some(Random.nextString(10)),
                               debug: Option[String] = Some(Random.nextString(10)),
                               details: Option[String] = Some(Random.nextString(10)),
                               reasonCodes: Option[String] = Some(Random.nextString(10))
                             ): ApiTransaction = {
    ApiTransaction(
      id = Some(id),
      transactionId = "abc",
      accountId = Some(123L),
      errorMsg = errorMsg,
      requestProps = RequestProps(
        parameters = parameters,
        requestUri = requestUri
      ),
      response = response,
      scoringProps = new ScoringProps(
        debug = debug,
        details = details,
        reasonCodes = reasonCodes
      ),
      environmentType = Some(1L)
    )
  }

}
