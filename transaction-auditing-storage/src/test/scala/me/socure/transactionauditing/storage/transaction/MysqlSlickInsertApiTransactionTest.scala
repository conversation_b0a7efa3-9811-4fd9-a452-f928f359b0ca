package me.socure.transactionauditing.storage.transaction

import me.socure.common.s3.v2.S3AsyncFiles
import me.socure.common.slick.domain.Sort
import me.socure.constants.EnvironmentConstants
import me.socure.transaction.auditing.encryption.common.AuditDataPii
import me.socure.transaction.auditing.encryption.decryptor.v2.AuditDataDecryptorV2
import me.socure.transactionauditing.common.domain.TransactionSearchRequest
import me.socure.transactionauditing.common.domain.dynamodb.DynamoDbAuditTableDetails
import me.socure.transactionauditing.common.transaction.TransactionColumns
import me.socure.transactionauditing.common.util.SlickDomainConversion
import me.socure.transactionauditing.sqs.marshaller.Gzip
import me.socure.transactionauditing.sqs.model.SqsApiTransaction
import me.socure.transactionauditing.storage.mysql.slick.MysqlSlickApiTransactionUnifiedDao
import me.socure.transactionauditing.storage.mysql.slick.sqs.MysqlSlickQueueUnifiedTransactionDao
import me.socure.transactionauditing.storage.util.LongTextColumnsReader
import org.flywaydb.core.Flyway
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import org.mockito.Matchers.any
import org.mockito.Mockito._
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.scalatest.BeforeAndAfter
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.s3.model.S3Object

import java.io.ByteArrayInputStream
import java.nio.charset.StandardCharsets
import scala.concurrent.{ExecutionContext, Future}

class MysqlSlickInsertApiTransactionTest extends BaseMysqlTest with BeforeAndAfter {
  implicit val ec :ExecutionContext = ExecutionContext.global
  override  def prepareDb(): Unit = {
    val insertMigration= new Flyway()
    insertMigration.setLocations("classpath:/db/migration/mysql_transaction")
    insertMigration.setValidateOnMigrate(false)
    insertMigration.setDataSource(ds)
    insertMigration.migrate()
  }

  val allEnvs = Set(
    EnvironmentConstants.PRODUCTION_ENVIRONMENT,
    EnvironmentConstants.PRODUCTION_ENVIRONMENT
  )

  private val auditDataDecryptor = mock(classOf[AuditDataDecryptorV2])
  private val s3AsyncFiles = mock(classOf[S3AsyncFiles])
  private val dynamoDbAuditTableDetails = DynamoDbAuditTableDetails(
    tableName = "tbl_api_transaction_dev",
    partitionKey = "transactionId",
    accountIdYearMonthIndexName = "accountIdYearMonth-index",
    customerUserIdAccountIdIndexName = "customerUserIdAccountId-index"
  )
  private val dynamoDbAsyncClient = mock(classOf[DynamoDbAsyncClient])
  lazy val mysqldb: MysqlSlickApiTransactionUnifiedDao = MysqlSlickApiTransactionUnifiedDao(
    ds,
    dynamoDbAuditTableDetails,
    dynamoDbAsyncClient,
    auditDataDecryptor,
    new LongTextColumnsReader("dummy", s3AsyncFiles, 10)
  )
  lazy val mysqlInsertDb = new MysqlSlickQueueUnifiedTransactionDao(ds)

  before {
    reset(auditDataDecryptor)
  }

  test("insert a transaction and see if we can find it") {
    val internalWorkLogs = "[{\"message\": \"Test message\",\"level\": \"info\",\"time\": \"2021-11-22 12:20:45.786\"}]"
    val compressed: Array[Byte] = Gzip.compress(internalWorkLogs.getBytes(StandardCharsets.UTF_8))
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.downloadToStream("dummy", "133/716501a8-2926-497a-8909-************/internalWorkLogs.gz"))
      .thenReturn(
        Future.successful(new ByteArrayInputStream(compressed))
      )
    when(s3AsyncFiles.listFiles(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(Seq(S3Object.builder().build())))
    when(s3AsyncFiles.downloadToString(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful("DUMMY_OBJECT"))
    val insertTransaction =mysqlInsertDb.insertAPITransaction(SqsApiTransaction(
      id = None,
      transactionId = "716501a8-2926-497a-8909-************",
      transactionDate = DateTime.now().getMillis,
      processingTime = 1235,
      api = "test-api",
      parameters = "{\"nationalid\":1234,\"ofac\":false,\"city\":\"Red Deer\",\"runid\":null,\"driverlicensestate\":null,\"mobilenumber\":\"+14033913967\",\"zip\":\"T4N 2A6\",\"email\":\"<EMAIL>\",\"state\":\"CO\",\"socurekey\":\"716501a8-2926-497a-8909-cd33eea072cf\",\"companyname\":\"So cure\",\"dob\":\"1980-01-01\",\"firstname\":\"Hazel\",\"physicaladdress2\":null,\"physicaladdress\":\"3398 Galts Ave\",\"ipaddress\":null,\"nocache\":null,\"surname\":\"Beck\",\"watchlist\":null,\"country\":\"US\",\"customeruserid\":null,\"userid\":null,\"debug\":true,\"driverlicense\":null,\"datascience\":false,\"geocode\":null,\"forcerefresh\":true,\"isblacklist\":false,\"impersonatorapikey\":null,\"details\":true,\"kyc\":true}",
      response = """{}""",
      error = false,
      errorMsg = None,
      accountIpAddress = "localhost",
      originOfInvocation = None,
      geocode = None,
      apiKey = "test-api-key",
      accountId = 133,
      apiName = "/api/3.0/EmailAuthScore",
      UUID = "uuid",
      cachesUsed = None,
      debug = "",
      details = "",
      authScore = 1.0,
      confidence = 1.0,
      reasonCodes = "[R104]",
      hasRiskCode = false,
      customerUserId = None,
      runId = None,
      requestURI = "url/fake/3.0",
      kyc = true,
      ofac = false,
      environmentType = 1,
      internalWorkLogs = Some(internalWorkLogs)
    ))

    whenReady(insertTransaction) { p ⇒
      p.addedNow shouldBe true
      val row = mysqldb.findByTransactionIdWithInternalData("716501a8-2926-497a-8909-************", TransactionFixtures.AllTransactionColumns)
      whenReady(row) {
        p ⇒ {
          p should not be empty
          val transaction = SlickDomainConversion.toApiTransaction(p.get)
          transaction.accountId shouldBe Some(133)
          transaction.apiKey shouldBe Some("test-api-key")
          transaction.transactionId shouldBe "716501a8-2926-497a-8909-************"
          transaction.productName.get shouldBe "EmailAuthScore (KYC)"
          transaction.apiName.get shouldBe "/api/3.0/EmailAuthScore"
          transaction.processingTime.get shouldBe 1235
          transaction.confidence.get shouldBe 1.0
          transaction.error.get shouldBe false
          transaction.internalWorkLogs.get shouldBe internalWorkLogs
        }
      }
    }
  }

  test("insert a transaction and see if we can find it with S3 read") {
    val internalWorkLogs = "[{\"message\": \"Test message\",\"level\": \"info\",\"time\": \"2021-11-22 12:20:45.786\"}]"
    val compressed: Array[Byte] = Gzip.compress(internalWorkLogs.getBytes(StandardCharsets.UTF_8))
    when(s3AsyncFiles.downloadToStream("dummy", "133/716501a8-2926-497a-8909-************/internalWorkLogs.gz"))
      .thenReturn(
        Future.successful(new ByteArrayInputStream(compressed))
      )
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.listFiles(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(Seq(S3Object.builder().key("133/716501a8-2926-497a-8909-************/debug").build())))
    when(s3AsyncFiles.downloadToString(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful("DUMMY_OBJECT"))
    val insertTransaction = mysqlInsertDb.insertAPITransaction(SqsApiTransaction(
      id = None,
      transactionId = "716501a8-2926-497a-8909-************",
      transactionDate = DateTime.now().getMillis,
      processingTime = 1235,
      api = "test-api",
      parameters = "{\"nationalid\":1234,\"ofac\":false,\"city\":\"Red Deer\",\"runid\":null,\"driverlicensestate\":null,\"mobilenumber\":\"+14033913967\",\"zip\":\"T4N 2A6\",\"email\":\"<EMAIL>\",\"state\":\"CO\",\"socurekey\":\"716501a8-2926-497a-8909-cd33eea072cf\",\"companyname\":\"So cure\",\"dob\":\"1980-01-01\",\"firstname\":\"Hazel\",\"physicaladdress2\":null,\"physicaladdress\":\"3398 Galts Ave\",\"ipaddress\":null,\"nocache\":null,\"surname\":\"Beck\",\"watchlist\":null,\"country\":\"US\",\"customeruserid\":null,\"userid\":null,\"debug\":true,\"driverlicense\":null,\"datascience\":false,\"geocode\":null,\"forcerefresh\":true,\"isblacklist\":false,\"impersonatorapikey\":null,\"details\":true,\"kyc\":true}",
      response = """{}""",
      error = false,
      errorMsg = None,
      accountIpAddress = "localhost",
      originOfInvocation = None,
      geocode = None,
      apiKey = "test-api-key",
      accountId = 133,
      apiName = "/api/3.0/EmailAuthScore",
      UUID = "uuid",
      cachesUsed = None,
      debug = "",
      details = "",
      authScore = 1.0,
      confidence = 1.0,
      reasonCodes = "[R104]",
      hasRiskCode = false,
      customerUserId = None,
      runId = None,
      requestURI = "url/fake/3.0",
      kyc = true,
      ofac = false,
      environmentType = 1,
      internalWorkLogs = Some(internalWorkLogs)
    ))

    whenReady(insertTransaction) { p ⇒
      p.addedNow shouldBe true
      val row = mysqldb.findByTransactionIdWithInternalData("716501a8-2926-497a-8909-************", TransactionFixtures.AllTransactionColumns)
      whenReady(row) {
        p ⇒ {
          p should not be empty
          val transaction = SlickDomainConversion.toApiTransaction(p.get)
          transaction.accountId shouldBe Some(133)
          transaction.apiKey shouldBe Some("test-api-key")
          transaction.transactionId shouldBe "716501a8-2926-497a-8909-************"
          transaction.productName.get shouldBe "EmailAuthScore (KYC)"
          transaction.apiName.get shouldBe "/api/3.0/EmailAuthScore"
          transaction.processingTime.get shouldBe 1235
          transaction.confidence.get shouldBe 1.0
          transaction.internalWorkLogs.get shouldBe internalWorkLogs
        }
      }
    }
  }

  test("insert a transaction and see if it removes duplicate") {
    val start = DateTime.parse("2018-03-20",DateTimeFormat.forPattern("YYYY-MM-dd"))
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    val transaction = SqsApiTransaction(
      id = None,
      transactionId = "716501a8-2926-497a-8909-************",
      transactionDate = DateTime.now().getMillis,
      processingTime = 1235,
      api = "test-api",
      parameters = "{\"nationalid\":1234,\"ofac\":false,\"city\":\"Red Deer\",\"runid\":null,\"driverlicensestate\":null,\"mobilenumber\":\"+14033913967\",\"zip\":\"T4N 2A6\",\"email\":\"<EMAIL>\",\"state\":\"CO\",\"socurekey\":\"716501a8-2926-497a-8909-cd33eea072cf\",\"companyname\":\"So cure\",\"dob\":\"1980-01-01\",\"firstname\":\"Hazel\",\"physicaladdress2\":null,\"physicaladdress\":\"3398 Galts Ave\",\"ipaddress\":null,\"nocache\":null,\"surname\":\"Beck\",\"watchlist\":null,\"country\":\"US\",\"customeruserid\":null,\"userid\":null,\"debug\":true,\"driverlicense\":null,\"datascience\":false,\"geocode\":null,\"forcerefresh\":true,\"isblacklist\":false,\"impersonatorapikey\":null,\"details\":true,\"kyc\":true}",
      response = """{}""",
      error = false,
      errorMsg = None,
      accountIpAddress = "localhost",
      originOfInvocation = None,
      geocode = None,
      apiKey = "test-api-key",
      accountId = 133,
      apiName = "/api/3.0/EmailAuthScore",
      UUID = "uuid",
      cachesUsed = None,
      debug = "",
      details = "",
      authScore = 1.0,
      confidence = 1.0,
      reasonCodes = "[R104]",
      hasRiskCode = false,
      customerUserId = None,
      runId = None,
      requestURI = "url/fake/3.0",
      kyc = true,
      ofac = false,
      environmentType = 1,
      internalWorkLogs = None
    )
    val insertTransactionFuture =mysqlInsertDb.insertAPITransaction(transaction)

    whenReady(insertTransactionFuture) { _ ⇒
      whenReady(mysqlInsertDb.insertAPITransaction(transaction)) { second ⇒
        second.addedNow shouldBe false
        val searchRequest = TransactionSearchRequest(
          accountId = None,
          accountIds = Some(Set(133)),
          start = Some(start),
          end = None,
          environment = None,
          pagination = None,
          product = None,
          isKyc = None,
          isError = None,
          customerUserId = None,
          runId = None,
          columns = TransactionFixtures.AllTransactionColumns,
          startTransactionId = None,
          sorting = Some(Seq(Sort(column = TransactionColumns.TRANSACTION_DATE, ascending = false))),
          maskPii = false
        )
        val rows = mysqldb.searchTransactions(searchRequest)
        whenReady(rows) {
          p ⇒ {
            p should not be empty
            p.length shouldBe 2
            p.head.internalWorkLogs shouldBe None
          }
        }
      }
    }
  }

  test("batch insert transactions") {
    val transactions = Seq(
      SqsApiTransaction(
        id = None,
        transactionId = "1bcd893d-0e42-4727-9cf3-422065288bd1",
        transactionDate = DateTime.now().getMillis,
        processingTime = 1235,
        api = "test-api",
        parameters = "{\"nationalid\":1234,\"ofac\":false,\"city\":\"Red Deer\",\"runid\":null,\"driverlicensestate\":null,\"mobilenumber\":\"+14033913967\",\"zip\":\"T4N 2A6\",\"email\":\"<EMAIL>\",\"state\":\"CO\",\"socurekey\":\"716501a8-2926-497a-8909-cd33eea072cf\",\"companyname\":\"So cure\",\"dob\":\"1980-01-01\",\"firstname\":\"Hazel\",\"physicaladdress2\":null,\"physicaladdress\":\"3398 Galts Ave\",\"ipaddress\":null,\"nocache\":null,\"surname\":\"Beck\",\"watchlist\":null,\"country\":\"US\",\"customeruserid\":null,\"userid\":null,\"debug\":true,\"driverlicense\":null,\"datascience\":false,\"geocode\":null,\"forcerefresh\":true,\"isblacklist\":false,\"impersonatorapikey\":null,\"details\":true,\"kyc\":true}",
        response = """{}""",
        error = false,
        errorMsg = None,
        accountIpAddress = "localhost",
        originOfInvocation = None,
        geocode = None,
        apiKey = "test-api-key",
        accountId = 133,
        apiName = "/api/3.0/EmailAuthScore",
        UUID = "uuid",
        cachesUsed = None,
        debug = "",
        details = "",
        authScore = 1.0,
        confidence = 1.0,
        reasonCodes = "[R104]",
        hasRiskCode = false,
        customerUserId = None,
        runId = None,
        requestURI = "url/fake/3.0",
        kyc = true,
        ofac = false,
        environmentType = 1,
        internalWorkLogs = Some("[{\"message\": \"Test message\",\"level\": \"info\",\"time\": \"2021-11-22 12:20:45.786\"}]")
      ),
      SqsApiTransaction(
        id = None,
        transactionId = "a32a8d07-d7ca-43ce-bf19-51d444etest1",
        transactionDate = DateTime.now().getMillis,
        processingTime = 946,
        api = "REST",
        parameters = "{\"prevUnpaidOrderCount\":null,\"country\":\"us\",\"addresses\":null,\"submissiondate\":null,\"userid\":\"johnsmith1979\",\"consentTimestamp\":null,\"deviceSessionId\":null,\"physicaladdress2\":null,\"orderamount\":null,\"nocache\":0,\"details\":false,\"state\":\"az\",\"runid\":null,\"isConsenting\":null,\"customeruserid\":null,\"vendors\":null,\"zip\":\"85003\",\"orderchannel\":null,\"datascience\":false,\"stepupReason\":null,\"modules\":[\"watchlistpremier\"],\"physicaladdress\":\"3490 coplin avenue\",\"driverlicensestate\":null,\"forcerefresh\":true,\"companyname\":\"Corporation Corp, LLC.\",\"dob\":\"1934-08-22\",\"socurekey\":\"ce2f0e48-d679-4b81-aa55-da81f05302c8\",\"device\":null,\"driverlicense\":null,\"ipaddress\":\"*******\",\"firstname\":\"LOIS\",\"mobilenumber\":\"**********\",\"impersonatorapikey\":null,\"previousReferenceId\":null,\"city\":\"phoenix\",\"payments\":null,\"nationalid\":\"*********\",\"shippingDetails\":null,\"surname\":\"j\",\"accountcreationdate\":null,\"watchlistfilters\":null,\"prevordercount\":0,\"email\":null,\"debug\":true,\"documentuuid\":null,\"lastorderdate\":null,\"userName\":null,\"merchantDetails\":null,\"userConsent\":true,\"geocode\":\"40.755688, -74.045986\",\"fullname\":null}",
        response = """{}""",
        error = false,
        errorMsg = None,
        accountIpAddress = "localhost",
        originOfInvocation = None,
        geocode = None,
        apiKey = "test-api-key-2",
        accountId = 134,
        apiName = "/api/3.0/EmailAuthScore",
        UUID = "uuid",
        cachesUsed = None,
        debug = "",
        details = "",
        authScore = 1.0,
        confidence = 1.0,
        reasonCodes = "[R104]",
        hasRiskCode = false,
        customerUserId = None,
        runId = None,
        requestURI = "url/fake/3.0",
        kyc = true,
        ofac = false,
        environmentType = 1,
        internalWorkLogs = None
      )
    )

    val insertTransactionsFuture = mysqlInsertDb.insertAPITransactions(transactions)
    whenReady(insertTransactionsFuture) { result ⇒
      val (first, second) = (result.head, result.last)
      first.addedNow shouldBe true
      second.addedNow shouldBe true
      val row = mysqldb.findByTransactionIds(Set("1bcd893d-0e42-4727-9cf3-422065288bd1", "a32a8d07-d7ca-43ce-bf19-51d444etest1"), TransactionFixtures.AllTransactionColumns)
      whenReady(row) {
        result ⇒ {
          val (first, second) = (result.head, result.last)
          val firstTransaction = SlickDomainConversion.toApiTransaction(first)
          firstTransaction.accountId shouldBe Some(133)
          firstTransaction.apiKey shouldBe Some("test-api-key")
          firstTransaction.transactionId shouldBe "1bcd893d-0e42-4727-9cf3-422065288bd1"
          firstTransaction.productName.get shouldBe "EmailAuthScore (KYC)"
          firstTransaction.apiName.get shouldBe "/api/3.0/EmailAuthScore"
          firstTransaction.processingTime.get shouldBe 1235
          firstTransaction.confidence.get shouldBe 1.0
          firstTransaction.error.get shouldBe false
          firstTransaction.internalWorkLogs shouldBe None

          val secondTransaction = SlickDomainConversion.toApiTransaction(second)
          secondTransaction.accountId shouldBe Some(134)
          secondTransaction.apiKey shouldBe Some("test-api-key-2")
          secondTransaction.transactionId shouldBe "a32a8d07-d7ca-43ce-bf19-51d444etest1"
          secondTransaction.productName.get shouldBe "EmailAuthScore (KYC)"
          secondTransaction.apiName.get shouldBe "/api/3.0/EmailAuthScore"
          secondTransaction.processingTime.get shouldBe 946
          secondTransaction.confidence.get shouldBe 1.0
          secondTransaction.error.get shouldBe false
          secondTransaction.internalWorkLogs shouldBe None
        }
      }
    }
  }
}
