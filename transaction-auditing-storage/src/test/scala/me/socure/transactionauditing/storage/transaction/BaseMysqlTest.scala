package me.socure.transactionauditing.storage.transaction

import _root_.com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.common.docker.mysqlservice.GlobalDockerMysql
import org.flywaydb.core.Flyway
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, FunSuite, Matchers}

/**
  * Created by joonkang on 6/6/16.
  */
abstract class BaseMysqlTest
    extends FunSuite
    with BeforeAndAfterAll
    with Matchers
    with ScalaFutures {

  System.setProperty("user.timezone", "UTC")

  implicit val defaultPatience: PatienceConfig =
    PatienceConfig(timeout = Span(200, Seconds), interval = Span(5, Millis))
  private val databaseName: String = GlobalDockerMysql.createDb()
  GlobalDockerMysql.createDb(databaseName)

  def prepareDb(): Unit

  lazy val ds: ComboPooledDataSource =
    dataSource(GlobalDockerMysql.mysqlHost, GlobalDockerMysql.mysqlPort)

  def dataSource(host: String, port: Int): ComboPooledDataSource = {
    val endpoint = s"**************************************"
    val connection = new ComboPooledDataSource()
    connection.setJdbcUrl(endpoint)
    connection.setUser("root")
    connection.setPassword("root")
    connection.setDriverClass("com.mysql.cj.jdbc.Driver")
    connection
  }

  override def beforeAll {
    val schemaMigration = new Flyway {
      setLocations("classpath:/db/migration/basemysql")
      setBaselineOnMigrate(true)
      setPlaceholderReplacement(false)
      setDataSource(ds)
    }
    schemaMigration.migrate()
    prepareDb()
  }

  override def afterAll {
    GlobalDockerMysql.dropDb(databaseName)
  }
}
