package me.socure.transactionauditing.storage.service

import me.socure.transactionauditing.common.domain.v2.CustomFilter
import me.socure.transactionauditing.common.transaction.CustomFilterTypes
import me.socure.transactionauditing.storage.service.ApiTransactionTestGenerator.apiTransaction
import org.scalatest.{FunSuite, Matchers}

class ReasonCodeFilterHandlerTest extends FunSuite with Matchers{

  val reasonCodeFilterHandler = new ReasonCodeFilterHandler

  test("should filter on `ALL` filters correctly"){
    reasonCodeFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.All), Set("R701", "I711", "I720"))) shouldBe(true)
    reasonCodeFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.All), Set("I711", "I720"))) shouldBe(true)
    reasonCodeFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.All), Set("I720"))) shouldBe(true)
    reasonCodeFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.All), Set("R186", "I711", "I720"))) shouldBe(false)
    reasonCodeFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.All), Set("R186"))) shouldBe(false)
  }

  test("should filter on `ANY` reasoncode correctly"){
    reasonCodeFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.Any), Set("R701", "I711", "I720"))) shouldBe(true)
    reasonCodeFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.Any), Set("I711", "I720"))) shouldBe(true)
    reasonCodeFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.Any), Set("I720"))) shouldBe(true)
    reasonCodeFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.Any), Set("R186", "I711", "I720"))) shouldBe(true)
    reasonCodeFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.Any), Set("R186", "I196", "I720"))) shouldBe(true)
    reasonCodeFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.Any), Set("R186"))) shouldBe(false)
  }

  test("should filter on ANY reasoncode if the filterType param is not given"){
    reasonCodeFilterHandler.filter(apiTransaction, CustomFilter(None, Set("R701", "I711", "I720"))) shouldBe(true)
    reasonCodeFilterHandler.filter(apiTransaction, CustomFilter(None, Set("I711", "I720"))) shouldBe(true)
    reasonCodeFilterHandler.filter(apiTransaction, CustomFilter(None, Set("I720"))) shouldBe(true)
    reasonCodeFilterHandler.filter(apiTransaction, CustomFilter(None, Set("R186", "I711", "I720"))) shouldBe(true)
    reasonCodeFilterHandler.filter(apiTransaction, CustomFilter(None, Set("R186", "I196", "I720"))) shouldBe(true)
    reasonCodeFilterHandler.filter(apiTransaction, CustomFilter(None, Set("R186"))) shouldBe(false)
  }

}
