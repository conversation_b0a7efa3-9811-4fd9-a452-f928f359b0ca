package me.socure.transactionauditing.storage.transaction

import com.github.tototoshi.slick.{GenericJodaSupport, MySQLJodaSupport}
import me.socure.common.s3.v2.S3AsyncFiles
import me.socure.common.slick.domain.{Pagination, Sort}
import me.socure.constants.EnvironmentConstants
import me.socure.transaction.auditing.encryption.common.AuditDataPii
import me.socure.transaction.auditing.encryption.decryptor.v2.AuditDataDecryptorV2
import me.socure.transactionauditing.common.domain._
import me.socure.transactionauditing.common.domain.dynamodb.DynamoDbAuditTableDetails
import me.socure.transactionauditing.common.json.TrxJsonFormats
import me.socure.transactionauditing.common.product.{ProductNames, ProductVersions, Products}
import me.socure.transactionauditing.common.transaction.TransactionColumns
import me.socure.transactionauditing.common.transaction.TransactionColumns._
import me.socure.transactionauditing.common.util.SlickDomainConversion
import me.socure.transactionauditing.storage.mysql.slick.MysqlSlickApiTransactionUnifiedDao
import me.socure.transactionauditing.storage.util.LongTextColumnsReader
import me.socure.transactionauditstorage.mysqlschema.{ApiTransactionUnified, DaoTblAPITransaction, RequestPropsUnified, ScoringPropsUnified, ApiTransaction => SlickApiTransaction}
import org.flywaydb.core.Flyway
import org.joda.time.DateTime
import org.joda.time.format.DateTimeFormat
import org.json4s.{DefaultFormats, Formats}
import org.json4s.jackson.Serialization
import org.mockito.Matchers._
import org.mockito.Mockito._
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.scalatest.BeforeAndAfter
import slick.driver.MySQLDriver
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.s3.model.S3Object

import scala.concurrent.{Await, ExecutionContext, Future}

class MysqlSlickApiTransactionDaoTest extends BaseMysqlTest with BeforeAndAfter with DaoTblAPITransaction {
  implicit val ec :ExecutionContext = ExecutionContext.global
  private val baseTrxId = 10000000000L
  override val profile = MySQLDriver
  override val jodaSupport: GenericJodaSupport = MySQLJodaSupport
  import profile.api._
  private lazy val db: profile.backend.Database = profile.backend.Database.forDataSource(ds)

  override  def prepareDb(): Unit = {
    val insertMigration= new Flyway()
    insertMigration.setLocations(
      "classpath:/db/migration/mysql",
      "classpath:/db/migration/mysql_transaction"
    )
    insertMigration.setValidateOnMigrate(false)
    insertMigration.setDataSource(ds)
    insertMigration.migrate()
  }

  private def insertEtlTransactions(): Unit = {
    val slickTransactions = ((1 to 10) ++ (20 to 30)).map(_.toLong).map(createEtlSlickTrxUnified(_, decryptionPrefix = ""))
    Await.result(db.run(
      TblApiTransactionUnified.forceInsertAll(slickTransactions)
    ), defaultPatience.timeout)
  }

  private def deleteEtlTransactions(): Unit = {
    val deleteQuery = TblApiTransactionUnified.filter(_.id.inSet(((1 to 10) ++ (20 to 30)).map(_.toLong + baseTrxId).toSet)).delete
    Await.result(db.run(deleteQuery), defaultPatience.timeout)
  }

  val allEnvs = Set(
    EnvironmentConstants.PRODUCTION_ENVIRONMENT,
    EnvironmentConstants.PRODUCTION_ENVIRONMENT
  )

  val allProducts = Set(Products.EmailAuthScore.V3_0, Products.EmailAuthScore.V2_5, Products.EmailAuthScore.V2, Products.AuthScore.V2_5, Products.AuthScore.V2, Products.Watchlist.V1, Products.EncryptedEmailAuthScore.V3)

  private val auditDataDecryptor = mock(classOf[AuditDataDecryptorV2])
  private val s3AsyncFiles = mock(classOf[S3AsyncFiles])
  private val dynamoDbAuditTableDetails = DynamoDbAuditTableDetails(
    tableName = "tbl_api_transaction_dev",
    partitionKey = "transactionId",
    accountIdYearMonthIndexName = "accountIdYearMonth-index",
    customerUserIdAccountIdIndexName = "customerUserIdAccountId-index"
  )
  private val dynamoDbAsyncClient = mock(classOf[DynamoDbAsyncClient])
  lazy val mysqldb: MysqlSlickApiTransactionUnifiedDao = MysqlSlickApiTransactionUnifiedDao(
    ds,
    dynamoDbAuditTableDetails,
    dynamoDbAsyncClient,
    auditDataDecryptor,
    new LongTextColumnsReader("dummy", s3AsyncFiles, 10)
  )

  before {
    reset(auditDataDecryptor)
  }

  test("query by dates") {
    val start = DateTime.parse("2016-06-16",DateTimeFormat.forPattern("YYYY-MM-dd"))
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.listFiles(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(Seq(S3Object.builder().build())))
    when(s3AsyncFiles.downloadToString(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(""))
    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(133L)),
      start = Some(start),
      end = Some(DateTime.now()),
      environment = Some(allEnvs),
      pagination = Some(Pagination(
        page = 1,
        size = 100
      )),
      product = Option(allProducts),
      isKyc = None,
      isError = None,
      customerUserId = None,
      runId = None,
      columns = TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = Some(Seq(Sort(column = TransactionColumns.TRANSACTION_DATE, ascending = false))),
      maskPii = false
    )
    val list =mysqldb.searchTransactions(searchRequest)

    whenReady(list) { p ⇒
      val transactions = p.map(SlickDomainConversion.toApiTransaction)
      transactions.size shouldBe 2
      transactions.head.internalWorkLogs shouldBe None
      transactions(1).internalWorkLogs shouldBe None
      transactions.head.status shouldBe None
      verifySort(transactions) shouldBe true
      verify(auditDataDecryptor).decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))
    }
  }

  test("query by dates with single accountId") {
    val start = DateTime.parse("2016-06-16",DateTimeFormat.forPattern("YYYY-MM-dd"))
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.listFiles(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(Seq(S3Object.builder().build())))
    when(s3AsyncFiles.downloadToString(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(""))
    val searchRequest = TransactionSearchRequest(
      accountId = Some(133),
      accountIds = None,
      start = Some(start),
      end = Some(DateTime.now()),
      environment = Some(allEnvs),
      pagination = Some(Pagination(
        page = 1,
        size = 100
      )),
      product = Option(allProducts),
      isKyc = None,
      isError = None,
      customerUserId = None,
      runId = None,
      columns = TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = Some(Seq(Sort(column = TransactionColumns.TRANSACTION_DATE, ascending = false))),
      maskPii = false
    )
    val list =mysqldb.searchTransactions(searchRequest)

    whenReady(list) { p ⇒
      val transactions = p.map(SlickDomainConversion.toApiTransaction)
      transactions.size shouldBe 2
      transactions.head.status shouldBe None
      verifySort(transactions) shouldBe true
      verify(auditDataDecryptor).decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))
    }
  }

  test("query by dates with less columns requested") {
    val start = DateTime.parse("2016-06-16",DateTimeFormat.forPattern("YYYY-MM-dd"))
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.listFiles(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(Seq(S3Object.builder().build())))
    when(s3AsyncFiles.downloadToString(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(""))
    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(133)),
      start = Some(start),
      end = Some(DateTime.now()),
      Some(allEnvs),
      Some(Pagination(
        page = 1,
        size = 100
      )),
      Some(allProducts),
      isError = None,
      isKyc = None,
      customerUserId = None,
      runId = None,
      columns = Set(
        API,
        API_NAME,
        TRANSACTION_DATE,
        PROCESSING_TIME,
        KYC,
        ERROR,
        PARAMETERS,
        ENVIRONMENT_ID
      ),
      startTransactionId = None,
      sorting = Some(Seq(Sort(column = TransactionColumns.TRANSACTION_DATE, ascending = false))),
      maskPii = false
    )
    val list =mysqldb.searchTransactions(searchRequest)

    whenReady(list) { p ⇒
      val transactions = p.map(SlickDomainConversion.toApiTransaction)
      transactions.size shouldBe 2
      transactions.head.fraudScore shouldBe None
      transactions.head.status shouldBe None
      transactions.head.internalWorkLogs shouldBe None
      verifySort(transactions) shouldBe true
    }

  }

  test("query by dates and environment") {
    val start = DateTime.parse("2016-06-16",DateTimeFormat.forPattern("YYYY-MM-dd"))
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.listFiles(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(Seq(S3Object.builder().build())))
    when(s3AsyncFiles.downloadToString(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(""))
    val searchRequest = TransactionSearchRequest(
      accountId = Some(133),
      accountIds = None,
      start = Some(start),
      end = Some(DateTime.now()),
      Some(Set(EnvironmentConstants.withName("Production"))),
      Some(Pagination(
        page = 1,
        size = 100
      )),
      Some(allProducts),
      isError = None,
      isKyc = None,
      customerUserId = None,
      runId = None,
      columns = TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = Some(Seq(Sort(column = TransactionColumns.TRANSACTION_DATE, ascending = false))),
      maskPii = false
    )
    val list =mysqldb.searchTransactions(searchRequest)
    whenReady(list) { p ⇒
      val transactions = p.map(SlickDomainConversion.toApiTransaction)
      transactions.head.environmentId shouldBe Some(1)
      transactions.head.productName shouldBe Some("EmailAuthScore (KYC)")
      transactions.head.internalWorkLogs shouldBe None
      verify(auditDataDecryptor).decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))
    }
  }

  test("query by dates and multiple accounts") {
    val start = DateTime.parse("2015-01-20",DateTimeFormat.forPattern("YYYY-MM-dd"))
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.listFiles(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(Seq(S3Object.builder().build())))
    when(s3AsyncFiles.downloadToString(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(""))
    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(133, 73)),
      start = Some(start),
      end = Some(DateTime.now()),
      environment = None,
      product = Some(allProducts),
      isError = None,
      isKyc = None,
      customerUserId = None,
      runId = None,
      pagination = None,
      columns = TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = Some(Seq(Sort(column = TransactionColumns.TRANSACTION_DATE, ascending = false))),
      maskPii = false
    )
    val list =mysqldb.searchTransactions(searchRequest)
    whenReady(list) { p ⇒
      val transactions = p.map(SlickDomainConversion.toApiTransaction)
      transactions.forall(tx => Seq(133).contains(tx.accountId.get)) shouldBe true
      transactions.map(_.accountId.get).distinct shouldBe Seq(133)
    }
  }

  test("should prefer the accountIds property over the accountId property") {
    val start = DateTime.parse("2015-01-20",DateTimeFormat.forPattern("YYYY-MM-dd"))
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.listFiles(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(Seq(S3Object.builder().build())))
    when(s3AsyncFiles.downloadToString(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(""))
    val searchRequest = TransactionSearchRequest(
      accountId = Some(73),
      accountIds = Some(Set(133)),
      start = Some(start),
      end = Some(DateTime.now()),
      environment = None,
      product = Some(allProducts),
      isError = None,
      isKyc = None,
      customerUserId = None,
      runId = None,
      pagination = None,
      columns = TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = Some(Seq(Sort(column = TransactionColumns.TRANSACTION_DATE, ascending = false))),
      maskPii = false
    )
    val list =mysqldb.searchTransactions(searchRequest)
    whenReady(list) { p ⇒
      val transactions = p.map(SlickDomainConversion.toApiTransaction)
      transactions.forall(tx => Seq(133).contains(tx.accountId.get)) shouldBe true
      transactions.map(_.accountId.get).distinct shouldBe Seq(133)
    }
  }

  test("query by dates and sandobox") {
    val start = DateTime.parse("2016-06-16",DateTimeFormat.forPattern("YYYY-MM-dd"))
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.listFiles(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(Seq(S3Object.builder().build())))
    when(s3AsyncFiles.downloadToString(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(""))
    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(133)),
      start = Some(start),
      end = Some(DateTime.now()),
      Some(Set(EnvironmentConstants.withName("Development"))),
      Some(Pagination(
        page = 1,
        size = 100
      )),
      Some(allProducts),
      isError = None,
      isKyc = None,
      customerUserId = None,
      runId = None,
      columns = TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = Some(Seq(Sort(column = TransactionColumns.TRANSACTION_DATE, ascending = false))),
      maskPii = false
    )
    val list =mysqldb.searchTransactions(searchRequest)

    whenReady(list) { p ⇒
      val transactions = p.map(SlickDomainConversion.toApiTransaction)
      transactions should have size 1
      verifySort(transactions) shouldBe true
      transactions.head.productName shouldBe Some("EmailAuthScore (KYC)")
      verify(auditDataDecryptor, times(p.size)).decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))
    }
  }

  test("query by dates with error set to true") {
    val start = DateTime.parse("2016-06-16",DateTimeFormat.forPattern("YYYY-MM-dd"))
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.listFiles(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(Seq(S3Object.builder().build())))
    when(s3AsyncFiles.downloadToString(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(""))
    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(133)),
      start = Some(start),
      end = Some(DateTime.now()),
      Some(allEnvs),
      Some(Pagination(
        page = 1,
        size = 100
      )),
      product = Some(allProducts),
      isError = Some(true),
      isKyc = None,
      customerUserId = None,
      runId = None,
      columns = TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = Some(Seq(Sort(column = TransactionColumns.TRANSACTION_DATE, ascending = false))),
      maskPii = false
    )
    val list =mysqldb.searchTransactions(searchRequest)

    whenReady(list) { p ⇒
      val transactions = p.map(SlickDomainConversion.toApiTransaction)
      transactions should have size 1
      val head = transactions.head
      head.transactionId shouldBe "251ef1ba-3dcf-47d7-811b-9ccc734dacae"
      head.fraudScore shouldBe None
      head.error shouldBe Some(true)
      head.apiName shouldBe Some("/api/2.5/EmailAuthScore")
      head.runId shouldBe Some("test_run")
      head.productName shouldBe Some("EmailAuthScore (KYC)")
      head.status shouldBe None
      verifySort(transactions) shouldBe true
      verify(auditDataDecryptor, times(p.size)).decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))
    }
  }

  test("query by dates with kyc set to true") {
    val start = DateTime.parse("2016-06-16",DateTimeFormat.forPattern("YYYY-MM-dd"))
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.listFiles(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(Seq(S3Object.builder().build())))
    when(s3AsyncFiles.downloadToString(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(""))
    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(133)),
      start = Some(start),
      end = Some(DateTime.now()),
      Some(allEnvs),
      Some(Pagination(
        page = 1,
        size = 100
      )),
      product = Some(allProducts),
      isError = None,
      isKyc = Some(true),
      customerUserId = None,
      runId = None,
      columns = TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = Some(Seq(Sort(column = TransactionColumns.TRANSACTION_DATE, ascending = false))),
      maskPii = false
    )
    val list =mysqldb.searchTransactions(searchRequest)

    whenReady(list) { p ⇒
      val transactions = p.map(SlickDomainConversion.toApiTransaction)
      transactions should have size 2
      transactions.forall(_.requestProps.kyc.get == true)
      val head = transactions.head
      verifySort(transactions) shouldBe true
      verify(auditDataDecryptor).decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))
    }
  }

  test("query miss")  {
    val start = DateTime.parse("2016-05-20",DateTimeFormat.forPattern("YYYY-MM-dd"))
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.listFiles(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(Seq(S3Object.builder().build())))
    when(s3AsyncFiles.downloadToString(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(""))
    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(88)),
      start = Some(start),
      end = Some(DateTime.now()),
      Some(allEnvs),
      Some(Pagination(
        page = 1,
        size = 100
      )),
      product = Some(allProducts),
      isError = None,
      isKyc = None,
      customerUserId = None,
      runId = None,
      columns = TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = Some(Seq(Sort(column = TransactionColumns.TRANSACTION_DATE, ascending = false))),
      maskPii = false
    )
    val list =mysqldb.searchTransactions(searchRequest)
    whenReady(list) { p =>
      val transactions = p.map(SlickDomainConversion.toApiTransaction)
      transactions shouldBe empty
      verify(auditDataDecryptor, times(transactions.size)).decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))
    }
  }

  test("query by pagination") {
    val start = DateTime.parse("2016-06-16",DateTimeFormat.forPattern("YYYY-MM-dd"))
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.listFiles(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(Seq(S3Object.builder().build())))
    when(s3AsyncFiles.downloadToString(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(""))
    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(133)),
      start = Some(start),
      end = Some(DateTime.now()),
      Some(allEnvs),
      Some(Pagination(
        page = 2,
        size = 5
      )),
      product = Some(allProducts),
      isError = None,
      isKyc = None,
      customerUserId = None,
      runId = None,
      columns = TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = Some(Seq(Sort(column = TransactionColumns.TRANSACTION_DATE, ascending = false))),
      maskPii = false
    )
    val list =mysqldb.searchTransactions(searchRequest)
    whenReady(list) {p ⇒
      p.size shouldBe 0
      verify(auditDataDecryptor, times(p.size)).decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))
    }
  }
  def verifySort(list:Seq[ApiTransaction]): Boolean = {
    if (list.size==1) true
    else {
      val head = list.head
      val next = list.tail.head
      if (head.transactionDate.get.isBefore(next.transactionDate.get)) false
      else verifySort(list.tail)
    }
  }

  def verifyIdSort(list:Seq[ApiTransaction]): Boolean = {
    if (list.size==1) true
    else {
      val head = list.head
      val next = list.tail.head

      if(head.id > next.id) false
      else verifyIdSort(list.tail)
    }
  }

  test("query by dates and environment - 3.0") {
    val start = DateTime.parse("2017-09-01",DateTimeFormat.forPattern("YYYY-MM-dd"))
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.listFiles(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(Seq(S3Object.builder().build())))
    when(s3AsyncFiles.downloadToString(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(""))
    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(133)),
      start = Some(start),
      end = Some(DateTime.now()),
      Some(Set(EnvironmentConstants.withName("Development"))),
      Some(Pagination(
        page = 1,
        size = 100
      )),
      product = Some(Set(Products.EmailAuthScore.V3_0)),
      isError = None,
      isKyc = None,
      customerUserId = None,
      runId = None,
      columns = TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = Some(Seq(Sort(column = TransactionColumns.TRANSACTION_DATE, ascending = false))),
      maskPii = false
    )
    val list = mysqldb.searchTransactions(searchRequest)

    whenReady(list) { p ⇒
      val transactions = p.map(SlickDomainConversion.toApiTransaction)
      transactions should have size 0
      verify(auditDataDecryptor, times(p.size)).decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))
    }
  }

  test("should get transactions by api types, start transaction id and limit - overflow - more than one api type") {
    testFetchByOffset(
      products =  Some(Products.EmailAuthScore.All ++ Products.AuthScore.All ++ Products.EncryptedEmailAuthScore.All),
      startTransactionId = baseTrxId + 2,
      limit = 8,
      expectedIds = Set(3, 4, 5, 7, 8, 9, 20, 21)
    )
  }

  test("should get transactions by api types, start transaction id and limit - overflow - exactly one api type") {
    testFetchByOffset(
      products = Some(Products.EmailAuthScore.All),
      startTransactionId = baseTrxId + 2,
      limit = 4,
      expectedIds = Set(4, 8, 20, 24)
    )
  }

  test("should get transactions by api types, start transaction id and limit - overflow - no api type") {
    testFetchByOffset(
      products = None,
      startTransactionId = baseTrxId + 8,
      limit = 6,
      expectedIds = Set(8, 20, 23, 24, 27, 28)
    )
  }

  test("should get transactions by api types, start transaction id and limit - underflow - more than one api type") {
    testFetchByOffset(
      products = Some(Products.EmailAuthScore.All ++ Products.AuthScore.All),
      startTransactionId = baseTrxId + 26,
      limit = 8,
      expectedIds = Set(28, 29)
    )
  }

  test("should get transactions by api types, start transaction id and limit - underflow - exactly one api type") {
    testFetchByOffset(
      products = Some(Products.EncryptedEmailAuthScore.All),
      startTransactionId = baseTrxId + 28,
      limit = 8,
      expectedIds = Set()
    )
  }

  test("should get transactions by api types, start transaction id and limit - underflow - no api type") {
    testFetchByOffset(
      products = None,
      startTransactionId = baseTrxId + 26,
      limit = 8,
      expectedIds = Set(27, 28)
    )
  }


  /*
   SEARCH V2 Tests
   */
//TODO Write tests for search v2


  private def testFetchByOffset(
                                 products: Option[Set[Products.Product]],
                                 startTransactionId: Long,
                                 limit: Int,
                                 expectedIds: Set[Long]
                               ): Unit = {
    val expectedSlickTransactions: Set[ApiTransactionUnified] = expectedIds.map(createEtlSlickTrxUnified(_, decryptionPrefix = "dec_"))

    expectedIds.foreach { id =>
      val dbId = baseTrxId + id
      val auditDataPii = AuditDataPii(
        requestUri = Some(s"request_uri_$dbId"),
        parameters = Some(s"""{"parameters_$dbId" : 1}"""),
        details = Some(s"details_$dbId"),
        response = Some(s"response_$dbId"),
        customerUserId = None
      )
      val auditDataPiiDecrypted = AuditDataPii(
        requestUri = Some(s"dec_request_uri_$dbId"),
        parameters = Some(s"""{"dec_parameters_$dbId" : 1}"""),
        details = Some(s"dec_details_$dbId"),
        response = Some(s"dec_response_$dbId"),
        customerUserId = None
      )
    }
    val ids = expectedIds.map(_ + baseTrxId)
    val resultFuture = withElTransactions(mysqldb.searchTransactions(TransactionSearchRequest(
      accountId = None,
      accountIds = Some(ids),
      start = None,
      end = None,
      environment = None,
      pagination = Some(Pagination(1, limit)),
      product = products,
      isKyc = None,
      isError = None,
      customerUserId = None,
      runId = None,
      columns = TransactionFixtures.StatsColumns,
      startTransactionId = Some(startTransactionId),
      sorting = None,
      maskPii = false
    )))

    whenReady(resultFuture) { result =>
      toApiTransactionUnifiedSeq(result).toSet shouldBe expectedSlickTransactions.map(_.copy(transactionDate = None))
      expectedIds.foreach { id =>
        val dbId = baseTrxId + id
        val auditDataPii = AuditDataPii(
          requestUri = Some(s"request_uri_$dbId"),
          parameters = Some(s"""{"parameters_$dbId" : 1}"""),
          details = Some(s"details_$dbId"),
          response = Some(s"response_$dbId"),
          customerUserId = None
        )
      }
    }
  }

  test("should fetch by runId") {
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(133)),
      start = None,
      end = None,
      environment = None,
      product = Some(allProducts),
      isError = None,
      isKyc = None,
      customerUserId = None,
      runId = Some("test_run"),
      pagination = None,
      columns = TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = Some(Seq(Sort(column = TransactionColumns.TRANSACTION_DATE, ascending = false))),
      maskPii = false
    )
    val list =mysqldb.searchTransactions(searchRequest)
    whenReady(list) { p ⇒
      p.length shouldBe 1
      val transactions = p.map(SlickDomainConversion.toApiTransaction)
      transactions.forall(tx => Seq(133).contains(tx.accountId.get)) shouldBe true
      transactions.forall(tx => tx.runId.getOrElse("") == "test_run") shouldBe true
      verify(auditDataDecryptor, times(p.size)).decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))
    }
  }

  test("should count by runId") {
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(133)),
      start = None,
      end = None,
      environment = None,
      product = Some(allProducts),
      isError = None,
      isKyc = None,
      customerUserId = None,
      runId = Some("test_run"),
      pagination = None,
      columns = TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = Some(Seq(Sort(column = TransactionColumns.TRANSACTION_DATE, ascending = false))),
      maskPii = false
    )
    whenReady(mysqldb.countTransactions(searchRequest)) { count ⇒
      count shouldBe 1
    }
  }

  test("count by dates and sandobox") {
    val start = DateTime.parse("2016-06-16",DateTimeFormat.forPattern("YYYY-MM-dd"))
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(133)),
      start = Some(start),
      end = Some(DateTime.now()),
      Some(Set(EnvironmentConstants.withName("Development"))),
      Some(Pagination(
        page = 1,
        size = 100
      )),
      Some(allProducts),
      isError = None,
      isKyc = None,
      customerUserId = None,
      runId = None,
      columns = TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = Some(Seq(Sort(column = TransactionColumns.TRANSACTION_DATE, ascending = false))),
      maskPii = false
    )
    whenReady(mysqldb.countTransactions(searchRequest)) { count ⇒
      count shouldBe 1
    }
  }

  test("query by column id") {
    val start = DateTime.parse("2016-06-16",DateTimeFormat.forPattern("YYYY-MM-dd"))
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(133)),
      start = Some(start),
      end = Some(DateTime.now()),
      Some(Set(EnvironmentConstants.withName("Development"))),
      Some(Pagination(
        page = 1,
        size = 100
      )),
      Some(allProducts),
      isError = None,
      isKyc = None,
      customerUserId = None,
      runId = None,
      columns = TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = Some(Seq(Sort(column = TransactionColumns.ID, ascending = true))),
      maskPii = false
    )
    val list =mysqldb.searchTransactions(searchRequest)

    whenReady(list) { p ⇒
      val transactions = p.map(SlickDomainConversion.toApiTransaction)
      transactions should have size 1
      verifyIdSort(transactions) shouldBe true
      transactions.head.productName shouldBe Some("EmailAuthScore (KYC)")
      verify(auditDataDecryptor, times(p.size)).decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))
    }
  }

  private def createEtlSlickTrxUnified(id: Long, decryptionPrefix: String): ApiTransactionUnified = {
    val dbId = baseTrxId + id
    val possibleProductVersions = Seq(ProductVersions.V2_5, ProductVersions.V3_0)
    val possibleProductNames = ProductNames.values.toSeq
    val productVersion = possibleProductVersions.apply((id % possibleProductVersions.size).toInt)
    val productName = possibleProductNames.apply((id % possibleProductNames.size).toInt)
    new ApiTransactionUnified(
      id = Some(dbId),
      accountId = Some(dbId),
      apiKey = Some(s"api_key_$dbId"),
      transactionId = s"trx_$dbId",
      transactionDate = Some(DateTime.now),
      environmentType = Some(EnvironmentConstants.PRODUCTION_ENVIRONMENT.id),
      apiName = Products.findByNameAndVersion(productName, productVersion).map(_.toString()),
      requestProps = RequestPropsUnified(),
      scoringProps = ScoringPropsUnified()
    )
  }

  private def withElTransactions[T](action: => T): T = {
    insertEtlTransactions()
    try {
      action
    } finally {
      deleteEtlTransactions()
    }
  }

  private def toApiTransactionUnifiedSeq(transactions: Seq[SlickApiTransaction]): Seq[ApiTransactionUnified] = {
    transactions map { transaction =>
      new ApiTransactionUnified(
        id = transaction.id,
        transactionId = transaction.transactionId,
        accountId = transaction.accountId,
        accountIpAddress = transaction.accountIpAddress,
        api = transaction.api,
        apiKey = transaction.apiKey,
        apiName = transaction.apiName,
        error = transaction.error,
        geocode = transaction.geocode,
        transactionDate = None,
        processingTime = transaction.processingTime,
        uuid = transaction.uuid,
        cachesUsed = transaction.cachesUsed,
        customerUserId = transaction.customerUserId,
        environmentType = transaction.environmentType,
        requestProps = RequestPropsUnified(
          originOfInvocation = transaction.originOfInvocation,
          runId = transaction.runId,
          kyc = transaction.kyc,
          ofac = transaction.ofac
        ),
        scoringProps = ScoringPropsUnified(
          authScore = transaction.authScore,
          confidence = transaction.confidence,
          hasRiskCode = transaction.hasRiskCode
        )
      )
    }
  }
}

object Test {

  private implicit val jsonFormats: Formats = TrxJsonFormats.value

  def main(args: Array[String]): Unit = {
    val allProducts = Set(Products.EmailAuthScore.V3_0, Products.EmailAuthScore.V2_5, Products.EmailAuthScore.V2, Products.AuthScore.V2_5, Products.AuthScore.V2, Products.Watchlist.V1, Products.EncryptedEmailAuthScore.V3)
    val start = DateTime.parse("2016-06-16",DateTimeFormat.forPattern("YYYY-MM-dd"))
    val allEnvs = Set(
      EnvironmentConstants.PRODUCTION_ENVIRONMENT,
      EnvironmentConstants.SANDBOX_ENVIRONMENT
    )

    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(133L)),
      start = Some(start),
      end = Some(DateTime.now()),
      environment = Some(allEnvs),
      pagination = Some(Pagination(
        page = 1,
        size = 100
      )),
      product = Option(allProducts),
      isKyc = None,
      isError = None,
      customerUserId = None,
      runId = None,
      columns = TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = Some(Seq(Sort(column = TransactionColumns.TRANSACTION_DATE, ascending = false))),
      maskPii = false
    )

    println(Serialization.write(searchRequest))
  }
}