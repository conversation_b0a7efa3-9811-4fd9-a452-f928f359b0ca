package me.socure.transactionauditing.storage.service

import me.socure.transactionauditing.common.domain.v2.CustomFilter
import me.socure.transactionauditing.common.transaction.CustomFilterTypes
import org.scalatest.{FunSuite, Matchers}
import me.socure.transactionauditing.storage.service.ApiTransactionTestGenerator._

class CustomFilterProcessorTest extends FunSuite with Matchers{


  val modifiedTransaction = apiTransaction.copy(
    requestProps = apiTransaction.requestProps.copy(
      parameters = Some("{\"modules\":[\"kyc\", \"fraud\"],\"fullName\":\"<PERSON>\",\"dob\":\"19830223\"}")
    ),
    transactionId = "98765"
  )
  val transactions = Seq(apiTransaction, modifiedTransaction)

  test("should apply custom filters on a sequence of transaction correctly - no match scenario"){
    val customFilterWithMultipleFilters = Map(
      "modules" -> CustomFilter(Some(CustomFilterTypes.All), Set("watchlistplus")),
      "reasoncodes" -> CustomFilter(Some(CustomFilterTypes.Any), Set("I720"))
    )
    val result = CustomFilterProcessor.applyCustomFilters(transactions, Some(customFilterWithMultipleFilters))
    result.size shouldBe(0)
  }

  test("should apply custom filters on a sequence of transaction correctly - one match scenario"){
    val customFilter = Map(
      "modules" -> CustomFilter(Some(CustomFilterTypes.All), Set("watchlistpremier")),
      "reasoncodes" -> CustomFilter(Some(CustomFilterTypes.Any), Set("I720"))
    )
    val result = CustomFilterProcessor.applyCustomFilters(transactions, Some(customFilter))
    result.size shouldBe(1)
    result.map(_.transactionId) shouldBe(Seq("12345"))
  }

  test("should apply custom filters on a sequence of transaction correctly - two match scenario"){
    val customFilter = Map(
      "modules" -> CustomFilter(Some(CustomFilterTypes.All), Set("kyc")),
      "reasoncodes" -> CustomFilter(Some(CustomFilterTypes.Any), Set("I720"))
    )
    val result = CustomFilterProcessor.applyCustomFilters(transactions, Some(customFilter))
    result.size shouldBe(2)
    result.map(_.transactionId) shouldBe(Seq("12345", "98765"))
  }

  test("should return the transactions as it is if custom filters is None"){
    CustomFilterProcessor.applyCustomFilters(transactions, None) shouldBe(transactions)
  }

  test("should return the transactions as it is if custom filter map is empty"){
    CustomFilterProcessor.applyCustomFilters(transactions, Some(Map.empty)) shouldBe(transactions)
  }



}
