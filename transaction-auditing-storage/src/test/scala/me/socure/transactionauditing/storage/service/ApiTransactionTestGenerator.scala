package me.socure.transactionauditing.storage.service

import me.socure.transactionauditstorage.mysqlschema.{ApiTransaction, RequestProps, ScoringProps}

object ApiTransactionTestGenerator {

  val requestProps = RequestProps(
    originOfInvocation = None,
    parameters = Some("{\"modules\":[\"watchlistpremier\", \"kyc\", \"fraud\"],\"fullName\":\"<PERSON>\",\"dob\":\"********\"}"),
    runId = None,
    requestUri = None,
    kyc = None,
    ofac = None
  )

  val scoringProps = ScoringProps(
    authScore = None,
    confidence = None,
    debug = None,
    details = None,
    reasonCodes = Some("[\"R701\",\"I610\",\"I711\",\"I720\"]"),
    hasRiskCode = None
  )

  val apiTransaction = ApiTransaction(
    id = Some(1),
    transactionId = "12345",
    accountId = Some(1),
    accountIpAddress = None,
    api = None,
    apiKey = None,
    apiName = None,
    error = None,
    errorMsg = None,
    geocode = None,
    response = None,
    transactionDate = None,
    processingTime = None,
    uuid = None,
    cachesUsed = None,
    customerUserId = None,
    environmentType = Some(1L),
    requestProps = requestProps,
    scoringProps = scoringProps,
    internalWorkLogs = None
  )


}
