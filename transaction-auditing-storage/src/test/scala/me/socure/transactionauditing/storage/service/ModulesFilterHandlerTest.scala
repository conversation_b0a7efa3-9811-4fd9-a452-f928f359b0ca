package me.socure.transactionauditing.storage.service

import me.socure.transactionauditing.common.domain.v2.CustomFilter
import me.socure.transactionauditing.common.transaction.CustomFilterTypes
import me.socure.transactionauditing.storage.service.ApiTransactionTestGenerator._
import org.scalatest.{FunSuite, Matchers}

class ModulesFilterHandlerTest extends FunSuite with Matchers{

  val modulesFilterHandler = new ModulesFilterHandler

  test("should filter on `ALL` modules correctly"){
    modulesFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.All), Set("watchlistpremier", "kyc", "fraud"))) shouldBe(true)
    modulesFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.All), Set("watchlistpremier", "kyc"))) shouldBe(true)
    modulesFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.All), Set("fraud"))) shouldBe(true)
    modulesFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.All), Set("fraud", "synthetic"))) shouldBe(false)
    modulesFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.All), Set("watchlistpremier", "addressrisk"))) shouldBe(false)
  }

  test("should filter on `ANY` module correctly"){
    modulesFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.Any), Set("watchlistpremier", "kyc", "fraud"))) shouldBe(true)
    modulesFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.Any), Set("watchlistpremier", "kyc"))) shouldBe(true)
    modulesFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.Any), Set("fraud"))) shouldBe(true)
    modulesFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.Any), Set("fraud", "synthetic"))) shouldBe(true)
    modulesFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.Any), Set("watchlistpremier", "addressrisk"))) shouldBe(true)
    modulesFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.Any), Set("watchlistplus", "addressrisk"))) shouldBe(false)
    modulesFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.Any), Set("documentverification"))) shouldBe(false)
  }

  test("should filter on ANY module if the filterType param is not given"){
    modulesFilterHandler.filter(apiTransaction, CustomFilter(None, Set("watchlistpremier", "kyc", "fraud"))) shouldBe(true)
    modulesFilterHandler.filter(apiTransaction, CustomFilter(None, Set("watchlistpremier", "kyc"))) shouldBe(true)
    modulesFilterHandler.filter(apiTransaction, CustomFilter(None, Set("fraud"))) shouldBe(true)
    modulesFilterHandler.filter(apiTransaction, CustomFilter(None, Set("fraud", "synthetic"))) shouldBe(true)
    modulesFilterHandler.filter(apiTransaction, CustomFilter(None, Set("watchlistpremier", "addressrisk"))) shouldBe(true)
    modulesFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.Any), Set("watchlistplus", "addressrisk"))) shouldBe(false)
    modulesFilterHandler.filter(apiTransaction, CustomFilter(Some(CustomFilterTypes.Any), Set("documentverification"))) shouldBe(false)
  }

}
