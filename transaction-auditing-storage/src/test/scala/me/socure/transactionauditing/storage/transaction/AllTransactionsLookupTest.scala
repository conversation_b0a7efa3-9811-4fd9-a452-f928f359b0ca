package me.socure.transactionauditing.storage.transaction

import com.github.tototoshi.slick.GenericJodaSupport
import me.socure.common.s3.v2.S3AsyncFiles
import me.socure.constants.EnvironmentConstants
import me.socure.transaction.auditing.encryption.common.AuditDataPii
import me.socure.transaction.auditing.encryption.decryptor.v2.AuditDataDecryptorV2
import me.socure.transactionauditing.common.domain.TransactionSearchRequest
import me.socure.transactionauditing.common.domain.dynamodb.DynamoDbAuditTableDetails
import me.socure.transactionauditing.common.product.Products
import me.socure.transactionauditing.common.transaction.TransactionColumns._
import me.socure.transactionauditing.common.util.SlickDomainConversion
import me.socure.transactionauditing.storage.mysql.slick.MysqlSlickApiTransactionUnifiedDao
import me.socure.transactionauditing.storage.util.LongTextColumnsReader
import me.socure.transactionauditstorage.mysqlschema.DaoTblAPITransaction
import org.flywaydb.core.Flyway
import org.joda.time.DateTime
import org.mockito.Matchers._
import org.mockito.Mockito._
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.scalatest.BeforeAndAfter
import slick.driver.{JdbcProfile, MySQLDriver}
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient
import software.amazon.awssdk.services.s3.model.S3Object

import scala.concurrent.{ExecutionContext, Future}

class AllTransactionsLookupTest extends BaseMysqlTest with DaoTblAPITransaction with BeforeAndAfter {
  private val s3AsyncFiles = mock(classOf[S3AsyncFiles])
  private val auditDataDecryptor = mock(classOf[AuditDataDecryptorV2])
  implicit val ec: ExecutionContext = ExecutionContext.global
  private val dynamoDbAuditTableDetails = DynamoDbAuditTableDetails(
    tableName = "tbl_api_transaction_dev",
    partitionKey = "transactionId",
    accountIdYearMonthIndexName = "accountIdYearMonth-index",
    customerUserIdAccountIdIndexName = "customerUserIdAccountId-index",
    maxFetchLimit = 500
  )
  private val dynamoDbAsyncClient = mock(classOf[DynamoDbAsyncClient])
  lazy val transactionUnifiedDao: MysqlSlickApiTransactionUnifiedDao = MysqlSlickApiTransactionUnifiedDao(
    ds,
    dynamoDbAuditTableDetails,
    dynamoDbAsyncClient,
    auditDataDecryptor,
    new LongTextColumnsReader("dummy", s3AsyncFiles, 10)
  )
  override val profile: JdbcProfile = MySQLDriver
  override val jodaSupport: GenericJodaSupport = new GenericJodaSupport(profile)


  override def prepareDb(): Unit = {
    val flyway = new Flyway()
    flyway.setLocations("classpath:/db.migration/mysql")
    flyway.setBaselineOnMigrate(true)
    flyway.setPlaceholderReplacement(false)
    flyway.setDataSource(ds)
    flyway.setValidateOnMigrate(false)
    flyway.migrate()
  }

  before {
    reset(auditDataDecryptor, s3AsyncFiles)
  }

  test("find all tranasctions within date and product") {
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.listFiles(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(Seq(S3Object.builder().build())))
    when(s3AsyncFiles.downloadToString(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(""))
    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(133)),
      start = Some(new DateTime("2015")),
      end = Some(new DateTime("2017-12-14")),
      environment = Some(Set(EnvironmentConstants.PRODUCTION_ENVIRONMENT)),
      pagination = None,
      product = None,
      isKyc = None,
      isError = Some(false),
      customerUserId = None,
      runId = None,
      columns =  TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = None,
      maskPii = false
    )
    val row = transactionUnifiedDao.searchTransactions(searchRequest)
    whenReady(row) {
      p ⇒ {
        p should not be empty
        val transactions = p.map(SlickDomainConversion.toApiTransaction)
        val first = transactions.find(_.transactionId == "9d507e72-2d57-4d3a-a3c0-c9a43a4587f8").get
        first.accountId shouldBe Some(133)
        first.transactionId shouldBe "9d507e72-2d57-4d3a-a3c0-c9a43a4587f8"
        first.productName.get shouldBe "EmailAuthScore (KYC)"
        first.apiName.get shouldBe "/api/2.5/EmailAuthScore"
        first.authScore.get shouldBe 0.0
        first.error.get shouldBe false
        first.apiKey.get.length > 0 shouldBe true
        verify(auditDataDecryptor).decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))
      }
    }
  }

  test("miss a transaction") {
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(133)),
      start = Some(new DateTime("2015")),
      end = Some(new DateTime("2015")),
      environment = Some(Set(EnvironmentConstants.PRODUCTION_ENVIRONMENT)),
      pagination = None,
      product = Some(Set(Products.EmailAuthScore.V3_0)),
      isKyc = None,
      isError = Some(false),
      customerUserId = None,
      runId = None,
      columns =  TransactionFixtures.AllTransactionColumns,
      startTransactionId = None,
      sorting = None,
      maskPii = false
    )

    val row = transactionUnifiedDao.searchTransactions(searchRequest)
    whenReady(row) {
      p ⇒ {
        assert(p.isEmpty)
        p shouldBe empty
        verify(auditDataDecryptor, times(p.size)).decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))
      }
    }
  }

  test("select only basic columns about the transaction") {
    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(133)),
      start = Some(new DateTime("2015")),
      end = Some(new DateTime("2017-12-14")),
      environment = Some(Set(EnvironmentConstants.PRODUCTION_ENVIRONMENT)),
      pagination = None,
      product = Some(Set(Products.EmailAuthScore.V2_5)),
      isKyc = None,
      isError = Some(false),
      customerUserId = None,
      runId = None,
      columns =  Set(
        ACCOUNT_ID,
        API_KEY,
        TRANSACTION_DATE,
        KYC,
        ERROR,
        ENVIRONMENT_ID
      ),
      startTransactionId = None,
      sorting = None,
      maskPii = false
    )
    val row = this.transactionUnifiedDao.searchTransactions(searchRequest)
    whenReady(row) {
      p ⇒ {
        p should not be empty
        val transactions = p.map(SlickDomainConversion.toApiTransaction)
        val first = transactions.find(_.transactionId == "9d507e72-2d57-4d3a-a3c0-c9a43a4587f8").get
        first.accountId shouldBe Some(133)
        first.transactionId shouldBe "9d507e72-2d57-4d3a-a3c0-c9a43a4587f8"
        first.apiResponse shouldBe None
        first.fraudScore shouldBe None
        first.productName shouldBe None
        first.apiName shouldBe None
        first.parameters shouldBe None
        first.authScore shouldBe None
        first.error.get shouldBe false
        first.apiKey.get.length > 0 shouldBe true
        first.environmentId.get shouldBe 1
      }
    }
  }

  test("select only basic columns about the transaction with S3 Read") {
    when(auditDataDecryptor.decrypt(any(classOf[Option[Long]]), any(classOf[Option[String]]), any(classOf[AuditDataPii]))).thenAnswer(new Answer[Future[AuditDataPii]] {
      override def answer(invocation: InvocationOnMock): Future[AuditDataPii] = Future.successful(invocation.getArgumentAt(2, classOf[AuditDataPii]))
    })
    when(s3AsyncFiles.listFiles(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(Seq(S3Object.builder().build())))
    when(s3AsyncFiles.downloadToString(any(classOf[String]), any(classOf[String]))).thenReturn(Future.successful(""))
    val searchRequest = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(133)),
      start = Some(new DateTime("2015")),
      end = Some(new DateTime("2017-12-14")),
      environment = Some(Set(EnvironmentConstants.PRODUCTION_ENVIRONMENT)),
      pagination = None,
      product = Some(Set(Products.EmailAuthScore.V2_5)),
      isKyc = None,
      isError = Some(false),
      customerUserId = None,
      runId = None,
      columns = Set(
        ACCOUNT_ID,
        API_KEY,
        TRANSACTION_DATE,
        KYC,
        ERROR,
        PARAMETERS,
        ENVIRONMENT_ID
      ),
      startTransactionId = None,
      sorting = None,
      maskPii = false
    )
    val row = this.transactionUnifiedDao.searchTransactions(searchRequest)
    whenReady(row) {
      p ⇒ {
        p should not be empty
        val transactions = p.map(SlickDomainConversion.toApiTransaction)
        val first = transactions.find(_.transactionId == "9d507e72-2d57-4d3a-a3c0-c9a43a4587f8").get
        first.accountId shouldBe Some(133)
        first.transactionId shouldBe "9d507e72-2d57-4d3a-a3c0-c9a43a4587f8"
        first.apiResponse shouldBe None
        first.fraudScore shouldBe None
        first.productName shouldBe None
        first.apiName shouldBe None
        first.authScore shouldBe None
        first.error.get shouldBe false
        first.apiKey.get.length > 0 shouldBe true
        first.environmentId.get shouldBe 1
      }
    }
  }

}
