CREATE TABLE if not exists `tbl_api_transaction` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `accountId` bigint(20) DEFAULT NULL,
  `accountIpAddress` varchar(255) DEFAULT NULL,
  `api` varchar(255) DEFAULT NULL,
  `api<PERSON>ey` varchar(255) DEFAULT NULL,
  `apiName` varchar(255) DEFAULT NULL,
  `error` tinyint(1) DEFAULT NULL,
  `geocode` varchar(255) DEFAULT NULL,
  `originOfInvocation` varchar(255) DEFAULT NULL,
  `processingTime` bigint(20) DEFAULT NULL,
  `transactionDate` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `transactionId` varchar(255) DEFAULT NULL,
  `UUID` varchar(255) DEFAULT NULL,
  `cachesUsed` varchar(255) DEFAULT NULL,
  `authScore` double DEFAULT NULL,
  `confidence` double DEFAULT NULL,
  `hasRiskCode` tinyint(1) DEFAULT NULL,
  `customerUserId` varchar(255) DEFAULT NULL,
  `runId` varchar(255) DEFAULT NULL,
  `KYC` bit(1) DEFAULT NULL,
  `OFAC` bit(1) DEFAULT NULL,
  `environment_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`,`transactionDate`),
  KEY `accountId` (`accountId`),
  KEY `accountIpAddress` (`accountIpAddress`),
  KEY `apiKey` (`apiKey`),
  KEY `apiName` (`apiName`),
  KEY `authscore` (`authScore`),
  KEY `cacheUsed` (`cachesUsed`),
  KEY `confidence` (`confidence`),
  KEY `customerUserId` (`customerUserId`),
  KEY `error` (`error`),
  KEY `geocode` (`geocode`),
  KEY `hasRiskCode` (`hasRiskCode`),
  KEY `KYC` (`KYC`),
  KEY `OFAC` (`OFAC`),
  KEY `originOfInvocation` (`originOfInvocation`),
  KEY `runId` (`runId`),
  KEY `transactionDate` (`transactionDate`),
  KEY `transactionId` (`transactionId`),
  KEY `uuid` (`UUID`),
  KEY `accTransEnv` (`accountId`,`transactionDate`,`environment_id`)
) ENGINE=InnoDB AUTO_INCREMENT=********* DEFAULT CHARSET=latin1
PARTITION BY RANGE(YEAR(transactionDate)) (
  PARTITION p_2015 VALUES LESS THAN (2016),
  PARTITION p_2016 VALUES LESS THAN (2017),
  PARTITION p_2017 VALUES LESS THAN (2018),
  PARTITION p_2018 VALUES LESS THAN (2019),
  PARTITION p_2019 VALUES LESS THAN (2020),
  PARTITION p_2020 VALUES LESS THAN (2021),
  PARTITION p_2021 VALUES LESS THAN (2022),
  PARTITION p_2022 VALUES LESS THAN (2023),
  PARTITION p_2023 VALUES LESS THAN (2024),
  PARTITION p_2024 VALUES LESS THAN (2025),
  PARTITION p_2025 VALUES LESS THAN (2026),
  PARTITION p_2026 VALUES LESS THAN (2027),
  PARTITION p_2027 VALUES LESS THAN (2028),
  PARTITION p_2028 VALUES LESS THAN (2029),
  PARTITION p_2029 VALUES LESS THAN (2030),
  PARTITION p_2030 VALUES LESS THAN (2031),
  PARTITION p_2031 VALUES LESS THAN (2032),
  PARTITION p_2032 VALUES LESS THAN (2033),
  PARTITION p_2033 VALUES LESS THAN (2034),
  PARTITION p_2034 VALUES LESS THAN (2035),
  PARTITION p_others VALUES LESS THAN MAXVALUE
);

CREATE TABLE IF NOT EXISTS `tbl_third_party_audit_stats`
(
	`id` bigint auto_increment PRIMARY KEY,
	`accountid` varchar(255) NULL,
	`isCache` bit NULL,
	`processingtime` bigint NULL,
	`serviceid` int NULL,
	`starttime` datetime NULL,
	`transactionid` varchar(255) NULL,
	`request` varchar(255) NULL,
	`response` varchar(255) NULL,
	`request_body` varchar(255) NULL,
	`uuid` varchar(255) NULL,
	`is_error` bit NULL
);

CREATE TABLE IF NOT EXISTS tbl_api_transaction_extn_1
(
	`id` bigint NOT NULL AUTO_INCREMENT,
	`transactionId` varchar(255) NOT NULL,
	`internalWorkLogs` longtext NULL,
	PRIMARY KEY(`id`),
	UNIQUE KEY `transactionId` (`transactionId`(191))
) DEFAULT CHARSET=utf8mb4;

ALTER TABLE tbl_api_transaction AUTO_INCREMENT = **********;