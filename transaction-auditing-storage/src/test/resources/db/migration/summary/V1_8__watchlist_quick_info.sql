insert into tbl_watchlist_info(`accountId`, `transactionId`, `environmentId`, `transactionDate`, `tier`, `watchlistSettings`) values (100, 'trx-id-1', 1, now(), 'tier1', '{"searchId":*********,"dobAndName":true,"dobTolerance":true,"dobMatchLogic":"exact_yyyy_mm","fuzzinessTolerance":0.9,"screeningCategory":["warning","pep","adverse-media","sanctions","fitness-probity"]}');
insert into tbl_watchlist_info(`accountId`, `transactionId`, `environmentId`, `transactionDate`, `tier`, `watchlistSettings`) values (100, 'trx-id-2', 1, now(), 'tier2', '{"searchId":*********,"dobAndName":true,"dobTolerance":true,"dobMatchLogic":"exact_yyyy_mm","fuzzinessTolerance":0.9');
insert into tbl_watchlist_info(`accountId`, `transactionId`, `environmentId`, `transactionDate`, `tier`, `watchlistSettings`) values (100, 'trx-id-3', 1, now(), 'tier1', '{"dobAndName":true,"dobTolerance":true,"dobMatchLogic":"exact_yyyy_mm","fuzzinessTolerance":0.9,"screeningCategory":["warning","pep","adverse-media","sanctions","fitness-probity"]}');
insert into tbl_watchlist_info(`accountId`, `transactionId`, `environmentId`, `transactionDate`, `tier`, `watchlistSettings`) values (100, 'trx-id-4', 1, now(), 'tier1', '{"searchId":*********,"dobAndName":true,"dobMatchLogic":"exact_yyyy_mm","fuzzinessTolerance":0.9,"screeningCategory":["warning","pep","adverse-media","sanctions","fitness-probity"]}');

