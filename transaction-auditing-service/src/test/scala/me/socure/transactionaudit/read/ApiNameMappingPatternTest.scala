package me.socure.transactionaudit.read

import java.util.regex.Pattern

import org.scalatest.{FunSuite, Matchers}

class ApiNameMappingPatternTest extends FunSuite with Matchers {

  val apiMapping = Map(
    Pattern.compile("^/transaction/\\b[0-9a-f]{8}\\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\\b[0-9a-f]{12}\\b$") -> "/transaction",
    Pattern.compile("^/thirdparty/\\b[0-9a-f]{8}\\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\\b[0-9a-f]{12}\\b$") -> "/thirdparty"
  )

  test("Search with Id at transaction servlet") {
    val endpoint = "/transaction/0eb2547b-3bf1-4b82-8855-da5dc81475bb"
    val result = apiMapping.find{
      case (k, _) => k.matcher(endpoint).matches()
    }.map(_._2).getOrElse("")

    result shouldBe "/transaction"
  }

  test("Search with Id at thirdparty servlet") {
    val endpoint = "/thirdparty/0eb2547b-3bf1-4b82-8855-da5dc81475bb"
    val result = apiMapping.find{
      case (k, _) => k.matcher(endpoint).matches()
    }.map(_._2).getOrElse("")

    result shouldBe "/thirdparty"
  }

  test("Invalid uuid should return as it is") {
    val endpoint = "/transaction/0eb2547b-3bf1-4b82-8855-da5dc81475"
    val result = apiMapping.find{
      case (k, _) => k.matcher(endpoint).matches()
    }.map(_._2).getOrElse(endpoint)

    result shouldBe "/transaction/0eb2547b-3bf1-4b82-8855-da5dc81475"
  }

  test("Other endpoints should return as it is") {
    val endpoint = "/thirdparty/search"
    val result = apiMapping.find{
      case (k, _) => k.matcher(endpoint).matches()
    }.map(_._2).getOrElse(endpoint)

    result shouldBe "/thirdparty/search"
  }

}
