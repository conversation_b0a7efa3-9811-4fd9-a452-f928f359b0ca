package me.socure.transactionaudit.read.servlet.search

import com.typesafe.config.Config
import io.swagger.v3.oas.models.OpenAPI
import me.socure.common.hmac.verifier.{HMACHttpVerifier, HmacVerificationRequest}
import me.socure.common.random.Random
import me.socure.common.slick.domain.{Pagination, Sort}
import me.socure.constants.EnvironmentConstants
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.support.timeout.ApiTimeout
import me.socure.transactionaudit.read.common.exception.ExceptionCodes
import me.socure.transactionaudit.read.service.TransactionAuditSearchService
import me.socure.transactionauditing.common.domain.TransactionSearchRequest
import me.socure.transactionauditing.common.domain.fixture.APITransactionValueGenerator
import me.socure.transactionauditing.common.json.TrxJsonFormats
import me.socure.transactionauditing.common.product.Products
import me.socure.transactionauditing.common.transaction.TransactionColumns.TransactionColumn
import me.socure.transactionauditing.common.transaction.{TransactionColumnFixtures, TransactionColumns}
import me.socure.transactionauditing.common.util.SlickDomainConversion
import me.socure.transactionauditing.sqs.model.summary.MonitorExecParameters
import me.socure.transactionauditstorage.mysqlschema.{ApiTransaction => SlickApiTransaction}
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.Formats
import org.json4s.jackson.Serialization
import org.mockito.internal.verification.Times
import org.mockito.{Mockito, Matchers => MMatchers}
import org.scalatest.Matchers
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFreeSpec

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration
import scala.concurrent.{ExecutionContext, Future}

class TransactionAuditSearchServletTest extends ScalatraFreeSpec with Matchers with MockitoSugar {
  private implicit val jsonFormats: Formats = TrxJsonFormats.value
  private implicit val ec: ExecutionContext = ExecutionContext.Implicits.global
  private implicit val apiTimeout: ApiTimeout = ApiTimeout(FiniteDuration(60, TimeUnit.SECONDS))
  private val hmacVerifier = mock[HMACHttpVerifier]
  private val openAPI = mock[OpenAPI]
  private val config = mock[Config]
  private val transactionAuditSearchService = mock[TransactionAuditSearchService]

  private val RequestHeaders: Map[String, String] = Map(
    "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
    "Content-Type" -> "application-json; charset=UTF-8",
    "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
  )

  Mockito
    .doNothing()
    .when(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))

  private val servlet = new TransactionAuditSearchServlet(
    transactionAuditSearchService,
    config,
    apiTimeout,
    hmacVerifier,
    openAPI
  )

  addServlet(servlet, "/*")

  "TransactionAuditSearchServlet" - {
    "should search transactions by products, start transaction id and limit" in {
      val tsr = TransactionSearchRequest(
        accountId = None,
        accountIds = Some(Set(Random.nextLong())),
        start = Some(DateTime.now(DateTimeZone.UTC)),
        end = Some(DateTime.now(DateTimeZone.UTC)),
        environment = Some(EnvironmentConstants.values),
        pagination = Some(Pagination(Random.nextInt(), Random.nextInt())),
        product = Some(Products.EmailAuthScore.All ++ Products.AuthScore.All ++ Products.Watchlist.All),
        isKyc = None,
        isError = Some(Random.nextBoolean()),
        customerUserId = Some(Random.alphaNumeric()),
        runId = None,
        columns = TransactionColumnFixtures.AllTransactionColumns,
        startTransactionId = Some(Random.nextLong()),
        sorting = Some(TransactionColumnFixtures.AllTransactionColumns.map { col =>
          Sort(column = col, ascending = Random.nextBoolean())
        }.toSeq),
        maskPii = false
      )

      val expectedTransactions = (1 to 10).map(_ => APITransactionValueGenerator.aAPITransaction(
        parameters = Some("{\"nationalid\": \"1234\" }")
      )).map(SlickDomainConversion.toSlickTransaction).toSet

      val expectedResponse = Response(status = ResponseStatus.Ok, data = expectedTransactions)

      Mockito
        .when(transactionAuditSearchService.searchTransactions(tsr))
        .thenReturn(Future.successful(Right(expectedTransactions.toSeq)))

      post("/search", toJson(tsr), headers = RequestHeaders) {
        fromJsonCollection(body) shouldBe expectedResponse
        Mockito.verify(transactionAuditSearchService).searchTransactions(tsr)
        Mockito.verify(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))
      }
    }

    "should search transactions by products, start transaction id and limit, and mask PII" in {
      Mockito.reset(hmacVerifier, transactionAuditSearchService)
      val tsr = TransactionSearchRequest(
        accountId = None,
        accountIds = Some(Set(Random.nextLong())),
        start = Some(DateTime.now(DateTimeZone.UTC)),
        end = Some(DateTime.now(DateTimeZone.UTC)),
        environment = Some(EnvironmentConstants.values),
        pagination = Some(Pagination(Random.nextInt(), Random.nextInt())),
        product = Some(Products.EmailAuthScore.All ++ Products.AuthScore.All ++ Products.Watchlist.All),
        isKyc = None,
        isError = Some(Random.nextBoolean()),
        customerUserId = Some(Random.alphaNumeric()),
        runId = None,
        columns = TransactionColumnFixtures.AllTransactionColumns,
        startTransactionId = Some(Random.nextLong()),
        sorting = Some(
          TransactionColumnFixtures.AllTransactionColumns.map { col =>
            Sort(column = col, ascending = Random.nextBoolean())
          }.toSeq
        ),
        maskPii = true
      )

      val daoTransactions = (1 to 10).map(_ => APITransactionValueGenerator.aAPITransaction(
        parameters = Some("{\"nationalid\": \"1234\" }")
      )).map(SlickDomainConversion.toSlickTransaction).toSet
      val expectedTransactions = daoTransactions.map(SlickDomainConversion.maskPii)
      val expectedResponse = Response(
        status = ResponseStatus.Ok,
        data = expectedTransactions
      )

      Mockito
        .when(transactionAuditSearchService.searchTransactions(tsr))
        .thenReturn(Future.successful(Right(expectedTransactions.toSeq)))

      post("/search", toJson(tsr), headers = RequestHeaders) {
        fromJsonCollection(body) shouldBe expectedResponse
        Mockito .verify(transactionAuditSearchService).searchTransactions(tsr)
        Mockito .verify(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))
      }
    }

    "should find one transaction, and mask PII" in {
      testById(maskPii = true)
    }

    "should find one transaction, and not mask PII" in {
      testById(maskPii = false)
    }

    "should find one transaction with internal data, and mask PII" in {
      testByIdWithInternalData(maskPii = true)
    }

    "should find one transaction with internal data, and not mask PII" in {
      testByIdWithInternalData(maskPii = false)
    }

    "should find one transaction, and mask PII - by transaction-id or customer-user-id" in {
      testByIdOrCuid(maskPii = true)
    }

    "should find one transaction, and not mask PII - by transaction-id or customer-user-id" in {
      testByIdOrCuid(maskPii = false)
    }

    "should search transactions without ids" in {
      Mockito.when(config.getInt("transaction-auditing.rest.maxRowFetch")).thenReturn(5)

      val params: Iterable[(String, String)] = Iterable(
        "ids" -> "",
        "columns" -> TransactionColumnFixtures.AllTransactionColumns.mkString(","),
        "maskPii" -> "false"
      )

      post(uri = "/search/ids", params = params, headers = RequestHeaders) {
        val response: Response[ErrorResponse] = Serialization.read[Response[ErrorResponse]](body)
        response.data.code shouldBe ExceptionCodes.InvalidInputFormat.id
      }
    }

    "should search all transactions by ids" in {
      Mockito.when(config.getInt("transaction-auditing.rest.maxRowFetch")).thenReturn(10)

      val transactionIds: Set[String] = randomTransactionIds
      val columns: Set[TransactionColumn] = TransactionColumnFixtures.AllTransactionColumns
      val params: Iterable[(String, String)] = Iterable(
        "ids" -> transactionIds.mkString(","),
        "columns" -> columns.mkString(","),
        "maskPii" -> "false"
      )
      val maskPii = false

      val daoTransaction: SlickApiTransaction = SlickDomainConversion.toSlickTransaction(
        APITransactionValueGenerator.aAPITransaction(
          parameters = Some("{\"nationalid\": \"" + Random.alphaNumeric(10) + "\" }")
        )
      )
      val expectedTransactions = 1 to 5 map { _ =>
        if (maskPii) SlickDomainConversion.maskPii(daoTransaction) else daoTransaction
      }
      val expectedResponse: Response[Seq[SlickApiTransaction]] = Response(
        status = ResponseStatus.Ok,
        data = expectedTransactions
      )
      val excepted: Seq[SlickApiTransaction] = 1 to 5 map { _ => daoTransaction }
      Mockito
        .when(transactionAuditSearchService.searchByTrxIds(transactionIds, columns, maskPii))
        .thenReturn(Future.successful(Right(excepted)))

      post( uri = "/search/ids", params = params, headers = RequestHeaders ) {
        Serialization.read[Response[Seq[SlickApiTransaction]]](body).data.size shouldBe expectedResponse.data.size
        Mockito .verify(transactionAuditSearchService).searchByTrxIds(transactionIds, columns, maskPii)
        Mockito .verify(hmacVerifier, new Times(3)).verify(MMatchers.any(classOf[HmacVerificationRequest]))
      }
    }
  }


  "should search all monitor transactions by ids" in {
    val transactionIds: Set[String] = randomTransactionIds

    val columns: Set[TransactionColumn] = Set(TransactionColumns.ENVIRONMENT_ID, TransactionColumns.ACCOUNT_ID, TransactionColumns.PARAMETERS, TransactionColumns.DEBUG, TransactionColumns.TRANSACTION_DATE, TransactionColumns.API_NAME)
    val params: Iterable[(String, String)] = Iterable(
      "ids" -> transactionIds.mkString(","),
      "columns" -> columns.mkString(","),
      "maskPii" -> "false"
    )
    val maskPii = false

    val daoTransaction: SlickApiTransaction = SlickDomainConversion.toSlickTransaction(
      APITransactionValueGenerator.aAPITransaction(
        parameters = Some("{\"country\":\"us\",\"ipaddress\":null,\"firstname\":\"abu\",\"addresses\":null,\"mobilenumber\":null,\"impersonatorapikey\":null,\"submissiondate\":null,\"city\":null,\"payments\":null,\"userid\":null,\"nationalid\":null,\"deviceSessionId\":null,\"physicaladdress2\":null,\"surname\":\"bakhar\",\"accountcreationdate\":null,\"orderamount\":null,\"watchlistfilters\":{\"categories\":[\"adverseMedia\"],\"matchingCriteria\":{\"nameDob\":false,\"nameFuzzinessTolerance\":0.5,\"dobFuzzinessTolerance\":true},\"limit\":100,\"countries\":[\"gb\"]},\"nocache\":0,\"prevordercount\":0,\"details\":false,\"state\":null,\"runid\":null,\"isConsenting\":null,\"customeruserid\":\"\",\"vendors\":null,\"email\":\"<EMAIL>\",\"zip\":null,\"debug\":false,\"documentuuid\":null,\"orderchannel\":null,\"datascience\":false,\"lastorderdate\":null,\"modules\":[\"watchlistpremier\"],\"physicaladdress\":null,\"driverlicensestate\":null,\"forcerefresh\":true,\"companyname\":null,\"dob\":\"\",\"geocode\":null,\"socurekey\":\"3f4e62a0-6424-4117-a593-baae2aefd43c\",\"fullname\":null,\"device\":null,\"driverlicense\":null}"),
        debug = Some("[{\"rule_codes\":[{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100007\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100007\"},{\"rulecode\":\"scoreComponents.GLOBAL.300003\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"0.0\",\"ruleCodeShort\":\"GLOBAL.300003\"},{\"rulecode\":\"scoreComponents.GLOBAL.100143\",\"score\":\"-0.5\",\"confidence\":\"0.05\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"GLOBAL.100143\"}],\"raw_score\":4.5,\"version_applied\":\"3\",\"correlation_score_info\":{\"name_address\":null,\"name_email\":null,\"name_phone\":null},\"instance_id\":\"i-0556c22fa3fc7ea22\",\"caches_used\":[],\"categorical_values\":[],\"query_plan\":{\"vendors\":[\"COMPLY_WLVAL\"],\"id_plus_models\":{\"correlation_models\":{},\"risk_models\":{}},\"id_plus_features\":{\"requested_features\":[\"Watchlist3_0\"],\"resolved_features\":[\"Watchlist3_0\"],\"outputted_features\":[\"Watchlist3_0\"]},\"scheduler_timeout\":{\"length\":5000,\"unit\":\"MILLISECONDS\"},\"vendor_timeouts\":{\"COMPLY_WLVAL\":{\"length\":5000,\"unit\":\"MILLISECONDS\"}}},\"http_status\":200,\"reason_codes\":[\"R186\"],\"watchlist3_0_settings\":{\"searchId\":34510406,\"dobAndName\":false,\"dobTolerance\":true,\"dobMatchLogic\":\"exactDob\",\"fuzzinessTolerance\":0.5,\"screeningCategory\":[\"adverse-media\",\"warning\",\"pep\",\"fitness-probity\",\"sanctions\"],\"limit\":10,\"country\":[],\"monitoring\":false}}]")
      )
    )

    val expectedTransactions = 1 to 5 map { _ =>
      if (maskPii) SlickDomainConversion.maskPii(daoTransaction) else daoTransaction
    }
    val expectedResponse: Response[Seq[SlickApiTransaction]] = Response(
      status = ResponseStatus.Ok,
      data = expectedTransactions
    )

    val excepted: Seq[SlickApiTransaction] = 1 to 5 map { _ => daoTransaction }
    Mockito
      .when(transactionAuditSearchService.searchByTrxIds(transactionIds, columns, maskPii))
      .thenReturn(Future.successful(Right(excepted)))

    post(uri = "/monitor/search/ids", params = params, headers = RequestHeaders) {
      Serialization.read[Response[List[MonitorExecParameters]]](body).data.size shouldBe expectedResponse.data.size
    }
  }

  private def randomTransactionIds: Set[String] = {
    val transactionIds: Set[String] = (1 to 10 map { _ =>
      Random.alphaNumeric(10)
    }).toSet
    transactionIds
  }

  private def testById(maskPii: Boolean): Unit = {
    Mockito.reset(hmacVerifier, transactionAuditSearchService)
    val columns = TransactionColumnFixtures.AllTransactionColumns

    val daoTransaction: SlickApiTransaction = SlickDomainConversion.toSlickTransaction(APITransactionValueGenerator.aAPITransaction(
      parameters = Some("{\"nationalid\": \"1234\" }")
    )
    )
    val id = Random.alphaNumeric(10)

    val expectedTransactions = if(maskPii) SlickDomainConversion.maskPii(daoTransaction) else daoTransaction
    val expectedResponse = Response(
      status = ResponseStatus.Ok,
      data = expectedTransactions
    )

    Mockito
      .when(transactionAuditSearchService.searchByTrxId(id, columns, maskPii))
      .thenReturn(Future.successful(Right(expectedTransactions)))

    get("/" + id + "?columns=" + columns.mkString(",") + s"&maskPii=$maskPii", headers = RequestHeaders) {
      Serialization.read[Response[SlickApiTransaction]](body) shouldBe expectedResponse
      Mockito .verify(transactionAuditSearchService).searchByTrxId(id, columns, maskPii)
      Mockito .verify(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))
    }
  }

  private def testByIdWithInternalData(maskPii: Boolean): Unit = {
    Mockito.reset(hmacVerifier, transactionAuditSearchService)
    val columns = TransactionColumnFixtures.AllTransactionColumns
    val daoTransaction: SlickApiTransaction = SlickDomainConversion.toSlickTransaction(
      APITransactionValueGenerator.aAPITransaction(
        parameters = Some("{\"nationalid\": \"1234\" }"),
        internalWorkLogs = Some("[{message: \"Test\"}]")
      )
    )
    val id = Random.alphaNumeric(10)
    val expectedTransactions = if(maskPii) SlickDomainConversion.maskPii(daoTransaction) else daoTransaction
    val expectedResponse = Response(
      status = ResponseStatus.Ok,
      data = expectedTransactions
    )

    Mockito
      .when(transactionAuditSearchService.searchByTrxIdWithInternalData(id, columns, maskPii))
      .thenReturn(Future.successful(Right(expectedTransactions)))

    get("/internal/" + id + "?columns=" + columns.mkString(",") + s"&maskPii=$maskPii", headers = RequestHeaders) {
      Serialization.read[Response[SlickApiTransaction]](body) shouldBe expectedResponse
      Mockito .verify(transactionAuditSearchService).searchByTrxIdWithInternalData(id, columns, maskPii)
      Mockito .verify(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))
    }
  }

  private def testByIdOrCuid(maskPii: Boolean): Unit = {
    Mockito.reset(hmacVerifier, transactionAuditSearchService)
    val columns = TransactionColumnFixtures.AllTransactionColumns
    val transactions = (1 to 10).map { i =>
      SlickDomainConversion.toSlickTransaction(APITransactionValueGenerator.aAPITransaction(
        parameters = Some(s"""{"nationalid": "111$i"}""")
      ))
    }

    val id = Random.alphaNumeric(10)
    val accountIds = List.fill(3)(Random.nextLong()).toSet[Long]

    val expectedTransactions = if(maskPii) transactions.map(SlickDomainConversion.maskPii) else transactions
    val expectedResponse = Response(
      status = ResponseStatus.Ok,
      data = expectedTransactions.toSet[SlickApiTransaction]
    )

    Mockito
      .when(transactionAuditSearchService.searchByTrxIdOrCustId(id, accountIds, columns, maskPii))
      .thenReturn(Future.successful(Right(expectedTransactions)))

    post("/search_by_id_or_cuid?id=" + id + "&accountIds=" + accountIds.mkString(",") + "&columns=" + columns.mkString(",") + s"&maskPii=$maskPii", headers = RequestHeaders) {
      fromJsonCollection(body) shouldBe expectedResponse
      Mockito .verify(transactionAuditSearchService).searchByTrxIdOrCustId(id, accountIds, columns, maskPii)
      Mockito .verify(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))
    }
  }

  private def toJson(req: TransactionSearchRequest): String = {
    Serialization.write(req)
  }

  private def fromJsonCollection(s: String): Response[Set[SlickApiTransaction]] = {
    Serialization.read[Response[Set[SlickApiTransaction]]](s)
  }
}
