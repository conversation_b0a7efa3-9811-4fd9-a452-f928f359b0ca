package me.socure.transactionaudit.read.common.config

import com.typesafe.config.ConfigFactory
import org.scalatest.{FunSuite, Matchers}

class HMACConfigFactoryTest extends FunSuite with Matchers {

  test("load config") {
    val config = ConfigFactory.load("test.conf")
    val hmacConfig = HMACConfigFactory.create(config.getConfig("hmac"))
    hmacConfig.timeInterval shouldBe 5
    hmacConfig.ttl shouldBe 5
    hmacConfig.strength shouldBe 512
    hmacConfig.secretKey shouldBe "ENC(uFkhQj790R7bJVxuA9DM/R4loXYHkWblvNfLTWMflvB9slccns5nZQAeYW3HZYCH)"
  }
}
