package me.socure.transactionaudit.read.validator

import me.socure.common.slick.domain.Pagination
import me.socure.transactionauditing.common.domain.v2.TransactionSearchTestObjectGenerator._
import me.socure.transactionaudit.read.validator.TransactionSearchRequestValidator._
import me.socure.transactionauditing.common.domain.v2.CustomFilter
import org.joda.time.DateTime
import org.scalatest.{FunSuite, Matchers}

class TransactionSearchRequestValidatorTest extends FunSuite with Matchers{


  test("should return success if the input object is valid"){
    validate(tsr).isValid shouldBe(true)
  }

  test("should return invalid dates if either of start date or end date is not present"){
    val columnFiltersWithoutEndDate = columnFilters.copy(endDate = None, startDate = Some(DateTime.now()))
    val tsrUpdated = tsr.copy(requestParams = tsr.requestParams.copy(filters = tsr.requestParams.filters.copy(columns = columnFiltersWithoutEndDate)))
    val validationResult = validate(tsrUpdated)
    validationResult.isValid shouldBe(false)

    val columnFiltersWithoutStartDate = columnFilters.copy(startDate = None, endDate = Some(DateTime.now()))
    val tsrUpdated2 = tsr.copy(requestParams = tsr.requestParams.copy(filters = tsr.requestParams.filters.copy(columns = columnFiltersWithoutStartDate)))
    val validationResult2 = validate(tsrUpdated2)
    validationResult2.isValid shouldBe(false)
  }

  test("should return invalid dates order if start date is greater than end date"){
    val columnFiltersInvalid = columnFilters.copy(endDate = Some(DateTime.now().minusHours(1)), startDate = Some(DateTime.now()))
    val tsrUpdated = tsr.copy(requestParams = tsr.requestParams.copy(filters = tsr.requestParams.filters.copy(columns = columnFiltersInvalid)))
    val validationResult = validate(tsrUpdated)
    validationResult.isValid shouldBe(false)
  }

  test("should return invalid pagination size if size > 150"){
    val tsrUpdated = tsr.copy(requestParams = tsr.requestParams.copy(pagination = Some(Pagination(1, 200))))
    val validationResult = validate(tsrUpdated)
    validationResult.isValid shouldBe(false)
  }

  test("should return invalid pagination size if size <= 0"){
    val tsrUpdated = tsr.copy(requestParams = tsr.requestParams.copy(pagination = Some(Pagination(1, 0))))
    val validationResult = validate(tsrUpdated)
    validationResult.isValid shouldBe(false)
  }

  test("should return invalid pagination size if page <= 0"){
    val tsrUpdated = tsr.copy(requestParams = tsr.requestParams.copy(pagination = Some(Pagination(0, 1))))
    val validationResult = validate(tsrUpdated)
    validationResult.isValid shouldBe(false)
  }

  test("should return invalid sorting if sorting is empty array"){
    val tsrUpdated = tsr.copy(requestParams = tsr.requestParams.copy(sorting = Some(Seq.empty)))
    val validationResult = validate(tsrUpdated)
    validationResult.isValid shouldBe(false)
  }

  test("should return error for unsupported custom filters"){
    val customFiltersWithInvalidData = Map("accountIds" -> CustomFilter(values = Set.empty, filterType = None))
    val tsrUpdated = tsr.copy(requestParams = tsr.requestParams.copy(filters = tsr.requestParams.filters.copy(custom = Some(customFiltersWithInvalidData))))
    val validationResult = validate(tsrUpdated)
    validationResult.isValid shouldBe(false)
  }
}
