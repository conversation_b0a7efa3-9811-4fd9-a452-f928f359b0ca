package me.socure.transactionaudit.read.servlet.search.v2

import io.swagger.v3.oas.models.OpenAPI
import me.socure.common.hmac.verifier.{HMACHttpVerifier, HmacVerificationRequest}
import me.socure.model.{ErrorResponse, Response}
import me.socure.support.timeout.ApiTimeout
import me.socure.transactionaudit.read.service.TransactionAuditSearchService
import me.socure.transactionaudit.read.validator.NoAccountIdOrAccountIds
import me.socure.transactionauditing.common.domain.v2.TransactionSearchTestObjectGenerator._
import me.socure.transactionauditing.common.domain.v2.{ResponseOptions, TransactionSearchResponseV2}
import me.socure.transactionauditing.common.json.TrxJsonFormats
import me.socure.transactionauditing.common.util.SlickDomainConversion
import me.socure.transactionauditing.common.util.SlickDomainConversion.maskPiiV2
import org.json4s.Formats
import org.json4s.jackson.Serialization
import org.mockito.{Mockito, Matchers => MMatchers}
import org.scalatest.Matchers
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFreeSpec

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration
import scala.concurrent.{ExecutionContext, Future}

class TransactionAuditSearchServletV2Test extends ScalatraFreeSpec with Matchers with MockitoSugar {
  private implicit val jsonFormats: Formats = TrxJsonFormats.value
  private implicit val ec: ExecutionContext = ExecutionContext.Implicits.global
  private implicit val apiTimeout: ApiTimeout = ApiTimeout(FiniteDuration(60, TimeUnit.SECONDS))
  private val transactionAuditSearchService: TransactionAuditSearchService = mock[TransactionAuditSearchService]
  private val hmacVerifier = mock[HMACHttpVerifier]
  private val openAPI = mock[OpenAPI]

  private val RequestHeaders: Map[String, String] =  Map(
    "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
    "Content-Type" -> "application-json; charset=UTF-8",
    "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
  )

  Mockito
    .doNothing()
    .when(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))

  private val servlet = new TransactionAuditSearchServletV2(
    transactionAuditSearchService,
    apiTimeout,
    hmacVerifier,
    openAPI
  )

  addServlet(servlet, "/v2/transaction/*")

  "TransactionAuditSearchV2" - {
    "should search transactions by products, start transaction id and limit" in {
      Mockito.when(transactionAuditSearchService.searchTransactionsV2(tsr)).thenReturn(Future.successful(Right(trxResponse)))
      post("/v2/transaction/search", Serialization.write(tsr), headers = RequestHeaders) {
        Serialization.read[Response[TransactionSearchResponseV2]](body).data shouldBe trxResponse
        Mockito.verify(transactionAuditSearchService).searchTransactionsV2(tsr)
        Mockito.verify(hmacVerifier, Mockito.atLeastOnce()).verify(MMatchers.any(classOf[HmacVerificationRequest]))
      }
    }

    "should mask PII in response if the maskPII flag is enabled" in {
      val updatedRequest = tsr.copy(responseParams = responseParams.copy(options = Some(ResponseOptions(maskPii = Some(true)))))
      val expected = maskPiiV2(trxResponse)
      Mockito.when(transactionAuditSearchService.searchTransactionsV2(updatedRequest)).thenReturn(Future.successful(Right(expected)))
      post("/v2/transaction/search", Serialization.write(updatedRequest), headers = RequestHeaders) {
        Serialization.read[Response[TransactionSearchResponseV2]](body).data shouldBe SlickDomainConversion.maskPiiV2(trxResponse)
        Mockito.verify(transactionAuditSearchService).searchTransactionsV2(updatedRequest)
        Mockito.verify(hmacVerifier, Mockito.atLeastOnce()).verify(MMatchers.any(classOf[HmacVerificationRequest]))
      }
    }

    "should throw validation error for invalid input params" in {
      val updatedRequest = tsr.copy(requestParams = requestParams.copy(filters = filters.copy(columns = columnFilters.copy(accountId = None, accountIds = None))))
      post("/v2/transaction/search", Serialization.write(updatedRequest), headers = RequestHeaders) {
        Serialization.read[Response[ErrorResponse]](body).data.message shouldBe(NoAccountIdOrAccountIds.errorMessage)
        Mockito.verify(hmacVerifier, Mockito.atLeastOnce()).verify(MMatchers.any(classOf[HmacVerificationRequest]))
      }
    }
  }
}
