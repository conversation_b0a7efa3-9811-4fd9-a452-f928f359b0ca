package me.socure.transactionaudit.read.common.exception

/**
  * Created by <PERSON><PERSON><PERSON> on 5/10/16.
  */
object ExceptionCodes extends Enumeration {
  type ExceptionCodes = EnumVal

  class EnumVal(val id : Int, val code : String, val description : String) extends Value


  //Request Handling
  val InvalidInputFormat = new EnumVal(115, "InvalidInputFormat", "Invalid input format exception")
  val NoHandlerFound = new EnumVal(118, "NotFound", "No handler found")
  val InternalError = new EnumVal(119, "InternalError", "Internal Error")
  val MissingRequiredParameters = new EnumVal(125, "MissingRequiredParameters", "Missing required parameters")
}
