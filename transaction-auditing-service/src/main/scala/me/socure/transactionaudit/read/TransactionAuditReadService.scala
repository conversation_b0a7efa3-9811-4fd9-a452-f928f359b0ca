package me.socure.transactionaudit.read

import java.util.regex.Pattern

import com.google.inject.Inject
import com.google.inject.name.Named
import javax.servlet.DispatcherType
import me.socure.common.check.servlet.api.CheckerServlet
import me.socure.common.jetty.AbstractJettyService
import me.socure.common.metrics.JavaMetricsFactory
import me.socure.common.metrics.models.MetricTags
import me.socure.common.servlet.metrics.ServletMetricsFilter
import me.socure.support.timeout.ApiTimeout
import me.socure.transactionaudit.read.servlet.healthcheck.TransactionAuditReadHealthCheckServlet
import me.socure.transactionaudit.read.servlet.openapi.TransactionAuditReadOpenAPIServlet
import me.socure.transactionaudit.read.servlet.search.TransactionAuditSearchServlet
import org.eclipse.jetty.servlet.{FilterHolder, ServletHolder}
import org.eclipse.jetty.util.thread.ExecutorThreadPool
import java.util.{EnumSet => JEnumSet}

import me.socure.transactionaudit.read.servlet.search.v2.TransactionAuditSearchServletV2

import scala.concurrent.ExecutionContext

class TransactionAuditReadService @Inject()(@Named("JettyExecutorThreadPool") threadPool: ExecutorThreadPool,
                                            @Named("appPort") port: Int,
                                            apiTimeout: ApiTimeout,
                                            transactionAuditSearchServlet: TransactionAuditSearchServlet,
                                            transactionAuditSearchServletV2: TransactionAuditSearchServletV2,
                                            openAPIServlet: TransactionAuditReadOpenAPIServlet,
                                            healthCheckServlet: TransactionAuditReadHealthCheckServlet,
                                            @Named("smokeTestServlet") smokeTestServlet: CheckerServlet,
                                            serverMetricsEnabled: Boolean = true
                                           )
                                           (implicit ec: ExecutionContext)
  extends AbstractJettyService(
    threadPool = threadPool,
    port = port,
    apiTimeout = Some(apiTimeout.value)
  ) {

  override def beforeStartUp(): Unit = {
    if(serverMetricsEnabled) {
      val servletMetricsFilter = new ServletMetricsFilter(
        metrics = JavaMetricsFactory.get(MetricTags.httpMetricPrefix),
        baseTags = MetricTags(serviceName = Some("transaction-auditing-service")),
        apiNameMapping = Map(
          Pattern.compile("^/transaction/\\b[0-9a-f]{8}\\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\\b[0-9a-f]{12}\\b$") -> "/transaction",
          Pattern.compile("^/thirdparty/\\b[0-9a-f]{8}\\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\\b[0-9a-f]{12}\\b$") -> "/thirdparty",
          Pattern.compile("^/transaction/internal/\\b[0-9a-f]{8}\\b-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-\\b[0-9a-f]{12}\\b$") -> "/transaction/internal"
        )
      )
      val dispatchTypes = JEnumSet.of(
        DispatcherType.REQUEST,
        DispatcherType.FORWARD,
        DispatcherType.ASYNC,
        DispatcherType.ERROR,
        DispatcherType.INCLUDE
      )
      getHandler.addFilter(new FilterHolder(servletMetricsFilter), "/*", dispatchTypes)
    }
    val transactionSearchServletHolder = new ServletHolder(transactionAuditSearchServlet)
    val transactionSearchServletV2Holder = new ServletHolder(transactionAuditSearchServletV2)
    val openAPIServletHolder = new ServletHolder(openAPIServlet)
    val healthCheckServletHolder = new ServletHolder(healthCheckServlet)
    val smokeTestServletHolder = new ServletHolder(smokeTestServlet)

    getHandler.addServlet(transactionSearchServletHolder, "/transaction/*")
    getHandler.addServlet(transactionSearchServletV2Holder, "/v2/transaction/*")
    getHandler.addServlet(openAPIServletHolder, "/spec/*")
    getHandler.addServlet(healthCheckServletHolder, "/healthcheck/*")
    getHandler.addServlet(smokeTestServletHolder, "/smoke-tests/*")
  }

}