package me.socure.transactionaudit.read.servlet.healthcheck

import com.google.inject.Inject
import me.socure.common.healthcheck.checker.HealthCheckProcessorBasedHealthChecker
import me.socure.common.healthcheck.servlet.HealthCheckServlet

import scala.concurrent.ExecutionContext

class TransactionAuditReadHealthCheckServlet @Inject()(healthChecker: HealthCheckProcessorBasedHealthChecker)
                                                      (implicit ec: ExecutionContext)
  extends HealthCheckServlet(checker = healthChecker) {}
