package me.socure.transactionaudit.read.module.datasource

import com.google.inject.{AbstractModule, Provides, Singleton}
import com.typesafe.config.Config
import me.socure.common.environment.{AppRegion, AppRegionResolver}
import me.socure.common.executorservice.MetricsThreadPoolExecutor
import me.socure.common.metrics.JavaMetricsFactory
import me.socure.common.rds.impl.HikariDataSourceProvider
import me.socure.common.s3.v2.S3AsyncFiles
import me.socure.common.s3.v2.factory.S3AsyncClientFactory
import me.socure.transaction.auditing.encryption.decryptor.v2.{AuditDataDecryptorV2, AuditDataDecryptorV2Factory}
import me.socure.transactionaudit.read.service.TransactionAuditSearchService
import me.socure.transactionauditing.common.domain.dynamodb.DynamoDbAuditTableDetails
import me.socure.transactionauditing.storage.factory.TransactionDaoFactory
import me.socure.transactionauditing.storage.mysql.slick.MysqlSlickApiTransactionUnifiedDao
import me.socure.transactionauditing.storage.util.LongTextColumnsReader
import software.amazon.awssdk.http.nio.netty.NettyNioAsyncHttpClient
import software.amazon.awssdk.services.dynamodb.DynamoDbAsyncClient

import java.net.URI
import javax.inject.Named
import javax.sql.DataSource
import scala.concurrent.ExecutionContext
import scala.util.Try

class TransactionAuditReadDataSourceModule extends AbstractModule {

  @Provides
  @Singleton
  def appRegion(): AppRegion = AppRegionResolver.resolve()



  @Provides
  @Named("apiTransactionUnifiedDao")
  @Singleton
  def apiTransactionUnifiedDao(@Named("auditClusterUnifiedDS") dataSource: DataSource,
                               dataDecryptor: AuditDataDecryptorV2, longTextColumnsReader: LongTextColumnsReader,
                               @Named("dynamoDbAsyncClient") dynamoDbAsyncClient: DynamoDbAsyncClient,
                               dynamoDbTableDetails: DynamoDbAuditTableDetails
                              )
                              (implicit ec: ExecutionContext): MysqlSlickApiTransactionUnifiedDao = {
    TransactionDaoFactory.createUnified(dataSource, dataDecryptor, longTextColumnsReader, dynamoDbTableDetails, dynamoDbAsyncClient)
  }

  @Provides
  @Named("dynamoDbAsyncClient")
  @Singleton
  def dynamoDbAsyncClient(config: Config): DynamoDbAsyncClient = {
    val endpointOpt: Option[String] = if(config.hasPath("dynamodb.endpoint")) Some(config.getString("dynamodb.endpoint")) else None
    val maxConcurrency = config.getInt("dynamodb.client.config.maxConcurrency")
    val readTimeout = config.getInt("dynamodb.client.config.readTimeout")
    val asyncHttpClient = NettyNioAsyncHttpClient.builder()
      .maxConcurrency(maxConcurrency)
      .readTimeout(java.time.Duration.ofSeconds(readTimeout))
      .build()
    endpointOpt match {
      case Some(endpoint) =>
        DynamoDbAsyncClient.builder()
          .endpointOverride(URI.create(endpoint))
          .httpClient(asyncHttpClient)
          .build()
      case _ => DynamoDbAsyncClient.builder().httpClient(asyncHttpClient).build()
    }
  }

  @Provides
  @Singleton
  def dynamoDbTableDetails(config: Config): DynamoDbAuditTableDetails = {
    DynamoDbAuditTableDetails(
      tableName = config.getString("dynamodb.table.name"),
      partitionKey = config.getString("dynamodb.table.partition.key"),
      accountIdYearMonthIndexName = config.getString("dynamodb.table.gsi.accountIdYearMonth.index.name"),
      customerUserIdAccountIdIndexName = config.getString("dynamodb.table.gsi.customerUserIdAccountId.index.name"),
      maxFetchLimit = config.getInt("dynamodb.max.records.fetch.limit")
    )
  }

  @Provides
  @Singleton
  @Named("dynamoDbEnabled")
  def dynamoDbEnabled(config: Config): Boolean = config.hasPath("dynamodb.enabled") && config.getBoolean("dynamodb.enabled")

  @Provides
  @Singleton
  def getLongTextColumnsReader(config: Config): LongTextColumnsReader = {
    val totalWorkers = if (config.hasPath("longtext.column.reader.num.threads")) {
      config.getInt("longtext.column.reader.num.threads")
    } else 30
    val batchSize = if (config.hasPath("longtext.column.reader.batch.size")) {
      config.getInt("longtext.column.reader.batch.size")
    } else 20
    implicit val ec = ExecutionContext.fromExecutor(new MetricsThreadPoolExecutor(totalWorkers, JavaMetricsFactory.get("ta.long.text.column.reader")))
    val dataBucketName = if (config.hasPath("transaction.audit.bucket.name")) config.getString("transaction.audit.bucket.name") else ""
    val s3AsyncFiles = new S3AsyncFiles(S3AsyncClientFactory.get())
    new LongTextColumnsReader(dataBucketName, s3AsyncFiles, batchSize)
  }

  @Provides
  @Named("auditClusterUnifiedDS")
  @Singleton
  def auditClusterUnifiedDataSource(config: Config): DataSource = {
    new HikariDataSourceProvider(config.getConfig("database.audit.unified-cluster")).provide()
  }

  @Provides
  @Singleton
  def dataDecryptor(config: Config, appRegion: AppRegion)
                   (implicit ec: ExecutionContext): AuditDataDecryptorV2 = {
    AuditDataDecryptorV2Factory.create(config = config, appRegion = appRegion)
  }



  @Provides
  @Singleton
  def getTransactionAuditSearchService(
                                        @Named("apiTransactionUnifiedDao") apiTransactionUnifiedDao: MysqlSlickApiTransactionUnifiedDao,
                                        @Named("dynamoDbEnabled") dynamoDbEnabled: Boolean,
                                        dynamoDbAuditTableDetails: DynamoDbAuditTableDetails
                                      )(implicit ec: ExecutionContext): TransactionAuditSearchService = {
    new TransactionAuditSearchService(apiTransactionUnifiedDao, dynamoDbEnabled, dynamoDbAuditTableDetails)
  }
}
