package me.socure.transactionaudit.read.validator


import cats.data.Validated.{Invalid, Valid}
import cats.data.{NonEmptyList, Validated, ValidatedNel}
import cats.implicits._
import me.socure.common.slick.domain.{Pagination, Sort}
import me.socure.transactionauditing.common.domain.TransactionSearchRequestDynamo
import me.socure.transactionauditing.common.domain.v2.Constants._
import me.socure.transactionauditing.common.domain.v2._
import me.socure.transactionauditing.common.transaction.TransactionColumns.TransactionColumn

object TransactionSearchRequestValidator {

  type ValidationResult[A] = ValidatedNel[SearchRequestValidation, A]

  def validate(tsr: TransactionSearchRequestV2): ValidationResult[TransactionSearchRequestV2] = {
    val requestParams = tsr.requestParams
    val responseParams = tsr.responseParams
    (
      validateRequestParams(requestParams),
      validateResponseParams(responseParams)
    ).mapN(TransactionSearchRequestV2)
  }

  private def validateRequestParams(requestParams: RequestParams): ValidationResult[RequestParams] = {
    (
      validatePagination(requestParams.pagination),
      validateSorting(requestParams.sorting),
      validateFilters(requestParams.filters)
      ).mapN(RequestParams)
  }

  private def validateExclusiveStartKey(exclusiveStartKey: Option[Map[String, String]]): ValidationResult[Option[Map[String, String]]] = {
    exclusiveStartKey match {
      case Some(startKey) =>
        val keys = Set("accountIdYearMonth", "transactionDateTime", "transactionId")
        if(keys.forall(startKey.contains)) exclusiveStartKey.validNel else InvalidExclusiveStartKey.invalidNel
      case _ =>
        exclusiveStartKey.validNel
    }
  }

  def validate(transactionSearchRequest: TransactionSearchRequestDynamo, maxFetchLimit: Int): Validated[Seq[NonEmptyList[SearchRequestValidation]], None.type] = {
    val pageSizeValidation = validatePageSize(transactionSearchRequest.pageSize, maxFetchLimit)
    val startKeyValidation = validateExclusiveStartKey(transactionSearchRequest.exclusiveStartKey)

    (pageSizeValidation, startKeyValidation) match {
      case (Valid(_), Valid(_)) => Valid(None)
      case (Invalid(errors1), Invalid(errors2)) => Invalid(Seq(errors1, errors2))
      case (Invalid(errors), Valid(_)) => Invalid(Seq(errors))
      case (Valid(_), Invalid(errors)) => Invalid(Seq(errors))
    }
  }

  private def validatePageSize(pageSize: Option[Int], maxFetchLimit: Int): ValidationResult[Option[Int]] = {
    pageSize match {
      case Some(size) =>
        if (size <= maxFetchLimit) pageSize.validNel else InvalidPageSize.invalidNel
      case _ => pageSize.validNel
    }
  }

  private def validateResponseParams(responseParams: ResponseParams): ValidationResult[ResponseParams] = {
    (
      validateResponseColumns(responseParams.columns),
      validateResponseOptions(responseParams.options)
    ).mapN(ResponseParams)
  }

  private def validateResponseColumns(responseColumns: Set[TransactionColumn]): ValidationResult[Set[TransactionColumn]] = {
    responseColumns.validNel
  }

  private def validateResponseOptions(responseOptions: Option[ResponseOptions]): ValidationResult[Option[ResponseOptions]] = {
    responseOptions.validNel
  }

  private def validatePagination(paginationOpt: Option[Pagination]): ValidationResult[Option[Pagination]] = {
    validateOptionalParam(paginationOpt, InvalidPagination)(pagination => {
      !(pagination.size > MaxPaginationSize || pagination.size <= 0 || pagination.page <= 0)
    })
  }

  private def validateSorting(sortingOpt: Option[Seq[Sort[TransactionColumn]]]): ValidationResult[Option[Seq[Sort[TransactionColumn]]]] = {
    validateOptionalParam(sortingOpt, InvalidSorting)(sorting =>{
      sorting.nonEmpty
    })
  }

  private def validateFilters(filters: RequestFilters): ValidationResult[RequestFilters] = {
    (
      validateColumnFilters(filters.columns),
      validateCustomFilters(filters.custom)
    ).mapN(RequestFilters)
  }

  private def validateColumnFilters(columnFilters: ColumnFilters): ValidationResult[ColumnFilters] = {

    if(columnFilters.accountId.isEmpty && (columnFilters.accountIds.isEmpty || columnFilters.accountIds.get.isEmpty)){
      NoAccountIdOrAccountIds.invalidNel
    }else{
      val startDate = columnFilters.startDate
      val endDate = columnFilters.endDate
        if((startDate.isDefined && endDate.isDefined) || (startDate.isEmpty && endDate.isEmpty)){
        if(startDate.isEmpty || (startDate.isDefined && startDate.get.isBefore(endDate.get))){
          columnFilters.validNel
        }else {
          InvalidDateOrder.invalidNel
        }
      }else {
        InvalidStartAndEndDates.invalidNel
      }
    }
  }

  private def validateCustomFilters(customFiltersOpt: Option[Map[String, CustomFilter]]): ValidationResult[Option[Map[String, CustomFilter]]] = {
    validateOptionalParam(customFiltersOpt, InvalidCustomFilter)(customFilters => {
      customFilters.forall((entry) => {
        val customFilterName = entry._1
        SupportedCustomFilters.contains(customFilterName)
      })
    })
  }

  private def validateOptionalParam[A](paramOpt: Option[A], validationError: SearchRequestValidation)
                                      (validationFunc: (A) => Boolean): ValidationResult[Option[A]] = {
    paramOpt match {
      case None => paramOpt.validNel
      case Some(param) =>
        validationFunc(param) match {
          case true => paramOpt.validNel
          case false => validationError.invalidNel
        }
    }
  }

}
