package me.socure.transactionaudit.read.validator


sealed trait SearchRequestValidation {
  def errorMessage: String
}

case object NoAccountIdOrAccountIds extends SearchRequestValidation{
  override def errorMessage: String = "accountId OR accountIds param is mandatory"
}

case object NoAccountId extends SearchRequestValidation{
  override def errorMessage: String = "accountId param is mandatory"
}

case object InvalidStartAndEndDates extends SearchRequestValidation{
  override def errorMessage: String = "Start Date and End date should be both present or both empty"
}

case object StartAndEndDatesMandatory extends SearchRequestValidation{
  override def errorMessage: String = "Start Date and End date should be both present"
}

case object InvalidExclusiveStartKey extends SearchRequestValidation {
  override def errorMessage: String = "Exclusive start key should contain accountIdYearMonth, transactionDateTime and transactionId"
}

case object InvalidDateOrder extends SearchRequestValidation{
  override def errorMessage: String = "End Date is before start date"
}

case object InvalidEnvironment extends SearchRequestValidation{
  override def errorMessage: String = "Environment is invalid"
}

case object InvalidPagination extends SearchRequestValidation{
  override def errorMessage: String = "Pagination is invalid"
}

case object InvalidPageSize extends SearchRequestValidation{
  override def errorMessage: String = "Page size should be less than 700"
}

case object InvalidSorting extends SearchRequestValidation{
  override def errorMessage: String = "Sorting is invalid"
}

case object InvalidColumns extends SearchRequestValidation{
  override def errorMessage: String = "Response columns are invalid"
}

case object InvalidStartRecordId extends SearchRequestValidation{
  override def errorMessage: String = "StartRecordId is invalid"
}

case object InvalidMaskPII extends SearchRequestValidation{
  override def errorMessage: String = "Mask PII is invalid"
}

case object InvalidHasRiskCode extends SearchRequestValidation{
  override def errorMessage: String = "hasRiskCode is invalid"
}

case object InvalidModules extends SearchRequestValidation{
  override def errorMessage: String = "Modules are invalid"
}

case object InvalidCustomFilter extends SearchRequestValidation{
  override def errorMessage: String = "Custom Filters are invalid"
}
