package me.socure.transactionaudit.read.common.errorhandler

import me.socure.model.{Response, ResponseStatus}
import me.socure.transactionaudit.read.common.exception.ExceptionCodes._
import org.json4s.ext.EnumNameSerializer
import org.json4s.{DefaultFormats, Formats, MappingException}
import org.scalatra.ScalatraServlet
import org.scalatra.json.JacksonJsonSupport
import org.slf4j.LoggerFactory

trait JsonErrorHandling extends {
  this: ScalatraServlet with JacksonJsonSupport =>

  private val logger = LoggerFactory.getLogger(getClass)

  override protected implicit def jsonFormats: Formats = DefaultFormats ++ Seq(new EnumNameSerializer(ResponseStatus))

  before() {
    contentType = formats("json")
  }

  notFound {
    status = 404
    logger.error(s"No handler found for ${request.getRequestURI}")
    Response(
      status = ResponseStatus.Error,
      data = ErrorResponseFactory.get(NoHandlerFound)
    )
  }

  error {
    case e: MappingException =>
      status = 400
      logger.error(s"Invalid input for URI ${request.getRequestURI}", e)
      Response(
        status = ResponseStatus.Error,
        data = ErrorResponseFactory.get(InvalidInputFormat)
      )
    case e : NoSuchElementException =>
      status = 400
      logger.error(s"Missing parameters for URI ${request.getRequestURI}", e)
      Response(ResponseStatus.Error, ErrorResponseFactory.get(MissingRequiredParameters))
    case e: Throwable =>
      status = 500
      logger.error(s"Unhandled exception while handling ${request.getRequestURI}", e)
      Response(
        status = ResponseStatus.Error,
        data = ErrorResponseFactory.get(InternalError)
      )
  }
}
