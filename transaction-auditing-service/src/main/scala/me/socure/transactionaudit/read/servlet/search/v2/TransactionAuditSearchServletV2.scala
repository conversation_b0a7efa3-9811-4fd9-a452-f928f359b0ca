package me.socure.transactionaudit.read.servlet.search.v2

import cats.data.Validated.{Invalid, Valid}
import com.google.inject.Inject
import io.swagger.v3.oas.models.OpenAPI
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.openapi3.scalatra.OpenApiScalatraSupport
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.model.ErrorResponse
import me.socure.support.timeout.{ApiTimeout, FutureWithTimeoutSupport}
import me.socure.transactionaudit.read.common.errorhandler.JsonErrorHandling
import me.socure.transactionaudit.read.service.TransactionAuditSearchService
import me.socure.transactionaudit.read.spec.TransactionAuditSearchSpec
import me.socure.transactionaudit.read.validator.TransactionSearchRequestValidator
import me.socure.transactionauditing.common.domain.v2.TransactionSearchRequestV2
import me.socure.transactionauditing.common.json.TrxJsonFormats
import org.json4s.Formats
import org.scalatra.ScalatraServlet
import org.scalatra.json.JacksonJsonSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

/**
 * Created by sumitkumar on 18/03/22
 **/
class TransactionAuditSearchServletV2 @Inject()(
                                                 val transactionAuditSearchService: TransactionAuditSearchService,
                                                 val apiTimeout: ApiTimeout,
                                                 val hmacVerifier: HMACHttpVerifier,
                                                 val openApi: OpenAPI
                                               )
                                                (implicit val executor : ExecutionContext)
  extends ScalatraServlet with FutureWithTimeoutSupport with JacksonJsonSupport
    with JsonErrorHandling with AuthenticationSupport with OpenApiScalatraSupport  {

  override val logger: Logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get("transaction.auditing.v2" + getClass.getSimpleName)

  override protected implicit def jsonFormats: Formats = TrxJsonFormats.value

  before() {
     validateRequest()
  }

  post("/search", TransactionAuditSearchSpec.TransactionSearchV2) {
    Try{
      parsedBody.extract[TransactionSearchRequestV2]
    }match {
      case Success(tsr) =>
        val resultFuture = TransactionSearchRequestValidator.validate(tsr) match {
          case Valid(a) =>
            transactionAuditSearchService.searchTransactionsV2(tsr)
          case Invalid(error) =>
            val errorString = error.toList.foldLeft("")((s, o) => {
              if(s.nonEmpty) s"$s ${o.errorMessage}" else s"$s${o.errorMessage}"
            })
            logger.debug(s"Validation failed for search v2 ${errorString}")
            Future.successful(Left(ErrorResponse(400, errorString)))
        }
        ScalatraResponseFactory.get(metrics.timeFuture("search.v2.duration")(resultFuture))
      case Failure(ex) =>
        logger.warn(s"Invalid request body for search v2, ${request.body}", ex)
        val errorResponse = Future.successful(Left(ErrorResponse(400, ex.getCause.getMessage)))
        ScalatraResponseFactory.get(errorResponse)
    }
  }

}
