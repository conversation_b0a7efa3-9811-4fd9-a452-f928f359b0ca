package me.socure.transactionaudit.read.servlet.search

import com.google.inject.Inject
import com.typesafe.config.Config
import io.swagger.v3.oas.models.OpenAPI
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.http.HttpStatus
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.openapi3.scalatra.OpenApiScalatraSupport
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.model.ErrorResponse
import me.socure.support.timeout.{ApiTimeout, FutureWithTimeoutSupport}
import me.socure.transactionaudit.read.common.errorhandler.JsonErrorHandling
import me.socure.transactionaudit.read.common.exception.ExceptionCodes
import me.socure.transactionaudit.read.service.TransactionAuditSearchService
import me.socure.transactionaudit.read.spec.TransactionAuditSearchSpec
import me.socure.transactionauditing.common.domain.{TransactionSearchRequest, TransactionSearchRequestDynamo}
import me.socure.transactionauditing.common.json.TrxJsonFormats
import me.socure.transactionauditing.common.transaction.TransactionColumns
import me.socure.transactionauditing.common.transaction.TransactionColumns.TransactionColumn
import me.socure.transactionauditing.sqs.model.summary.MonitorExecParameters
import me.socure.transactionauditstorage.mysqlschema.ApiTransaction
import org.json4s.Formats
import org.scalatra.ScalatraServlet
import org.scalatra.json.JacksonJsonSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class TransactionAuditSearchServlet @Inject()(
                                             transactionAuditSearchService: TransactionAuditSearchService,
                                               config: Config,
                                               val apiTimeout: ApiTimeout,
                                               val hmacVerifier: HMACHttpVerifier,
                                               val openApi: OpenAPI
                                             )
                                              (implicit val executor : ExecutionContext)
  extends ScalatraServlet with FutureWithTimeoutSupport with JacksonJsonSupport
    with JsonErrorHandling with AuthenticationSupport with OpenApiScalatraSupport  {

  override val logger: Logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get("transaction.auditing." + getClass.getSimpleName)

  override protected implicit def jsonFormats: Formats = TrxJsonFormats.value

  before() {
    validateRequest()
  }

  get("/:id", TransactionAuditSearchSpec.SearchByTxnID) {
    val id: String = params("id").trim
    val columns: Set[TransactionColumn] = params("columns").split(",").flatMap(s => TransactionColumns.byColumn(s)).toSet
    val enablePiiMask: Boolean = params("maskPii").toBoolean
    val resultFuture = transactionAuditSearchService.searchByTrxId(id, columns, enablePiiMask)
    metrics.timeFuture(s"byId.duration")(resultFuture)
    ScalatraResponseFactory.get(resultFuture)
  }

  get("/internal/:id", TransactionAuditSearchSpec.SearchByTxnIDWithInternalData) {
    val id: String = params("id").trim
    val columns: Set[TransactionColumn] = params("columns").split(",").flatMap(s => TransactionColumns.byColumn(s)).toSet
    val enablePiiMask: Boolean = params("maskPii").toBoolean
    val resultFuture = transactionAuditSearchService.searchByTrxIdWithInternalData(id, columns, enablePiiMask)
    metrics.timeFuture(s"internal.byId.duration")(resultFuture)
    ScalatraResponseFactory.get(resultFuture)
  }

  post("/search/ids", TransactionAuditSearchSpec.SearchByTxnIDs) {
    val maxRowFetch: Int = config.getInt("transaction-auditing.rest.maxRowFetch")
    println(params.get("ids"))
    val transactionIds: Set[String] = params.get("ids").map(_.split(",").filter(_.nonEmpty).toSet[String]).getOrElse(Set.empty[String])
    if (transactionIds.isEmpty || transactionIds.size > maxRowFetch) {
      ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(ExceptionCodes.InvalidInputFormat.id,
        "We can process only 1-100 transaction ids per request"))))
    } else {
      val columns: Set[TransactionColumn] = params("columns").split(",").flatMap(s => TransactionColumns.byColumn(s)).toSet
      val enablePiiMask: Boolean = params("maskPii").toBoolean
      val resultFuture = transactionAuditSearchService.searchByTrxIds(transactionIds, columns, enablePiiMask)
      metrics.timeFuture(s"byIds.duration")(resultFuture)
      ScalatraResponseFactory.get(resultFuture)
    }
  }

  post("/monitor/search/ids") {
    val transactionIds: Set[String] = params.get("ids").map(_.split(",").filter(_.nonEmpty).toSet[String]).getOrElse(Set.empty[String])
    if(transactionIds.isEmpty) ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(HttpStatus.BAD_REQUEST.value(), "No transaction id found in input"))))
    val columns: Set[TransactionColumn] = Set(
      TransactionColumns.ENVIRONMENT_ID,
      TransactionColumns.ACCOUNT_ID,
      TransactionColumns.PARAMETERS,
      TransactionColumns.DEBUG,
      TransactionColumns.TRANSACTION_DATE,
      TransactionColumns.API_NAME
    )
    val isPiiMask: Boolean = params("maskPii").toBoolean
    val result: Future[Either[ErrorResponse, Seq[MonitorExecParameters]]] = transactionAuditSearchService.searchByTrxIds(transactionIds, columns, isPiiMask) map {
      case Right(res) => Right(extractFields(res))
      case Left(err) => Left(err)
    }
    metrics.timeFuture("fetch.monitor.tx.byIds.duration")(result)
    ScalatraResponseFactory.get(result)
  }

  post("/search_by_id_or_cuid", TransactionAuditSearchSpec.SearchByTxnIDOrCustID) {
    val id = params("id")
    val accountIds = params
      .get("accountIds")
      .map(_.split(",")
        .toSet[String]
        .filter(_.nonEmpty)
        .map(_.toLong)
      )
      .getOrElse(Set.empty[Long])

    val columns = params("columns").split(",").flatMap(s => TransactionColumns.byColumn(s)).toSet
    val enablePiiMask = params("maskPii").toBoolean
    val resultFuture = transactionAuditSearchService.searchByTrxIdOrCustId(id, accountIds, columns, enablePiiMask)
    metrics.timeFuture("search_by_id_or_cuid.duration")(resultFuture)
    ScalatraResponseFactory.get(resultFuture)
  }

  post("/search", TransactionAuditSearchSpec.TransactionSearch) {
    val tsr = parsedBody.extract[TransactionSearchRequest]
    val resultFuture = transactionAuditSearchService.searchTransactions(tsr)
    metrics.timeFuture("search.duration")(resultFuture)
    ScalatraResponseFactory.get(resultFuture)
  }

  post("/search/dynamo") {
    val tsr = parsedBody.extract[TransactionSearchRequestDynamo]
    val resultFuture = transactionAuditSearchService.searchTransactionsDynamo(tsr)
    metrics.timeFuture("search.duration.dynamo")(resultFuture)
    ScalatraResponseFactory.get(resultFuture)
  }

  post("/count", TransactionAuditSearchSpec.TransactionSearchCount) {
    val tsr = parsedBody.extract[TransactionSearchRequest]
    val resultFuture = transactionAuditSearchService.countTransactions(tsr)
    metrics.timeFuture("count.duration")(resultFuture)
    ScalatraResponseFactory.get(resultFuture)
  }

  post("/count/dynamo", TransactionAuditSearchSpec.TransactionSearchCount) {
    val tsr = parsedBody.extract[TransactionSearchRequestDynamo]
    val resultFuture = transactionAuditSearchService.countTransactionsDynamo(tsr)
    metrics.timeFuture("count.duration.dynamo")(resultFuture)
    ScalatraResponseFactory.get(resultFuture)
  }

  post("/count/fetch", TransactionAuditSearchSpec.TransactionSearchCount) {
    val tsr = parsedBody.extract[TransactionSearchRequest]
    val resultFuture = transactionAuditSearchService.countTransactions(tsr)
    metrics.timeFuture("count.fetch.duration")(resultFuture)
    ScalatraResponseFactory.get(resultFuture)
  }

  private def extractFields(apiTransaction: ApiTransaction): MonitorExecParameters = {
    MonitorExecParameters.apply1(
      transactionIdBase = Option(apiTransaction.transactionId),
      apiName = apiTransaction.apiName,
      accountIdBase = apiTransaction.accountId,
      environmentIdBase = apiTransaction.environmentType,
      parameters = apiTransaction.parameters,
      debug = apiTransaction.scoringProps.debug,
      transactionDate = apiTransaction.transactionDate
    )
  }

  private def extractFields(apiTransactions: Seq[ApiTransaction]): Seq[MonitorExecParameters] = {
    apiTransactions.map(extractFields)
  }

}
