package me.socure.transactionaudit.read.spec

import io.swagger.v3.oas.annotations.enums.ParameterIn
import me.socure.common.openapi3.scalatra.{DataTypes, OpenApiSpecSupport}
import me.socure.common.openapi3.scalatra.model.{JsonResponseBody, Param}
import me.socure.model.ErrorResponse
import me.socure.transactionauditing.common.domain.WatchlistMonitoringOperation
import me.socure.transactionauditing.storage.model.WatchlistInfo
import org.scalatra

import scala.reflect.runtime.{universe => ru}

object SummarySpec {

  val Search: scalatra.RouteTransformer = OpenApiSpecSupport.endpointSpec(
    description = "Transaction audit service endpoint to search watchlist monitoring operations",
    summary = "Search Watchlist Monitoring Operations",
    params = Set.empty,
    requestBodies = None,
    responseBodies = Some(Map(
      200 -> JsonResponseBody(`type` = ru.typeOf[Seq[WatchlistMonitoringOperation]], description = Some("Watchlist monitoring operation details")),
      500 -> JsonResponseBody(`type` = ru.typeOf[ErrorResponse], description = Some("internal server error")))
    )
  )

  val WatchlistQuickInfo: scalatra.RouteTransformer = OpenApiSpecSupport.endpointSpec(
    description = "Transaction audit service endpoint to search watchlist quick info",
    summary = "Search Watchlist Quick Info",
    params = Set(
      Param(name = "ids", `type` = DataTypes.String, in = ParameterIn.QUERY, required = true,
        description = Some("comma separated transaction id's"))
    ),
    requestBodies = None,
    responseBodies = Some(Map(
      200 -> JsonResponseBody(`type` = ru.typeOf[Seq[WatchlistInfo]], description = Some("Watchlist Info details")),
      500 -> JsonResponseBody(`type` = ru.typeOf[ErrorResponse], description = Some("internal server error")))
    )
  )

}
