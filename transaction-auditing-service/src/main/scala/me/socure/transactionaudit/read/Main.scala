package me.socure.transactionaudit.read

import com.google.common.util.concurrent.ServiceManager
import com.google.inject.Guice
import me.socure.common.config.IntConfigurationModule
import me.socure.common.guice.module.{CommonModule, HealthModule}
import me.socure.common.microservice.defaults.DefaultMicroservice
import me.socure.transactionaudit.read.module.datasource.TransactionAuditReadDataSourceModule
import me.socure.transactionaudit.read.module.openapi.TransactionAuditReadOpenAPIModule
import me.socure.transactionaudit.read.module.servicemanager.TransactionAuditReadServiceManagerModule
import me.socure.transactionaudit.read.module.tareadservice.TransactionAuditReadServiceModule
import net.codingwell.scalaguice.InjectorExtensions._
import org.slf4j.{Logger, LoggerFactory}

import java.util.concurrent.TimeUnit
import scala.util.{Failure, Success, Try}

object Main extends DefaultMicroservice {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  override def startUp(args: Array[String]): Unit = {
    Try {
      val injector = Guice.createInjector(
        new CommonModule,
        new HealthModule,
        new IntConfigurationModule("jmx.port"),
        new TransactionAuditReadServiceManagerModule,
        new TransactionAuditReadOpenAPIModule,
        new TransactionAuditReadDataSourceModule,
        new TransactionAuditReadServiceModule
      )
      val services = injector.instance[ServiceManager]
      services.startAsync()
      sys.addShutdownHook {
        services.stopAsync().awaitStopped(5, TimeUnit.SECONDS)
      }

    } match {
      case Success(_) =>
      case Failure(ex) => {
        System.exit(1)
      }
    }
  }

}
