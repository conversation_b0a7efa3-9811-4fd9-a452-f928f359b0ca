package me.socure.transactionaudit.read.servlet.openapi

import com.google.inject.Inject
import com.google.inject.name.Named
import io.swagger.v3.oas.models.OpenAPI
import me.socure.common.openapi3.scalatra.OpenApiScalatraServlet
import org.scalatra.CorsSupport

class TransactionAuditReadOpenAPIServlet @Inject()(openAPI: OpenAPI,
                                                   @Named("corsAllowedDomain") corsAllowedDomains: String)
  extends OpenApiScalatraServlet(openApi = openAPI) with CorsSupport {

  after() {
    response.setHeader("Access-Control-Allow-Origin", corsAllowedDomains)
    response.setHeader("Strict-Transport-Security", "max-age=86400; includeSubDomains")
    response.setHeader("Cache-Control", "no-cache")
  }

  options("/*") {
    response.setHeader("Access-Control-Allow-Headers",
      request.getHeader("Access-Control-Request-Headers"))
  }

}
