package me.socure.transactionaudit.read.module.openapi

import com.google.inject.name.Named
import com.google.inject.{AbstractModule, Provides, Singleton}
import com.typesafe.config.Config
import io.swagger.v3.oas.models.OpenAPI
import io.swagger.v3.oas.models.info.{Contact, Info}
import me.socure.service.constants.OpenApiConstants

import scala.collection.JavaConverters._

class TransactionAuditReadOpenAPIModule extends AbstractModule {

  @Provides
  @Singleton
  def openAPI(): OpenAPI = {
    new OpenAPI()
      .info(new Info()
        .description("Transaction Audit Read Service")
        .contact(new Contact()
          .email(OpenApiConstants.Email)
          .name(OpenApiConstants.Name)
          .url(OpenApiConstants.Url)
        )
        .termsOfService(OpenApiConstants.TermsOfService)
      )
  }

  @Provides
  @Named("corsAllowedDomain")
  def corsAllowedDomain(config: Config): String = {
    config.getStringList("cors.allowedDomains").asScala.toSet.mkString(",")
  }

}
