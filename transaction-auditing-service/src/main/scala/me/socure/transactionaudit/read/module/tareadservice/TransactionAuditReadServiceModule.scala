package me.socure.transactionaudit.read.module.tareadservice

import java.util.concurrent.TimeUnit

import com.google.common.util.concurrent.AbstractIdleService
import com.google.inject.multibindings.Multibinder
import com.google.inject.name.Named
import com.google.inject.{AbstractModule, Provides, Singleton}
import com.typesafe.config.Config
import me.socure.common.check.impl.db.DbCheckerAutoLoader
import me.socure.common.check.impl.http.HttpCheckerAutoLoader
import me.socure.common.check.servlet.api.CheckerServlet
import me.socure.common.clock.Clock
import me.socure.common.hmac.factory.{HMACEncrypterFactory, NonceCacheServiceFactory}
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.common.jmx.SocureMicroJMXService
import me.socure.support.timeout.ApiTimeout
import me.socure.transactionaudit.read.common.config.HMACConfigFactory
import me.socure.transactionaudit.read.TransactionAuditReadService

import scala.concurrent.duration.{Duration, FiniteDuration}

class TransactionAuditReadServiceModule extends AbstractModule {

  override def configure(): Unit = {
    val b = Multibinder.newSetBinder(binder(), classOf[AbstractIdleService])
    b.addBinding().to(classOf[TransactionAuditReadService])
    b.addBinding().to(classOf[SocureMicroJMXService])
  }

  @Provides
  @Singleton
  def serverMetricsEnabled(config: Config) : Boolean = {
    if(config.hasPath("server.metrics.enabled")) config.getBoolean("server.metrics.enabled") else true
  }

  @Provides
  @Named("appPort")
  def appPort(config: Config): Int = {
    config.getInt("transaction-auditing.server.port")
  }

  @Provides
  @Singleton
  def apiTimeout(config: Config): ApiTimeout = {
    val timeoutConfig: String = config.getString("transaction-auditing.server.apiTimeout")
    ApiTimeout(Duration(timeoutConfig) match {
      case d if d.isFinite() => FiniteDuration(d.toMillis, TimeUnit.MILLISECONDS)
      case d => throw new IllegalStateException(s"Expected a finite duration for server.apiTimeout but found $d")
    })
  }

  @Provides
  @Singleton
  def hmacHTTPVerifier(config: Config, @Named("RealClock") clock: Clock): HMACHttpVerifier = {
    val hmacConfig = HMACConfigFactory.create(config.getConfig("hmac"))
    val encrypter = HMACEncrypterFactory.get(hmacConfig.secretKey, hmacConfig.strength)
    val nonceCacheService = NonceCacheServiceFactory.create(hmacConfig.ttl)
    new HMACHttpVerifier(hmacConfig.timeInterval, encrypter, nonceCacheService, clock)
  }

  @Provides
  @Singleton
  @Named("smokeTestServlet")
  def smokeTestServlet(): CheckerServlet = {
    CheckerServlet(
      DbCheckerAutoLoader,
      HttpCheckerAutoLoader
    )
  }

}
