package me.socure.transactionaudit.read.spec

import io.swagger.v3.oas.annotations.enums.ParameterIn
import me.socure.common.openapi3.scalatra.model.{JsonRequestBody, JsonResponseBody, Param}
import me.socure.common.openapi3.scalatra.{DataTypes, OpenApiSpecSupport}
import me.socure.model.ErrorResponse
import me.socure.transactionauditing.common.domain.TransactionSearchRequest
import me.socure.transactionauditing.common.domain.v2.{TransactionSearchRequestV2, TransactionSearchResponseV2}
import me.socure.transactionauditing.sqs.model.summary.MonitorExecParameters
import me.socure.transactionauditstorage.mysqlschema.ApiTransaction
import org.scalatra

import scala.reflect.runtime.{universe => ru}

object TransactionAuditSearchSpec {

  // SearchByTxnID - BEGIN

  private val searchByTxnIDParams = Set(
    Param(name = "id", `type` = DataTypes.String, in = ParameterIn.PATH, required = true,
      description = Some("API transaction ID to load")),
    Param(name = "columns", `type` = DataTypes.String, in = ParameterIn.QUERY, required = true,
      description = Some("Comma separated list of columns to load")),
    Param(name = "maskPii", `type` = DataTypes.Boolean, in = ParameterIn.QUERY, required = true,
      description = Some("mask PII elements in the response"))
  )

  private val searchByTxnIDResponse = Some(Map(
    200 -> JsonResponseBody(`type` = ru.typeOf[Seq[ApiTransaction]],
      description = Some("API transaction data")),
    400 -> JsonResponseBody(`type` = ru.typeOf[ErrorResponse],
      description = Some("invalid request")),
    500 -> JsonResponseBody(`type` = ru.typeOf[ErrorResponse],
      description = Some("internal server error"))
  ))

  val SearchByTxnID: scalatra.RouteTransformer = OpenApiSpecSupport.endpointSpec(
    description = "Transaction Audit Read Service Endpoint to search API transaction by ID",
    summary = "Search API Transaction by ID",
    params = searchByTxnIDParams,
    requestBodies = None,
    responseBodies = searchByTxnIDResponse
  )

  val SearchByTxnIDWithInternalData: scalatra.RouteTransformer = OpenApiSpecSupport.endpointSpec(
    description = "Transaction Audit Read Service Endpoint to search API transaction by ID with internal data included",
    summary = "Search API Transaction by ID with internal data included",
    params = searchByTxnIDParams,
    requestBodies = None,
    responseBodies = searchByTxnIDResponse
  )

  // SearchByTxnID - END

  // SearchByTxnIDs - BEGIN

  private val searchByTxnIDsParams = Set(
    Param(name = "ids", `type` = DataTypes.String, in = ParameterIn.QUERY, required = true,
      description = Some("Comma separated list of API txn-ids to load")),
    Param(name = "columns", `type` = DataTypes.String, in = ParameterIn.QUERY, required = true,
      description = Some("Comma separated list of columns to load")),
    Param(name = "maskPii", `type` = DataTypes.Boolean, in = ParameterIn.QUERY, required = true,
      description = Some("mask PII elements in the response"))
  )

  private val searchByTxnIDsResponse = Some(Map(
    200 -> JsonResponseBody(`type` = ru.typeOf[Seq[ApiTransaction]],
      description = Some("API transaction data")),
    400 -> JsonResponseBody(`type` = ru.typeOf[ErrorResponse],
      description = Some("invalid request")),
    500 -> JsonResponseBody(`type` = ru.typeOf[ErrorResponse],
      description = Some("internal server error"))
  ))

  val SearchByTxnIDs: scalatra.RouteTransformer = OpenApiSpecSupport.endpointSpec(
    description = "Transaction Audit Read Service Endpoint to search for multiple transaction IDs",
    summary = "Search Multiple API Transactions by ID",
    params = searchByTxnIDsParams,
    requestBodies = None,
    responseBodies = searchByTxnIDsResponse
  )

  // SearchByTxnIDs - END

  // MonitorSearchByTxnIDs - BEGIN

  private val monitorSearchByTxnIDsParams = Set(
    Param(name = "ids", `type` = DataTypes.String, in = ParameterIn.QUERY, required = true,
      description = Some("Comma separated list of API txn-ids to load")),
    Param(name = "maskPii", `type` = DataTypes.Boolean, in = ParameterIn.QUERY, required = true,
      description = Some("mask PII elements in the response"))
  )

  private val monitorSearchByTxnIDsResponse = Some(Map(
    200 -> JsonResponseBody(`type` = ru.typeOf[Seq[MonitorExecParameters]],
      description = Some("API transaction monitoring data")),
    400 -> JsonResponseBody(`type` = ru.typeOf[ErrorResponse],
      description = Some("invalid request")),
    500 -> JsonResponseBody(`type` = ru.typeOf[ErrorResponse],
      description = Some("internal server error"))
  ))

  val MonitorSearchByTxnIDs: scalatra.RouteTransformer = OpenApiSpecSupport.endpointSpec(
    description = "Transaction Audit Read Service Endpoint to search for multiple transaction IDs for monitor",
    summary = "Search Multiple API Transactions by ID for Monitor",
    params = monitorSearchByTxnIDsParams,
    requestBodies = None,
    responseBodies = monitorSearchByTxnIDsResponse
  )

  // MonitorSearchByTxnIDs - END

  // SearchByTxnIDOrCustID - BEGIN

  private val searchByTxnIDOrCustIDParams = Set(
    Param(name = "id", `type` = DataTypes.String, in = ParameterIn.QUERY, required = true,
      description = Some("Transaction ID or Customer ID to load")),
    Param(name = "accountIds", `type` = DataTypes.String, in = ParameterIn.QUERY, required = true,
      description = Some("Comma separated list of account-ids to load")),
    Param(name = "columns", `type` = DataTypes.String, in = ParameterIn.QUERY, required = true,
      description = Some("Comma separated list of columns to load")),
    Param(name = "maskPii", `type` = DataTypes.Boolean, in = ParameterIn.QUERY, required = true,
      description = Some("mask PII elements in the response"))
  )

  private val searchByTxnIDOrCustIDResponse = Some(Map(
    200 -> JsonResponseBody(`type` = ru.typeOf[Seq[ApiTransaction]],
      description = Some("API transaction data")),
    400 -> JsonResponseBody(`type` = ru.typeOf[ErrorResponse],
      description = Some("invalid request")),
    500 -> JsonResponseBody(`type` = ru.typeOf[ErrorResponse],
      description = Some("internal server error"))
  ))

  val SearchByTxnIDOrCustID: scalatra.RouteTransformer = OpenApiSpecSupport.endpointSpec(
    description = "Transaction Audit Read Service Endpoint to search for transactions with txn-id or customer-id",
    summary = "Search API Transactions by Transaction ID or Customer ID",
    params = searchByTxnIDOrCustIDParams,
    requestBodies = None,
    responseBodies = searchByTxnIDOrCustIDResponse
  )

  // SearchByTxnIDOrCustID - END

  // TransactionSearch - BEGIN

  private val transactionSearchRequestBody = JsonRequestBody(
    `type` = ru.typeOf[TransactionSearchRequest],
    required = true,
    description = Some("transaction search request")
  )

  private val transactionSearchResponse = Some(Map(
    200 -> JsonResponseBody(`type` = ru.typeOf[Seq[ApiTransaction]],
      description = Some("API transaction data")),
    400 -> JsonResponseBody(`type` = ru.typeOf[ErrorResponse],
      description = Some("invalid request")),
    500 -> JsonResponseBody(`type` = ru.typeOf[ErrorResponse],
      description = Some("internal server error"))
  ))

  private val transactionSearchV2RequestBody = JsonRequestBody(
    `type` = ru.typeOf[TransactionSearchRequestV2],
    required = true,
    description = Some("transaction search request v2")
  )

  private val transactionSearchResponseV2 = Some(Map(
    200 -> JsonResponseBody(`type` = ru.typeOf[TransactionSearchResponseV2],
      description = Some("API Transaction Response")),
    400 -> JsonResponseBody(`type` = ru.typeOf[ErrorResponse],
      description = Some("invalid request")),
    500 -> JsonResponseBody(`type` = ru.typeOf[ErrorResponse],
      description = Some("internal server error"))
  ))

  val TransactionSearch: scalatra.RouteTransformer = OpenApiSpecSupport.endpointSpec(
    description = "Transaction Audit Read Service Endpoint to search for transaction with specific constraints",
    summary = "Search API Transactions with Specific Constraints",
    params = Set.empty,
    requestBodies = Some(transactionSearchRequestBody),
    responseBodies = transactionSearchResponse
  )

  val TransactionSearchV2: scalatra.RouteTransformer = OpenApiSpecSupport.endpointSpec(
    description = "Transaction Audit Read Service Endpoint v2 to search for transaction with specific constraints",
    summary = "Search API v2 transactions with specific constraints",
    params = Set.empty,
    requestBodies = Some(transactionSearchV2RequestBody),
    responseBodies = transactionSearchResponseV2
  )

  // TransactionSearch - END

  // TransactionSearchCount - BEGIN

  private val transactionSearchCountRequestBody = JsonRequestBody(
    `type` = ru.typeOf[TransactionSearchRequest],
    required = true,
    description = Some("transaction search request for count")
  )

  private val transactionSearchCountResponse = Some(Map(
    200 -> JsonResponseBody(`type` = ru.typeOf[Int],
      description = Some("Count of transactions that satisfied the request")),
    400 -> JsonResponseBody(`type` = ru.typeOf[ErrorResponse],
      description = Some("invalid request")),
    500 -> JsonResponseBody(`type` = ru.typeOf[ErrorResponse],
      description = Some("internal server error"))
  ))

  val TransactionSearchCount: scalatra.RouteTransformer = OpenApiSpecSupport.endpointSpec(
    description = "Transaction Audit Read Service Endpoint to get count of transactions that satisfy a constraint",
    summary = "Count API Transactions with Specific Constraints",
    params = Set.empty,
    requestBodies = Some(transactionSearchCountRequestBody),
    responseBodies = transactionSearchCountResponse
  )

  // TransactionSearchCount - END

}
