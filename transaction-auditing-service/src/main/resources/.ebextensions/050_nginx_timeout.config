files:
  "/opt/elasticbeanstalk/hooks/appdeploy/post/91_add_nginx_configuration.sh":
    content: |
        #!/usr/bin/env bash
        /bin/echo "proxy_connect_timeout 600s;" >> /etc/nginx/conf.d/proxy.conf
        /bin/echo "proxy_send_timeout 600s;" >> /etc/nginx/conf.d/proxy.conf
        /bin/echo "proxy_read_timeout 600s;" >> /etc/nginx/conf.d/proxy.conf
        /bin/echo "send_timeout 600s;" >> /etc/nginx/conf.d/proxy.conf
        /sbin/service nginx reload
    group: root
    mode: "000755"
    owner: root