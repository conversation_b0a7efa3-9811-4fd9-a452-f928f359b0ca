name = "assert_transaction_audit_service"

request {
  method = "POST"
  endpoint = ${hostname}"/transaction/search/ids"
  configurators = [
    {
      type = "socure_hmac"
      realm = "Socure"
      version = "1.0"
      strength = 512
      secret_key = ${hmac.secret.key}
    },
    {
      type = "disable_cert_validation"
    }
  ]
  headers = {
    "Content-Type" = "application/x-www-form-urlencoded"
  }
  multipart_url_encoded = {
    "ids" = ${transactionid}
    "columns" = "uuid,customerUserId,debug,api,apiKey"
    "maskPii" = "true"
  }
}

validations = [
  {
    validator = "=="
    json_path = "status_code"
    value = 200
  }
  {
    validator = "regex"
    json_path = "body.json.data[0].apiKey"
    value = "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"
  }
  {
    validator = "=="
    json_path = "body.json.data[0].transactionId"
    value = ${transactionid}
  }
  {
    validator = "=="
    json_path = "body.json.data[0].environmentType"
    value = 1
  }
  {
    validator = "=="
    json_path = "body.json.data[0].api"
    value = "REST"
  }
]