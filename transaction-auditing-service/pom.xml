<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>transaction-auditing</artifactId>
        <groupId>me.socure</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>transaction-auditing-service</artifactId>
    <dependencies>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.13</version>
        </dependency>
        <!-- AUDIT STORAGE -->
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>transaction-auditing-storage</artifactId>
            <version>${revision}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.eclipse.jetty</groupId>
                    <artifactId>jetty-util</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- SCALATRA -->
        <dependency>
            <groupId>org.scalatra</groupId>
            <artifactId>scalatra_2.11</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scalatra</groupId>
            <artifactId>scalatra-scalatest_2.11</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
	    <groupId>com.mysql</groupId>
	    <artifactId>mysql-connector-j</artifactId>
	    <version>${mysql.connector.j.version}</version> 
            <exclusions>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>protobuf-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- GUICE -->
        <dependency>
            <groupId>com.google.inject</groupId>
            <artifactId>guice</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.inject.extensions</groupId>
            <artifactId>guice-multibindings</artifactId>
        </dependency>
        <dependency>
            <groupId>net.codingwell</groupId>
            <artifactId>scala-guice_2.11</artifactId>
            <version>4.2.2</version>
        </dependency>
        <!-- JETTY SERVER -->
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-webapp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-servlet</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-server</artifactId>
        </dependency>
        <!-- SOCURE COMMON -->
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-microservice</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-openapi3-scalatra</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-check-servlet-api</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-check-impl-db</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-check-impl-http</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-jmx-service</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-docker-sqs-service</artifactId>
            <version>${socure.common.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-docker-mysql-service</artifactId>
            <version>${socure.common.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-docker-client-factory</artifactId>
            <version>${socure.common.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-sql</artifactId>
            <version>${socure.common.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-jetty</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-ms-dependencies</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-scalatraresponse-factory</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-environment</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-c3p0-factory</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-hmac-http</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_2.11</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-servlet-metrics</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-rds</artifactId>
        </dependency>
        <dependency>
            <groupId>org.typelevel</groupId>
            <artifactId>cats-core_2.11</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>netty-nio-client</artifactId>
            <version>${aws.java.sdk.v2.version}</version>
        </dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>properties-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>jib-fips</id>
            <activation>
                <property>
                    <name>build-environment</name>
                    <value>gitlab-ci-fips</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>3.2.1</version>
                        <configuration>
                            <allowInsecureRegistries>true</allowInsecureRegistries>
                            <from>
                                <image>${env.JIB_JDK8_FIPS_IMAGE}</image>
                                <platforms>
                                    <platform>
                                        <architecture>amd64</architecture>
                                        <os>linux</os>
                                    </platform>
                                    <platform>
                                        <architecture>arm64</architecture>
                                        <os>linux</os>
                                    </platform>
                                </platforms>
                            </from>
                            <to>
                                <image>fips-registry.us-east-1.build.socure.link/${env.PROJECT_NAME}/${env.SVC_NAME}
                            </image>
                                <auth>
                                    <username>${env.REGISTRY_USER}</username>
                                    <password>${env.REGISTRY_PASS}</password>
                                </auth>
                                <tags>
                                    <tag>latest-fips</tag>
                                    <tag>${env.FIPS_DOCKER_IMAGE_TAG}</tag>
                                </tags>
                            </to>
                            <container>
                                <mainClass>me.socure.transactionaudit.read.Main</mainClass>
                                <ports>
                                    <port>5000</port>
                                </ports>
                            </container>
                        </configuration>
                        <executions>
                            <execution>
                                <id>build-and-push-docker-image</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>build</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>jib</id>
            <activation>
                <property>
                    <name>build-environment</name>
                    <value>gitlab-ci</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>3.2.1</version>
                        <configuration>
                            <from>
                                <image>${env.JIB_JDK8_IMAGE}</image>
                                <platforms>
                                    <platform>
                                        <architecture>amd64</architecture>
                                        <os>linux</os>
                                    </platform>
                                    <platform>
                                        <architecture>arm64</architecture>
                                        <os>linux</os>
                                    </platform>
                                </platforms>
                            </from>
                            <to>
                                <image>registry.us-east-1.build.socure.link/${env.PROJECT_NAME}/${env.SVC_NAME}</image>
                                <auth>
                                    <username>${env.REGISTRY_USER}</username>
                                    <password>${env.REGISTRY_PASS}</password>
                                </auth>
                                <tags>
                                    <tag>latest</tag>
                                    <tag>${env.NON_FIPS_DOCKER_IMAGE_TAG}</tag>
                                </tags>
                            </to>
                            <container>
                                <mainClass>me.socure.transactionaudit.read.Main</mainClass>
                                <ports>
                                    <port>5000</port>
                                </ports>
                            </container>
                        </configuration>
                        <executions>
                            <execution>
                                <id>build-and-push-docker-image</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>build</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
