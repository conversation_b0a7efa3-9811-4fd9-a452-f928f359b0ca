package me.socure.transactionauditing.client.sqspool

import com.amazonaws.services.s3.AmazonS3Client
import me.socure.transactionauditing.common.config.{ClientSqsEndpoint}
import me.socure.transactionauditing.common.util.SqsQueueEnums
import org.scalatest.mock.MockitoSugar
import org.scalatest.{FunSuite, Matchers}

class RoundRobinExecutorWithFailoverFactoryTest extends FunSuite with Matchers with MockitoSugar {


  test("create a new roundrobinfactory") {
    val roundRobinExecuteWithFailover = new RoundRobinExecutorWithFailoverFactory(Seq.empty, minBackOff = 2, maxBackoff = 32)
    roundRobinExecuteWithFailover.initializedEndpoints shouldBe Seq.empty
  }

  test("make sure we reuse the existing aws clients instead of creating new ones") {
    val endpoints = Seq(mock[ClientSqsEndpoint])
    val otherEndpoints = Seq(mock[ClientSqsEndpoint])
    val roundRobinExecuteWithFailover = new RoundRobinExecutorWithFailoverFactory(Seq.empty, minBackOff = 2, maxBackoff = 32) {
      override val initializedEndpoints = endpoints
    }
    roundRobinExecuteWithFailover.initializedEndpoints shouldBe endpoints
    roundRobinExecuteWithFailover.get().endpoints shouldBe endpoints
    roundRobinExecuteWithFailover.get().endpoints shouldBe endpoints
    roundRobinExecuteWithFailover.get().endpoints shouldBe endpoints
    roundRobinExecuteWithFailover.get().endpoints should not be otherEndpoints
  }
}
