package me.socure.transactionauditing.client

import akka.actor.ActorSystem
import akka.testkit.TestProbe
import com.amazonaws.handlers.AsyncHandler
import com.amazonaws.services.sqs.model.{SendMessageRequest, SendMessageResult}
import me.socure.transactionauditing.client.sqspool.{RoundRobinExecuteWithFailover, RoundRobinExecutorWithFailoverFactory}
import me.socure.transactionauditing.common.config.{ClientSqsEndpoint, SqsBaseClientConfiguration}
import me.socure.transactionauditing.common.util.SqsQueueEnums
import me.socure.transactionauditing.sqs.model.{SqsAPITransactionValueGenerator, SqsMessage}
import me.socure.transactionauditing.sqs.queue.SqsProducerQueue
import org.mockito.Matchers._
import org.mockito.Mockito._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Milliseconds, Seconds, Span}
import org.scalatest.{FunSuite, Matchers}

import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

class TransactionAuditingClientTest extends FunSuite with Matchers with ScalaFutures with MockitoSugar {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  override implicit val patienceConfig: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(50, Milliseconds))
  implicit val system: ActorSystem = ActorSystem()


  val sqsBaseConfiguration = mock[SqsBaseClientConfiguration]
  when(sqsBaseConfiguration.maxRetries).thenReturn(3)
  when(sqsBaseConfiguration.minBackoff).thenReturn(2)
  when(sqsBaseConfiguration.maxBackoff).thenReturn(16)
  val sqsEndpoint = mock[ClientSqsEndpoint]
  when(sqsEndpoint.queueType).thenReturn(SqsQueueEnums.TRANSACTION)
  val roundRobinExecutorWithFailover = mock[RoundRobinExecuteWithFailover]
  when(roundRobinExecutorWithFailover.endpoints).thenReturn(Seq(sqsEndpoint))
  val producerQueue = mock[SqsProducerQueue]
  val roundRobinExecutorFactory = mock[RoundRobinExecutorWithFailoverFactory]
  when(roundRobinExecutorFactory.get()).thenReturn(roundRobinExecutorWithFailover)
  val callback = mock[AsyncHandler[SendMessageRequest, SendMessageResult]]
  val sendMessageResult = new SendMessageResult()
  sendMessageResult.setMessageId("test-message-id")
  when(producerQueue.send(any(), any(), any())(any())).thenReturn(Future.successful(sendMessageResult))

  val transactionAuditingClient = new TransactionAuditingClient(sqsBaseConfiguration, producerQueue, roundRobinExecutorFactory)

  test("insert sqs message") {
    when(roundRobinExecutorWithFailover.execute(any(), any(), any())(any())).thenReturn(Future.successful(sendMessageResult))
    val message = SqsAPITransactionValueGenerator.aAPITransaction()
    whenReady(transactionAuditingClient.pushTransaction(message, callback, (_) => ())) { result =>
      result shouldBe sendMessageResult
    }
  }



  test("fail on non-sqs message") {
    when(roundRobinExecutorWithFailover.execute(any(), any(), any())(any())).thenReturn(Future.successful(sendMessageResult))
    val message:SqsMessage = null
    intercept[IllegalArgumentException] {
      whenReady(transactionAuditingClient.pushTransaction(message, callback, (_) => ()).failed) { result =>
        result shouldBe sendMessageResult
      }
    }
  }

  test("should trigger fallback logic when failing to insert") {
    val ex = new Exception("failed")
    when(roundRobinExecutorWithFailover.execute(any(), any(), any())(any())).thenReturn(Future.failed(ex))
    val fallback = "fallback triggered"
    val message:SqsMessage = SqsAPITransactionValueGenerator.aAPITransaction()
    val probe = TestProbe()
    def failedCallback(e: Throwable) = {
     probe.ref ! fallback
    }
    whenReady(transactionAuditingClient.pushTransaction(message, callback, failedCallback).failed) { result =>
      result shouldBe ex
      probe expectMsg (10.seconds, fallback)
    }
  }
}
