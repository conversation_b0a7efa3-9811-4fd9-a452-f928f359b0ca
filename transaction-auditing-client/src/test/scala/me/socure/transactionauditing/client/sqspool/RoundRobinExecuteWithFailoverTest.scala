package me.socure.transactionauditing.client.sqspool

import akka.testkit.TestProbe
import akka.actor.{ActorRef, ActorSystem}
import akka.stream.ActorMaterializer
import atmos.dsl.{RetryPolicy, keepRetrying, retryFor, stopRetrying}
import rummage.AkkaClock._
import com.amazonaws.handlers.AsyncHandler
import com.amazonaws.services.sqs.model.{SendMessageRequest, SendMessageResult}
import me.socure.common.transaction.id.TrxId
import me.socure.transactionauditing.common.config.{ClientSqsEndpoint, ClientSqsEndpointConfig, ClientSqsQueueConfig}
import me.socure.transactionauditing.common.model.SqsQueue
import me.socure.transactionauditing.sqs.model.{SqsAPITransactionValueGenerator, SqsMessage}
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.{FunSuite, Matchers}
import atmos.dsl._
import rummage.AkkaClock

import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}
import scala.util.{Failure, Success}

class RoundRobinExecuteWithFailoverTest extends FunSuite with Matchers with MockitoSugar with ScalaFutures {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  implicit val system: ActorSystem = ActorSystem()
  implicit val clock = AkkaClock(system.scheduler)
  implicit val mat: ActorMaterializer = ActorMaterializer()

  val transactionQueue = ClientSqsQueueConfig(
    queueName = "test-queue",
    waitTimeSeconds = 20,
    maxBatchSize = 20,
    maxBufferSize = 20,
    maxInFlight = 15
  )

  val endpoints = Seq(
    ClientSqsEndpointConfig(region = "us-east-1", transactionQueue),
    ClientSqsEndpointConfig(region = "us-west-1", transactionQueue),
    ClientSqsEndpointConfig(region = "us-east-2", transactionQueue)
  )

  implicit val trxId = TrxId("test-trx")

  val clientSqsEndpoints = Seq(mock[ClientSqsEndpoint], mock[ClientSqsEndpoint], mock[ClientSqsEndpoint])

  implicit def exponentialRetryPolicy(implicit trxId: TrxId): RetryPolicy = retryFor { 3 attempts } onError {
    case e: Throwable => {
      stopRetrying
    }
  }

  val sendMessageRequest = mock[SendMessageRequest]
  val sendMessageResult = mock[SendMessageResult]
  val sendFailureException = mock[Exception]


  def getSqsTransactionExecutor() = new RoundRobinExecuteWithFailover(
    minBackoff = 2,
    maxBackoff = 32,
    randomFactor = 0,
    endpointPool = clientSqsEndpoints
  )



  test("should succeed on the first attempt") {
    val probe = TestProbe()
    def send(msg: SqsMessage, sqsEndpoint: ClientSqsEndpoint, callback: AsyncHandler[SendMessageRequest, SendMessageResult])
            (implicit ec: ExecutionContext): Future[SendMessageResult] = {
      Mockito.when(sqsEndpoint.queue).thenReturn(SqsQueue("test-queue-east"))

      val futureResult =  Future{
        if(sqsEndpoint.queue != SqsQueue("test-queue-east")) throw new Exception("Queue not found")
      }
      futureResult.onComplete{
        case Success(_) => callback.onSuccess(sendMessageRequest, sendMessageResult)
        case Failure(ex: Exception) => callback.onError(ex)
      }
      futureResult.map(_ => sendMessageResult)
    }
    val myAsyncHandler =  new AsyncHandler[SendMessageRequest, SendMessageResult] {
      override def onError(exception: Exception): Unit = {
        probe.ref ! exception
      }
      override def onSuccess(request: SendMessageRequest, result: SendMessageResult): Unit = {
        probe.ref ! result
      }
    }

    val roundRobinExecutor = getSqsTransactionExecutor()
    val msg = SqsAPITransactionValueGenerator.aAPITransaction()
    val x: Future[SendMessageResult] = roundRobinExecutor.execute(
      msg = msg,
      callback = myAsyncHandler,
      operation = send
    )

    whenReady(x) { res =>
      res shouldBe sendMessageResult
    }
    probe expectMsg (10.seconds, sendMessageResult)
  }

  test("should succeed on the second attempt") {
    var counter = 0
    val probe = TestProbe()
    def send(msg: SqsMessage, sqsEndpoint: ClientSqsEndpoint, callback: AsyncHandler[SendMessageRequest, SendMessageResult])
            (implicit ec: ExecutionContext): Future[SendMessageResult] = {
      if(counter == 0)
        Mockito.when(sqsEndpoint.queue).thenReturn(SqsQueue("test-queue-west"))
      else
        Mockito.when(sqsEndpoint.queue).thenReturn(SqsQueue("test-queue-east"))

      val futureResult =  Future{
        if(sqsEndpoint.queue != SqsQueue("test-queue-east")) throw new Exception("Queue not found")
      }
      futureResult.onComplete{
        case Success(_) => callback.onSuccess(sendMessageRequest, sendMessageResult)
        case Failure(ex: Exception) => {
          counter += 1
          callback.onError(ex)
        }
      }
      futureResult.map(_ => sendMessageResult)
    }
    val myAsyncHandler =  new AsyncHandler[SendMessageRequest, SendMessageResult] {
      override def onError(exception: Exception): Unit = {
        probe.ref ! sendFailureException
      }
      override def onSuccess(request: SendMessageRequest, result: SendMessageResult): Unit = {
        probe.ref ! result
      }
    }

    val roundRobinExecutor = getSqsTransactionExecutor()
    val msg = SqsAPITransactionValueGenerator.aAPITransaction()
    val x: Future[SendMessageResult] = retryAsync("Executing transaction sqs executor") {
      roundRobinExecutor.execute(
        msg = msg,
        callback = myAsyncHandler,
        operation = send
      )
    }

    whenReady(x) { res =>
      res shouldBe sendMessageResult
    }
    probe expectMsg (10.seconds, sendFailureException)
    probe expectMsg (10.seconds, sendMessageResult)
  }

}
