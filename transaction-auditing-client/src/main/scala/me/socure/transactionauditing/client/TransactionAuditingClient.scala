package me.socure.transactionauditing.client

import atmos.dsl._
import com.amazonaws.SdkClientException
import com.amazonaws.handlers.AsyncHandler
import com.amazonaws.services.sqs.model.{SendMessageRequest, SendMessageResult}
import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.transaction.id.TrxId
import me.socure.transactionauditing.client.sqspool.RoundRobinExecutorWithFailoverFactory
import me.socure.transactionauditing.common.config.SqsBaseClientConfiguration
import me.socure.transactionauditing.sqs.model.{SqsApiTransaction, SqsMessage}
import me.socure.transactionauditing.sqs.queue.SqsProducerQueue

import scala.concurrent.{ExecutionContext, Future}

class TransactionAuditingClient(sqsBaseConfiguration: SqsBaseClientConfiguration,
                                producerQueue: SqsProducerQueue,
                                roundRobinExecutorWithFailoverFactory: RoundRobinExecutorWithFailoverFactory) {
  private val log = TransactionAwareLoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get(classOf[TransactionAuditingClient].getSimpleName)

  private implicit def exponentialRetryPolicy(implicit trxId: TrxId): RetryPolicy = retryFor { sqsBaseConfiguration.maxRetries attempts } onError {
    case e: SdkClientException => {
      log.error("Error communicating with SQS", e)
      keepRetrying
    }
    case e: Throwable => {
      log.error("Unknown error sending message to SQS, not retrying", e)
      stopRetrying
    }
  }

  initCheck()

  private def getSqsExecutor() = roundRobinExecutorWithFailoverFactory.get()

  def pushTransaction(message: SqsMessage, callback:AsyncHandler[SendMessageRequest, SendMessageResult], fallback: (Throwable) => Unit)(implicit ec: ExecutionContext): Future[SendMessageResult] = {
    implicit val trxId: TrxId = message match {
      case tx: SqsApiTransaction => TrxId(tx.transactionId)
      case _ => throw new IllegalArgumentException("Unsupported SQS message type")
    }
    val sqsTransactionExecutor = getSqsExecutor()
    log.debug("Pushing %s transaction to queue".format(sqsTransactionExecutor.endpoints.head.queueType.name))
    retryAsync("Executing %s transaction sqs executor".format(sqsTransactionExecutor.endpoints.head.queueType.name)) {
      sqsTransactionExecutor.execute(message, callback, producerQueue.send)
    }.recoverWith {
      case e: Throwable =>
        metrics.increment("push_call.exception", "class:" + e.getClass.getSimpleName)
        fallback(e)
        Future.failed(e)
    }
  }

  private def initCheck() = getSqsExecutor()
}
