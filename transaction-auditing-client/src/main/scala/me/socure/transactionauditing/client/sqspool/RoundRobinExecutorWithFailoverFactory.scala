package me.socure.transactionauditing.client.sqspool

import me.socure.transactionauditing.common.config.ClientSqsEndpoint

class RoundRobinExecutorWithFailoverFactory(val initializedEndpoints: Seq[ClientSqsEndpoint],
                                            minBackOff: Int,
                                            maxBackoff: Int) {
  def get(): RoundRobinExecuteWithFailover = {
    new RoundRobinExecuteWithFailover(
      minBackoff = minBackOff,
      maxBackoff = maxBackoff,
      randomFactor = 0,
      endpointPool = initializedEndpoints
    )
  }
}
