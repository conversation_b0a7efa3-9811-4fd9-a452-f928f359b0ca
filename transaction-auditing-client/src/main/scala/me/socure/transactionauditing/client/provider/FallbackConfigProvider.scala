package me.socure.transactionauditing.client.provider

import com.amazonaws.auth.{AWSCredentials, AWSCredentialsProvider, BasicAWSCredentials}
import com.typesafe.config.Config

@Deprecated
class FallbackConfigProvider(config: Config) extends AWSCredentialsProvider {
  private val awsKey: String = config.getString("transaction-auditing.aws.access.key")
  private val awsSecret: String = config.getString("transaction-auditing.aws.access.secret")

  override def getCredentials: AWSCredentials = {
    new BasicAWSCredentials(awsKey, awsSecret)
  }

  override def refresh(): Unit = {}
}
