package me.socure.transactionauditing.client.sqspool

import java.util.concurrent.atomic.AtomicInteger

import atmos.dsl._
import com.amazonaws.handlers.AsyncHandler
import com.amazonaws.services.sqs.model.{SendMessageRequest, SendMessageResult}
import me.socure.transactionauditing.common.config.ClientSqsEndpoint
import me.socure.transactionauditing.sqs.model.SqsMessage

import scala.concurrent.{ExecutionContext, Future}
import scala.concurrent.duration._

class RoundRobinExecuteWithFailover(minBackoff:   Int,
                                    maxBackoff:   Int,
                                    randomFactor: Double,
                                    endpointPool: Seq[ClientSqsEndpoint]) extends ExecuteWithFailover {

  override val index: AtomicInteger = new AtomicInteger(0)

  implicit val exponentialRetryPolicy = retryFor { calculateAttempts(minBackoff, maxBackoff) attempts } using exponentialBackoff { minBackoff millis }
  val endpoints = endpointPool

  def execute(msg: SqsMessage, callback: AsyncHandler[SendMessageRequest, SendMessageResult], operation: (SqsMessage, ClientSqsEndpoint, AsyncHandler[SendMessageRequest, SendMessageResult]) => Future[SendMessageResult])(implicit executionContext: ExecutionContext): Future[SendMessageResult] = {
    retryAsync(){
      operation(msg, endpointPool(index.get()), callback)
    }.recoverWith {
      case e: Throwable =>
        index.set(getNextHostIndex())
        Future.failed(e)
    }
  }

  def getNextHostIndex(): Int = {
    index.incrementAndGet() % endpointPool.length
  }

  def calculateAttempts(
                      minBackoff:   Int,
                      maxBackoff:   Int): Int = {
    val max = Math.log(maxBackoff)/Math.log(2)
    val min = Math.log(minBackoff)/Math.log(2)
    Math.ceil(max/min).toInt
  }
}
