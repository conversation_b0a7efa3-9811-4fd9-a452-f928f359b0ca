package me.socure.transactionauditing.client.sqspool

import java.util.concurrent.atomic.AtomicInteger
import com.amazonaws.handlers.AsyncHandler
import com.amazonaws.services.sqs.model.{SendMessageRequest, SendMessageResult}
import me.socure.transactionauditing.common.config.ClientSqsEndpoint
import me.socure.transactionauditing.sqs.model.SqsMessage

import scala.concurrent.{ExecutionContext, Future}

abstract class ExecuteWithFailover {
  def execute(msg: SqsMessage, callback: AsyncHandler[SendMessageRequest, SendMessageResult], operation: (SqsMessage, ClientSqsEndpoint, AsyncHandler[SendMessageRequest, SendMessageResult]) => Future[SendMessageResult])(implicit executionContext: ExecutionContext): Future[SendMessageResult]

  val index: AtomicInteger
}
