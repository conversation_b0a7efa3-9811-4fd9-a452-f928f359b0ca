package me.socure.transactionauditing.client.provider

import com.amazonaws.auth._
import com.amazonaws.auth.profile.ProfileCredentialsProvider
import com.typesafe.config.Config
import org.slf4j.LoggerFactory

@deprecated
class WrappedCredentialProviderChain(awsCredentialsProvider: AWSCredentialsProvider*) extends AWSCredentialsProviderChain(awsCredentialsProvider: _*)

object WrappedCredentialProviderChain {

  private val logger = LoggerFactory.getLogger(getClass)

  private def defaultChain: Seq[AWSCredentialsProvider] = Seq(new EnvironmentVariableCredentialsProvider(), new SystemPropertiesCredentialsProvider(), new ProfileCredentialsProvider(), new EC2ContainerCredentialsProviderWrapper())

  def apply(config: Config): WrappedCredentialProviderChain = {
    val legacyCredential = if (config.hasPath("transaction-auditing.aws.access.key") && config.hasPath("transaction-auditing.aws.access.secret")) {
      val awsKey: String = config.getString("transaction-auditing.aws.access.key")
      val awsSecret: String = config.getString("transaction-auditing.aws.access.secret")
      logger.debug(s"Legacy credential provider found for transaction audit!")
      Some(
        new AWSStaticCredentialsProvider(new BasicAWSCredentials(awsKey, awsSecret))
      )
    } else {
      None
    }

    val combinedCredentials = (legacyCredential ++ defaultChain).toSeq
    apply(combinedCredentials: _*)
  }

  def apply(awsCredentialProvider: AWSCredentialsProvider*): WrappedCredentialProviderChain = {
    new WrappedCredentialProviderChain(awsCredentialProvider: _*)
  }

  def apply(): WrappedCredentialProviderChain = {
    new WrappedCredentialProviderChain(defaultChain: _*)
  }
}
