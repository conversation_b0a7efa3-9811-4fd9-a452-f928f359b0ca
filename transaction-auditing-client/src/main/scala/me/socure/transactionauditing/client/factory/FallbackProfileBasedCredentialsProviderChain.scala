package me.socure.transactionauditing.client.factory

import com.amazonaws.auth.{AWSCredentialsProviderChain, InstanceProfileCredentialsProvider}
import com.amazonaws.auth.profile.ProfileCredentialsProvider
import com.typesafe.config.Config
import me.socure.transactionauditing.client.provider.FallbackConfigProvider

@Deprecated
class FallbackProfileBasedCredentialsProviderChain(config: Config) extends AWSCredentialsProviderChain (
  new FallbackConfigProvider(config),
  InstanceProfileCredentialsProvider.getInstance(),
  new ProfileCredentialsProvider
)
