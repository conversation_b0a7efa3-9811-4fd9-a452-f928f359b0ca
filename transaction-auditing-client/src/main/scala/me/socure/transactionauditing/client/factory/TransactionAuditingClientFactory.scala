package me.socure.transactionauditing.client.factory

import com.amazonaws.auth.{AWSCredentialsProviderChain, DefaultAWSCredentialsProviderChain}
import com.typesafe.config.Config
import me.socure.common.s3.client.AmazonS3ClientFactory
import me.socure.transactionauditing.client.TransactionAuditingClient
import me.socure.transactionauditing.client.sqspool.RoundRobinExecutorWithFailoverFactory
import me.socure.transactionauditing.common.config.{ClientSqsEndpoint, S3ConfigFactory, SqsBaseClientConfiguration}
import me.socure.transactionauditing.common.factory.ClientSqsEndpointFactory
import me.socure.transactionauditing.common.util.SqsQueueEnums.SqsQueueEnum
import me.socure.transactionauditing.sqs.queue.ProducerQueueFactory

object TransactionAuditingClientFactory {

  def get(config: Config, queueEnum: SqsQueueEnum): TransactionAuditingClient = {
    buildClient(config, queueEnum, DefaultAWSCredentialsProviderChain.getInstance())
  }

  def getInstanceProfile(config: Config, queueEnum: SqsQueueEnum): TransactionAuditingClient = {
    buildClient(config, queueEnum, DefaultAWSCredentialsProviderChain.getInstance())
  }

  private def buildClient(config: Config, queueEnum: SqsQueueEnum, providerChain: AWSCredentialsProviderChain): TransactionAuditingClient = {
    val sqsBaseConfiguration = new SqsBaseClientConfiguration(config, queueEnum)
    val producerQueue = ProducerQueueFactory.getAsync()
    val s3Config = S3ConfigFactory.get(config)
    val amazonS3Client = AmazonS3ClientFactory.get(providerChain)

    val initializedEndpoints: Seq[ClientSqsEndpoint] = sqsBaseConfiguration.endpoints.map(ClientSqsEndpointFactory.get(amazonS3Client, s3Config.largeFileBucket, _, providerChain, queueEnum))
    val roundRobinExecutorWithFailoverFactory = new RoundRobinExecutorWithFailoverFactory(
      initializedEndpoints = initializedEndpoints,
      minBackOff = sqsBaseConfiguration.minBackoff,
      maxBackoff = sqsBaseConfiguration.maxBackoff
    )
    new TransactionAuditingClient(sqsBaseConfiguration, producerQueue, roundRobinExecutorWithFailoverFactory)
  }
}
