withDBMigration=true


threadpool {
  poolSize=100
}

pipeline {

  region = "us-east-1"

  //how many workers will be processing in parallel
  parallelism = 3

#  buffering {
#    //try to buffer upto this count. It could go upto targetCount + (receive.message.count)
#    targetCount = 10 //waits upto maxDuration to receive targetCount, it could be less, exact or more
#    //how long to wait for the targetCount
#    maxDuration = "1 minutes"
#  }

  transaction {
    parallelism = 3
    restart.interval = "24 hours"
    sqs {
      queue.name = "transaction-auditing-prod"
      receive.message.count = 10
      visibility.timeout.seconds = "900" //15 minutes
      large.files.s3.bucket.name = "sqs-storage-prod"
      s3.masking-failures.bucket = "prod-audit-errors/pii_mask_failures"
      s3.parsing-failures.bucket = "prod-audit-errors/parse_failures"
      s3.failures.bucket.key-arn = "arn:aws:kms:us-east-1:************:key/2b77b4f8-1779-4476-a611-5c80ff941442"
      s3.audit-data.bucket = "transaction-audit-data-prod"
    }
  }

  thirdparty {
    parallelism = 20
    restart.interval = "24 hours"
    sqs {
      queue.name = "third-party-transaction-auditing-prod"
      receive.message.count = 10
      visibility.timeout.seconds = "900" //15 minutes
      large.files.s3.bucket.name = "thirdparty-stats-prod"
      large.files.s3.bucket.key-arn = "arn:aws:kms:us-east-1:************:key/5614f298-5696-48d4-b79f-8f1eca5ed285"

      auditdata.sqs {
        queue.name = "thirdparty-audit-data-prod"
        largepayload.s3.bucket.name = "thirdparty-stats-prod"
        largepayload.s3.bucket.key-arn = "arn:aws:kms:us-east-1:************:key/5614f298-5696-48d4-b79f-8f1eca5ed285"
      }
    }
  }

  byok-failure {
    parallelism = 1
    restart.interval = "24 hours"
    sqs {
      queue.name = "TF-transaction-auditing-byok-failures-prod"
      receive.message.count = 10
      visibility.timeout.seconds = "3600" // 1 hour
      large.files.s3.bucket.name = "transaction-auditing-byok-failures-prod"
    }
  }
}

server {
  port=5000
  apiTimeout = "10 minutes"
}

cors.allowedDomains = ["http://swagger.us-east-vpc.socure.be/"]

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<80.0" //should be less than 90%
    non_heap.used_max_percentage = "<80.0" //should be less than 90%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

database {
  audit {
    unified-cluster {
      jdbcUrl="**************************************************************************************************************************************************************************************************************************************************************************************************"
      dataSource.user="audit-2023"
      dataSource.password="""ENC(GnKuWkaGa5HopgB12XNgmeutbmeeXtN9S0UfbFjc2Gk86urYqB68teYIHgZLJYtcX5ROEw==)"""
      driverClassName="com.mysql.cj.jdbc.Driver"
      poolName="ta_r_u_trx"
      maximumPoolSize=25
    }
  }
}

account.service {
  endpoint="http://account-service"
  endpoint2="http://account-service"
  groupName="UXServicesRamp"
  flagName="AccountService_Ramp"

  dynamic.control.center{
    s3 {
      bucketName="globalconfig-prod"
    }
    memcached {
      host="vpc-memcached-prod-2020.ps2mlp.cfg.use1.cache.amazonaws.com"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }

  hmac {
    realm = Socure
    version = "1.0"
    secret.key="""ENC(BrsK1r5yNmghO4setxAicVEikCUXPg9jQgAb1mlGCH7kSybLP3ZDwuPqGeca2PImr/utVOfurHznvHXRv0NDLg==)""" # FIPS encypted value
    ttl=5
    time.interval=5
    strength=512
  }
}

memcached {
  host=vpc-memcached-prod-2020.ps2mlp.cfg.use1.cache.amazonaws.com
  port=11211
  ttl=86400
}

client.specific.encryption {
  encryption.context.account_id.key = "socure_account_id"
  kms.ids {
    "us-east-1" = """arn:aws:kms:us-east-1:************:alias/client-specific-encryption-prod"""
    "us-west-1" = """arn:aws:kms:us-west-1:************:alias/client-specific-encryption-prod"""
  }
  kms.cache.time.seconds=604800
}

#============= Mailgun config =============#
mailgun {
  endpoint = "https://api.mailgun.net/v2/socure.com/messages"
  key = "ENC(AeD7vhdfp8psLdLBY6kmQuHLeF4DzFcPJhyeMV3+3gGC6Ma3cBZF3ap74PDSxAdxOH7GSy3AtytoppffvKNBN9daCI0=)" # FIPS encypted value
  domain = "socure.com"
  from = "Model Monitoring Failure - Prod <<EMAIL>>"
  subject = "Model Monitoring Failure - Prod"
  to = ["<EMAIL>"]
}
#============= Mailgun config =============#

hmac {
  secret.key="""ENC(UEkDlYKOeIYO27QcMu0CMR/1n0HGQZJLkSjkLs5Kiqc6SWBFD2HJ5Ki4rD0PeJFi5Cpx35hyfg==)""" # FIPS encypted value
  ttl=5
  time.interval=5
  strength=512
}

dynamic.control.center{
  s3 {
    bucketName="globalconfig-prod"
  }
  memcached {
    host="vpc-memcached-prod-2020.ps2mlp.cfg.use1.cache.amazonaws.com"
    port=11211
    ttl=86400
  }
  local.cache {
    timeout.minutes=2
  }
}

transaction.search {
  endpoint = "http://transaction-search-service"
  endpoint2="http://transaction-search-service"
  groupName="UXServicesRamp"
  flagName="TransactionSearchService_Ramp"

  hmac {
    realm = Socure
    version = "1.0"
    strength = 512
    aws.secrets.manager.id = "transaction-search-service/prod/hmac/indexing"
    secret.refresh.interval = 5000
  }
}

velocity.search {
  endpoint = "https://socure-velocity-search-prod.us-east-1.elasticbeanstalk.com"
  endpoint2 = "http://velocity-search-service"

  hmac {
    realm = Socure
    version = "1.0"
    strength = 512
    aws.secrets.manager.id = "velocity-search-service/prod/hmac/indexing"
    secret.refresh.interval = 5000
  }

  hmac2 {
    realm = Socure
    version = "1.0"
    strength = 512
    aws.secrets.manager.id = "velocity-search-service/prod/hmac/indexing"
    secret.refresh.interval = 5000
  }

  dynamic.control.center{
    s3 {
      bucketName="globalconfig-prod"
    }
    memcached {
      host="vpc-memcached-prod-2020.ps2mlp.cfg.use1.cache.amazonaws.com"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }
}

device.jwt.token {
  key = "ENC(KsfxUbX23hWikfHK8SS5ZH+0LCXIm8nyojnBvK6l1u2WDIAjXdd1i10SQye+9Meh5RzVC6IC+zmuSMyd)" # FIPS encypted value
}

obfuscate.pii {
  accounts = [1442,2345,6134] #CK Primary, CK Money, CK_PROFILE_SYNC
}
#===================Dynamic Control Center==========================#

dynamic.control.center {
    s3 {
        bucketName = "globalconfig-prod"
      }
    memcached {
        host=vpc-memcached-prod-2020.ps2mlp.cfg.use1.cache.amazonaws.com
        port=11211
        ttl=86400
      }
    local {
      cache.timeout.minutes=2
    }
  }

#===================Dynamic Control Center==========================#

txn.metrics.enabled = false

dynamodb {
  enabled = true
  tableName = "tbl_api_transaction_prod_new"
  client.config {
    maxConcurrency = 100
    writeTimeout = 60 # seconds
  }
}

