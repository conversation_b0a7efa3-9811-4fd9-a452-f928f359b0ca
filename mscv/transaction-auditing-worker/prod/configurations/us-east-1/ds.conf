withDBMigration=true

threadpool {
  poolSize=100
}

pipeline {

  region = "us-east-1"

  parallelism = 8

#  buffering {
#    //try to buffer upto this count. It could go upto targetCount + (receive.message.count)
#    targetCount = 10 //waits upto maxDuration to receive targetCount, it could be less, exact or more
#    //how long to wait for the targetCount
#    maxDuration = "1 minutes"
#  }

  transaction {
    restart.interval = "24 hours"
    sqs {
      queue.name = "transaction-auditing-ds"
      receive.message.count = 10
      visibility.timeout.seconds = "900" //15 minutes
      large.files.s3.bucket.name = "sqs-storage-ds"
      s3.masking-failures.bucket = "datasci-audit-errors/pii_mask_failures"
      s3.parsing-failures.bucket = "datasci-audit-errors/parse_failures"
      s3.failures.bucket.key-arn = "arn:aws:kms:us-east-1:************:key/2575bafc-39f0-4e0c-a0a9-e9398beb486d"
      s3.audit-data.bucket = "transaction-audit-data-ds"
    }
  }

  thirdparty {
    parallelism = 30
    restart.interval = "24 hours"
    sqs {
      queue.name = "third-party-transaction-auditing-ds"
      receive.message.count = 10
      visibility.timeout.seconds = "90" //1.5 minutes
      large.files.s3.bucket.name = "mlpipe"
      base.path = "thirdparty/datascience"
      large.files.s3.bucket.key-arn = "arn:aws:kms:us-east-1:************:key/6a364622-5d67-443c-9b40-74ca900db95d"

      auditdata.sqs {
        queue.name = "thirdparty-audit-data-ds"
        largepayload.s3.bucket.name = "mlpipe/thirdparty/datascience"
        largepayload.s3.bucket.key-arn = "arn:aws:kms:us-east-1:************:key/6a364622-5d67-443c-9b40-74ca900db95d"
      }
    }
  }

  byok-failure {
    parallelism = 1
    restart.interval = "24 hours"
    sqs {
      queue.name = "TF-transaction-auditing-byok-failures-ds"
      receive.message.count = 10
      visibility.timeout.seconds = "3600" // 1 hour
      large.files.s3.bucket.name = "transaction-auditing-byok-failures-ds"
    }
  }
}

server {
  port=5000
  apiTimeout = "10 minutes"
}

cors.allowedDomains = ["http://swagger.us-east-vpc.socure.be/"]

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<80.0" //should be less than 90%
    non_heap.used_max_percentage = "<80.0" //should be less than 90%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

database {
  audit {
    unified-cluster {
      jdbcUrl="***********************************************************************************************************************************************************************************************************************************************************************************************************************************"
      dataSource.user="audit-2018"
      dataSource.password="ENC(n5HegQQT2dvzvb3a0sI3IBC00RJqwS0owA4y3jBK1HXXl1dwy3xt+QwSd/Xoy0rkXsovTA==)"
      driverClassName="com.mysql.cj.jdbc.Driver"
      poolName="ta_w_trx"
      maximumPoolSize=25
    }
  }
}

account.service {
  endpoint = "https://account-service.webapps.us-east-1.ds.socure.link"
  endpoint2 = "https://account-service.webapps.us-east-1.ds.socure.link"
  groupName = "UXServicesRamp"
  flagName = "AccountService_Ramp"

  dynamic.control.center{
    s3 {
      bucketName="globalconfig-ds"
    }
    memcached {
      host="vpc-memcached-ds-2020.ps2mlp.cfg.use1.cache.amazonaws.com"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }

  hmac {
      realm = Socure
      version = "1.0"
      secret.key="""ENC(qEt1fHZKoTqdRIPfd0pu6ir1pZzLqV7BjBfIMXgyfFSr3qwU1ie6KHYYeuIGayQyeR7rFE9kFuUVUU8yvKzwCw==)"""
      ttl=5
      time.interval=5
      strength=512
    }
}

memcached {
  host=vpc-memcached-ds-2020.ps2mlp.cfg.use1.cache.amazonaws.com
  port=11211
  ttl=86400
}

client.specific.encryption {
  encryption.context.account_id.key = "socure_account_id"
  kms.ids {
    "us-east-1" = """arn:aws:kms:us-east-1:************:alias/client-specific-encryption-ds"""
    "us-west-1" = """arn:aws:kms:us-west-1:************:alias/client-specific-encryption-ds"""
  }
  kms.cache.time.seconds=604800
}

#============= Datadog config =============#
datadog {
  series_url = "https://app.datadoghq.com/api/v1/series"
  api_key = """ENC(wH31j6Rejiq/PQ6RaEEL27UwC8WVCNYIX5MCw7qMR+sUR46SbmHu6xBxNMP0K44muXfPc77x9ISbTOfNLeav/Q==)"""
}
#============= Datadog config =============#

#============= Mailgun config =============#
mailgun {
  endpoint = "https://api.mailgun.net/v2/socure.com/messages"
  key = """ENC(vmBIM+Y6sxZmZNBOg5PRn/4BIeQEFFip5fq/kKWRybyT4omOQYhfQVNRv5CkhU3bK2CVsOEdRYJ6RtLnsyJXBcuvakI=)"""
  domain = "socure.com"
  subject = "Model Monitoring Metrics Reporting Failure - DataScience"
  from = "<EMAIL>"
  to = ["<EMAIL>"]
}

hmac {
  secret.key="""ENC(axjBI1WKCIxYscgwNIzUSK2nmtSYyLdlpYLaYARi0Qqg2bD5Dee0AzYS8YAeTaXKDtSDLFKo2ah3/xk=)"""
  ttl=5
  time.interval=5
  strength=512
}

transaction.search {
  endpoint = "https://transaction-search-ds.us-east-1.elasticbeanstalk.com"
  indexing.enabled = false
  hmac {
    realm = Socure
    version = "1.0"
    strength = 512
    aws.secrets.manager.id = "transaction-search-service/ds/hmac/indexing"
    secret.refresh.interval = 5000
  }
}

device.jwt.token {
  key = "ENC(bPNOcFBZBV3uWA1mdhXVAIiTxMhjaBVdmxL5QylajNZsQSZg1xQjYpS7lbShusGG3jgnz3BxgsUrz2Nv)"
}
#===================Dynamic Control Center==========================#

dynamic.control.center {
    s3 {
        bucketName = "globalconfig-ds"
      }
    memcached {
        host=vpc-memcached-ds-2020.ps2mlp.cfg.use1.cache.amazonaws.com
        port=11211
        ttl=86400
      }
    local {
      cache.timeout.minutes=2
    }
  }

#===================Dynamic Control Center==========================#

use.old.transaction.audit.table=true

dynamodb {
  enabled = true
  tableName = "tbl_api_transaction_ds"
  client.config {
    maxConcurrency = 100
    writeTimeout = 60 # seconds
  }
}


