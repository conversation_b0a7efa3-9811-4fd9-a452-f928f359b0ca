withDBMigration=true


threadpool {
  poolSize=10
}

pipeline {

  region = "us-gov-west-1"

  //how many workers will be processing in parallel
  parallelism = 3

  transaction {
    parallelism = 3
    restart.interval = "24 hours"
    sqs {
      queue.name = "transaction-auditing-stage"
      receive.message.count = 10
      visibility.timeout.seconds = "900" //15 minutes
      large.files.s3.bucket.name = "sqs-storage-stage-************-us-gov-west-1"
      s3.masking-failures.bucket = "stage-audit-errors-************-us-gov-west-1/pii_mask_failures"
      s3.parsing-failures.bucket = "stage-audit-errors-************-us-gov-west-1/parse_failures"
      s3.failures.bucket.key-arn = "arn:aws-us-gov:kms:us-gov-west-1:************:key/d3d39577-64b4-4c70-8d60-b89efe436086"
      s3.audit-data.bucket = "transaction-audit-data-stage-************-us-gov-west-1"
    }
  }

  thirdparty {
    parallelism = 20
    restart.interval = "24 hours"
    sqs {
      queue.name = "third-party-transaction-auditing-stage"
      receive.message.count = 10
      visibility.timeout.seconds = "900" //15 minutes
      large.files.s3.bucket.name = "thirdparty-stats-stage-************-us-gov-west-1"
      large.files.s3.bucket.key-arn = "arn:aws-us-gov:kms:us-gov-west-1:************:key/mrk-2356a80ff56844a8b735c80bd694fa05"

      auditdata.sqs {
        queue.name = "thirdparty-audit-data-stage"
        largepayload.s3.bucket.name = "thirdparty-stats-stage-************-us-gov-west-1"
        largepayload.s3.bucket.key-arn = "arn:aws-us-gov:kms:us-gov-west-1:************:key/mrk-2356a80ff56844a8b735c80bd694fa05"
      }
    }
  }

  byok-failure {
    parallelism = 1
    restart.interval = "24 hours"
    sqs {
      queue.name = "transaction-auditing-byok-failures-stage"
      receive.message.count = 10
      visibility.timeout.seconds = "3600" // 1 hour
      large.files.s3.bucket.name = "transaction-auditing-failures-************-us-gov-west-1"
    }
  }
}

server {
  port=5000
  apiTimeout = "10 minutes"
}

cors.allowedDomains = ["http://swagger.us-east-vpc.socure.be/"]

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<80.0" //should be less than 90%
    non_heap.used_max_percentage = "<80.0" //should be less than 90%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

database {
  audit {
    unified-cluster {
      jdbcUrl="**************************************************************************************************************************************************************************************************************************************************************************************************************************"
      dataSource.user="audit-2023"
      dataSource.password="""ENC(v3+BpCO6CYwoxPXA7EMzDrNK95xiFtSKuvP108HPrKHoCNI/tPwPa7x+SB+yAFSY2cz/qg==)"""
      driverClassName="com.mysql.cj.jdbc.Driver"
      poolName="ta_w_trx"
      maximumPoolSize=25
    }
  }
}


account.service {
  endpoint="http://account-service"
  endpoint2="http://account-service"
  groupName="UXServicesRamp"
  flagName="AccountService_Ramp"

  dynamic.control.center{
    s3 {
      bucketName="globalconfig-************-us-gov-west-1"
    }
    memcached {
      host="memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }

  hmac {
    realm = Socure
    version = "1.0"
    secret.key="ENC(9XDolTgXmvchUH6JLZe1uPa1xemvTPFrkd1hU1q3wSlxntaxuL875MdQYJCiAUWRtii/m90moJ4=)"
    ttl=5
    time.interval=5
    strength=512
  }
}

memcached {
  host="memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com"
  port=11211
  ttl=86400
}

client.specific.encryption {
  encryption.context.account_id.key = "socure_account_id"
  kms.ids {
    "us-gov-west-1" = "arn:aws-us-gov:kms:us-gov-west-1:************:alias/socure/client-specific-encryption-stage"
    # "us-west-1" = "{REPLACE}", only 1 key?
  }
  kms.cache.time.seconds=604800
}

#============= Datadog config =============#
datadog {
  series_url = "https://app.datadoghq.com/api/v1/series"
  api_key = "ENC(qgNnHWUSWNDOgiH/Kc8++ALTo4/MJr/se0v7oIqBrmuoVrSauwAlWiMR/1KCE4KZBxqBs54o6AnBhQ5J2Agvmg==)"
}
#============= Datadog config =============#

#============= Mailgun config =============#
mailgun {
  endpoint = "https://api.mailgun.net/v2/socure.com/messages"
  key = "ENC(pL+mnPaME+4AXhBENLNuGMmc4uMwMCFv+Dxqj16OsktspZ0/fI9lb62KxwP4tGONHaJeaWyOfbLM2gP7G5P7qbXxsrE=)"
  domain = "socure.com"
  from = "Model Monitoring Failure - Stage <<EMAIL>>"
  subject = "Model Monitoring Failure - Stage"
  to = ["<EMAIL>"]
}
#============= Mailgun config =============#

hmac {
  secret.key="ENC(5LbwG8ViVtqc26VdFnLB32KCdHJcUgkfRkQe9+u8+d8mSvg4cm2LCS1DMjyL+HzihT3zn2rxIZipvWTJguZlEA==)"
  ttl=5
  time.interval=5
  strength=512
}

transaction.search {
  endpoint = "http://transaction-search-service"
  endpoint2="http://transaction-search-service"
  groupName="UXServicesRamp"
  flagName="TransactionSearchService_Ramp"

  hmac {
    realm = Socure
    version = "1.0"
    strength = 512
    aws.secrets.manager.id = "transaction-search-service/stage/hmac/indexing-235d7e4501"
    secret.refresh.interval = 5000
  }
}

device.jwt.token {
  key = "ENC(wHPBxvCF/vfvTLSXHohaSQq1wVArlZpiinDSUfQDCS9lS2KTEgHOlWNBMW7Ym0TSsc5DmIvNoQUWuNry)"
}

obfuscate.pii {
  accounts = [1442,2345] #CK Primary, CK Money
}
#===================Dynamic Control Center==========================#

dynamic.control.center {
    s3 {
        bucketName = "globalconfig-************-us-gov-west-1"
      }
    memcached {
        host="memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com"
        port=11211
        ttl=86400
      }
    local {
      cache.timeout.minutes=2
    }
  }

#===================Dynamic Control Center==========================#

dynamodb {
  enabled = false
  tableName = "tbl_api_transaction_stage"
  client.config {
    maxConcurrency = 100
    writeTimeout = 60 # seconds
  }
}

