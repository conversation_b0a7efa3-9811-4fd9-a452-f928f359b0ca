<?xml version='1.0' encoding='UTF-8'?>
<configuration>
  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>DEBUG</level>
      <onMatch>DENY</onMatch>
    </filter>
    <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
      <providers>
        <timestamp/>
        <mdc/>
        <context/>
        <logLevel/>
        <loggerName/>
        <threadName/>
        <message/>
        <stackTrace/>
      </providers>
    </encoder>
  </appender>
  <appender name="STDERR" class="ch.qos.logback.core.ConsoleAppender">
    <target>System.err</target>
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
      <level>DEBUG</level>
    </filter>
    <encoder class="net.logstash.logback.encoder.LoggingEventCompositeJsonEncoder">
      <providers>
        <timestamp/>
        <mdc/>
        <context/>
        <callerData/>
        <logLevel/>
        <loggerName/>
        <threadName/>
        <message/>
        <stackTrace/>
      </providers>
    </encoder>
  </appender>
  <root level="INFO">
    <appender-ref ref="STDOUT"/>
    <appender-ref ref="STDERR"/>
  </root>
  <!-- Setting the log level to ERROR  -->
  <logger name="net.spy.memcached.ConfigurationPoller" level="DEBUG" additivity="false">
    <appender-ref ref="STDERR"/>
  </logger>
  <!--  Silence HTTP logging-->
  <logger name="org.apache.http" level="DEBUG" additivity="false">
    <appender-ref ref="STDOUT"/>
  </logger>
  <logger name="httpclient" level="DEBUG" additivity="false">
    <appender-ref ref="STDOUT"/>
  </logger>
</configuration>
