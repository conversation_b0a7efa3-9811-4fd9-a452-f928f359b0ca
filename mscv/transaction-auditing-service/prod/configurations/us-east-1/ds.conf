cors.allowedDomains = ["http://swagger.us-east-vpc.socure.be"]

withDBMigration=false

threadpool {
  poolSize=300
}

longtext.column.reader {
  num.threads = 40
  batch.size = 30
}

transaction-auditing {
  rest {
    maxRowFetch=100
  }

  server {
    port=5000
    apiTimeout = "10 minutes"
  }
}

hmac {
  secret.key="""ENC(oIiWVv9bQssjFQKua6Ne1mLbemlHxWo+guVdqnVToe5oBTvH2GRuPdOcwaoZVAWQf+ly2CKXwnrvxME=)"""
  ttl=5
  time.interval=5
  strength=512
}

jmx {
  port = 1098
}

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<80.0" // should be less than 90%
    non_heap.used_max_percentage = "<80.0" // should be less than 90%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

database {
  audit {
    unified-cluster {
      jdbcUrl="********************************************************************************************************************************************************************************************************************************************************************************************************"
      dataSource.user="audit-2018"
      dataSource.password="ENC(n5HegQQT2dvzvb3a0sI3IBC00RJqwS0owA4y3jBK1HXXl1dwy3xt+QwSd/Xoy0rkXsovTA==)"
      driverClassName="com.mysql.cj.jdbc.Driver"
      poolName="ta_r_trx"
      readOnly=true
      maximumPoolSize=50
    }
  }
}

client.specific.encryption {
  encryption.context.account_id.key = "socure_account_id"
  kms.ids {
    "us-east-1" = """arn:aws:kms:us-east-1:************:alias/client-specific-encryption-ds"""
    "us-west-1" = """arn:aws:kms:us-west-1:************:alias/client-specific-encryption-ds"""
  }
  kms.cache.time.seconds=604800
}

account.service {
  endpoint = "https://account-service.webapps.us-east-1.ds.socure.link"
  hmac {
    realm = Socure
    version = "1.0"
    secret.key="""ENC(qEt1fHZKoTqdRIPfd0pu6ir1pZzLqV7BjBfIMXgyfFSr3qwU1ie6KHYYeuIGayQyeR7rFE9kFuUVUU8yvKzwCw==)"""
    ttl=5
    time.interval=5
    strength=512
  }
}

accountService {
  endpoint = "https://account-service.webapps.us-east-1.ds.socure.link"
  hmac {
    realm = Socure
    version = "1.0"
    secret.key="""ENC(qEt1fHZKoTqdRIPfd0pu6ir1pZzLqV7BjBfIMXgyfFSr3qwU1ie6KHYYeuIGayQyeR7rFE9kFuUVUU8yvKzwCw==)"""
    ttl=5
    time.interval=5
    strength=512
  }
}

memcached {
  host=vpc-memcached-ds-2020.ps2mlp.cfg.use1.cache.amazonaws.com
  port=11211
}

#============= Datadog config =============#
datadog {
  series_url = "https://app.datadoghq.com/api/v1/series"
  api_key = """ENC(OmOTpsvy06KR+uhp+kp53+XQkEyK6dujIVtwzMqyGgaC7HV4vqFZhnaelb8EHKShc/y0jvL/VBpUZMGbfFOqbQ==)"""
}
#============= Datadog config =============#
#===================Dynamic Control Center==========================#

dynamic.control.center {
    s3 {
        bucketName = "globalconfig-ds"
      }
    memcached {
        host=vpc-memcached-ds-2020.ps2mlp.cfg.use1.cache.amazonaws.com
        port=11211
        ttl=86400
      }
    local {
      cache.timeout.minutes=2
    }
  }

#===================Dynamic Control Center==========================#

use.old.transaction.audit.table=true

mask.response.fields = ["kycPlus.bestMatchedEntity.dob", "kycPlus.bestMatchedEntity.ssn", "deceasedCheck.dob", "deceasedCheck.nationalId"]

transaction.audit.bucket.name = transaction-audit-data-ds

dynamodb {
  enabled = false
  table {
    name = "tbl_api_transaction_ds"
    partition.key = "transactionId"
    gsi {
      accountIdYearMonth.index.name = "accountIdYearMonth-index"
      customerUserIdAccountId.index.name = "customerUserIdAccountId-index"
    }
  }
  client {
    config {
      maxConcurrency = 100
      readTimeout = 20000
    }
  }
  max.records.fetch.limit = 500
}


