cors.allowedDomains = ["http://swagger.us-east-vpc.socure.be"]

withDBMigration=false

threadpool {
  poolSize=300
}

longtext.column.reader {
  num.threads = 40
  batch.size = 30
}

transaction-auditing {
  rest {
    maxRowFetch=100
  }

  server {
    port=5000
    apiTimeout = "10 minutes"
  }
}

hmac {
  secret.key="""ENC(A3Gr2VpcJ/BrfWNLsd+z5FnbF8RmXPmhXO6sbn6hRhyjvr/qoB0VFCfBVI6T7yja5HmZreSAXQ==)""" #FIPS Encrypted Value
  ttl=5
  time.interval=5
  strength=512
}

jmx {
  port = 1098
}

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<80.0" // should be less than 90%
    non_heap.used_max_percentage = "<80.0" // should be less than 90%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

database {
  audit {
    unified-cluster {
      jdbcUrl="**********************************************************************************************************************************************************************************************************************************************************************************************************************"
      dataSource.user="audit-2023"
      dataSource.password="""ENC(GnKuWkaGa5HopgB12XNgmeutbmeeXtN9S0UfbFjc2Gk86urYqB68teYIHgZLJYtcX5ROEw==)"""
      driverClassName="com.mysql.cj.jdbc.Driver"
      poolName="ta_r_u_trx"
      readOnly=true
      maximumPoolSize=25
    }
  }
}

client.specific.encryption {
  encryption.context.account_id.key = "socure_account_id"
  kms.ids {
    "us-east-2" = """arn:aws:kms:us-east-2:************:alias/socure/client-specific-encryption-prod"""
    "us-west-1" = """arn:aws:kms:us-west-1:************:alias/client-specific-encryption-prod"""
    "us-east-1" = """arn:aws:kms:us-east-1:************:alias/client-specific-encryption-prod"""
  }
  kms.cache.time.seconds=604800
}

account.service {
  endpoint="http://account-service"
  endpoint2="http://account-service"
  groupName="UXServicesRamp"
  flagName="AccountService_Ramp"

  dynamic.control.center{
    s3 {
      bucketName="pltv2-ue2-globalconfig-************-us-east-2"
    }
    memcached {
      host="pltv2-ue2-memcached-26e24e.y5vd22.cfg.use2.cache.amazonaws.com"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }

  hmac {
    realm = Socure
    version = "1.0"
    secret.key="""ENC(BrsK1r5yNmghO4setxAicVEikCUXPg9jQgAb1mlGCH7kSybLP3ZDwuPqGeca2PImr/utVOfurHznvHXRv0NDLg==)""" #FIPS Encrypted Value
    ttl=5
    time.interval=5
    strength=512
  }
}

memcached {
  host=pltv2-ue2-memcached-26e24e.y5vd22.cfg.use2.cache.amazonaws.com
  port=11211
}

#===================Dynamic Control Center==========================#

dynamic.control.center {
    s3 {
        bucketName = "pltv2-ue2-globalconfig-************-us-east-2"
      }
    memcached {
        host=pltv2-ue2-memcached-26e24e.y5vd22.cfg.use2.cache.amazonaws.com
        port=11211
        ttl=86400
      }
    local {
      cache.timeout.minutes=2
    }
  }

#===================Dynamic Control Center==========================#

mask.response.fields = ["kycPlus.bestMatchedEntity.dob", "kycPlus.bestMatchedEntity.ssn", "deceasedCheck.dob", "deceasedCheck.nationalId"]

transaction.audit.bucket.name = transaction-audit-data-prod-************-us-east-2

server.metrics.enabled = false

dynamodb {
  enabled = true
  table {
    name = "tbl_api_transaction_prod_new"
    partition.key = "transactionId"
    gsi {
      accountIdYearMonth.index.name = "accountIdYearMonth-index"
      customerUserIdAccountId.index.name = "customerUserIdAccountId-index"
    }
  }
  client {
    config {
      maxConcurrency = 100
      readTimeout = 20000
    }
  }
  max.records.fetch.limit = 500
}

