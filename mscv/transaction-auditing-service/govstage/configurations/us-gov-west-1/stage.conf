cors.allowedDomains = ["http://swagger.us-east-vpc.socure.be"]

withDBMigration=false

threadpool {
  poolSize=50
}

transaction-auditing {
  rest {
    maxRowFetch=100
  }

  server {
    port=5000
    apiTimeout = "10 minutes"
  }
}

hmac {
  secret.key="""ENC(BRnrs8IEraufZTL4VPCDtRY8ojvdP9iod7aDJPnSkkv5jDq7iYznF5WndTYHojk1HGjVg2OLn5AV)"""
  ttl=5
  time.interval=5
  strength=512
}

jmx {
  port = 1098
}

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<80.0" // should be less than 90%
    non_heap.used_max_percentage = "<80.0" // should be less than 90%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

database {
  audit {
    unified-cluster {
      jdbcUrl="jdbc-secretsmanager:mysql://transaction-audit-stage.cluster-ro-cvuthvaf2kni.us-gov-west-1.rds.amazonaws.com/socureaudit?useUnicode=true&characterEncoding=UTF-8&loadBalanceBlacklistTimeout=5000&loadBalanceConnectionGroup=socureauditgroup&loadBalanceEnableJMX=true&autoReconnect=true&autoReconnectForPools=true&sslMode=VERIFY_IDENTITY"
      dataSource.user="rds-transaction-audit-stage-8bf4cb-app"
      driverClassName="com.amazonaws.secretsmanager.sql.AWSSecretsManagerMySQLDriver"
      poolName="ta_r_trx"
      readOnly=true
      maximumPoolSize=25
    }
    thirdparty-cluster {
      jdbcUrl="jdbc-secretsmanager:mysql://thirdparty-audit-stage.cluster-ro-cvuthvaf2kni.us-gov-west-1.rds.amazonaws.com/thirdpartyaudit?useUnicode=true&characterEncoding=UTF-8&loadBalanceBlacklistTimeout=5000&loadBalanceConnectionGroup=thirdpartygroup&loadBalanceEnableJMX=true&autoReconnect=true&autoReconnectForPools=true&sslMode=VERIFY_IDENTITY"
      dataSource.user="rds-thirdparty-audit-stage-58c150-app"
      driverClassName="com.amazonaws.secretsmanager.sql.AWSSecretsManagerMySQLDriver"
      poolName="ta_r_tp"
      readOnly=true
      maximumPoolSize=25
    }
  }
}

client.specific.encryption {
  encryption.context.account_id.key = "socure_account_id"
  kms.ids {
    "us-gov-west-1" = "arn:aws-us-gov:kms:us-gov-west-1:************:alias/socure/client-specific-encryption-stage"
  }
  kms.cache.time.seconds=604800
}

account.service {
  endpoint="http://account-service"
  endpoint2="http://account-service"
  groupName="UXServicesRamp"
  flagName="AccountService_Ramp"

  dynamic.control.center{
    s3 {
      bucketName="globalconfig-************-us-gov-west-1"
    }
    memcached {
      host="memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }

  hmac {
    realm = Socure
    version = "1.0"
    secret.key="ENC(9XDolTgXmvchUH6JLZe1uPa1xemvTPFrkd1hU1q3wSlxntaxuL875MdQYJCiAUWRtii/m90moJ4=)"
    ttl=5
    time.interval=5
    strength=512
  }
}

memcached {
  host=memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com
  port=11211
}

#============= Datadog config =============#
datadog {
  series_url = "https://app.datadoghq.com/api/v1/series"
  api_key = "ENC(ITn9O+1o85paWh1fald9cBvU/TB9c3RNo5dIXdTHCXNSArAqu61mRHKWE4pDYv4EMV4cThKL3odUiHbe9A1Zpw==)"
}
#============= Datadog config =============#
#===================Dynamic Control Center==========================#

dynamic.control.center {
  s3 {
    bucketName = "globalconfig-************-us-gov-west-1"
  }
  memcached {
    host=memcached-4648b7.rrznfx.cfg.usgw1.cache.amazonaws.com
    port=11211
    ttl=86400
  }
  local {
    cache.timeout.minutes=2
  }
}

#===================Dynamic Control Center==========================#

mask.response.fields = ["kycPlus.bestMatchedEntity.dob", "kycPlus.bestMatchedEntity.ssn"]

transaction.audit.bucket.name = "transaction-audit-data-stage-************-us-gov-west-1"

dynamodb {
  enabled = false
  table {
    name = "tbl_api_transaction_stage"
    partition.key = "transactionId"
    gsi {
      accountIdYearMonth.index.name = "accountIdYearMonth-index"
      customerUserIdAccountId.index.name = "customerUserIdAccountId-index"
    }
  }
  client {
    config {
      maxConcurrency = 100
      readTimeout = 20000
    }
  }
  max.records.fetch.limit = 500
}

