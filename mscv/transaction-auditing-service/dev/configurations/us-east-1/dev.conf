cors.allowedDomains = ["http://swagger.us-east-vpc.socure.be"]

withDBMigration=false

threadpool {
  poolSize=200
}

longtext.column.reader {
  num.threads = 30
  batch.size = 20
}

transaction-auditing {
  rest {
    maxRowFetch=100
  }

  server {
    port=5000
    apiTimeout = "10 minutes"
  }
}

hmac {
  secret.key="""ENC(HJRRpM5+NopLMrp3dkdUOrCq4+6E3cr0iIAmoqlG1l8GywwqfFlNp8H/z1ifOa4Z8Gg0HZIRG2s=)"""
  ttl=5
  time.interval=5
  strength=512
}

jmx {
  port = 1098
}

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<80.0" // should be less than 90%
    non_heap.used_max_percentage = "<80.0" // should be less than 90%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

database {
  audit {
    unified-cluster {
      jdbcUrl="**************************************************************************************************************************************************************************************************************************************************************************************************"
      dataSource.user="socureaudit"
      dataSource.password="ENC(4wFU+SbxdHRbBSg147EqoIfE7e2AgK5Q888mxs6evcrrQHTXm/sBxP+FHe6l0UuDD6Vnag==)"
      driverClassName="com.mysql.cj.jdbc.Driver"
      poolName="ta_r_trx"
      readOnly=true
      maximumPoolSize=25
    }
  }
}

client.specific.encryption {
  encryption.context.account_id.key = "socure_account_id"
  kms.ids {
    "us-east-1" = """arn:aws:kms:us-east-1:************:alias/client-specific-encryption-stage"""
    "us-west-1" = """arn:aws:kms:us-west-1:************:alias/client-specific-encryption-stage"""
  }
  kms.cache.time.seconds=604800
}

account.service {
  endpoint = "http://account-service"
  endpoint2 = "http://account-service"
  groupName = "UXServicesRamp"
  flagName = "AccountService_Ramp"

  dynamic.control.center{
    s3 {
      bucketName="globalconfig-************-us-east-1"
    }
    memcached {
      host="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }

  hmac {
    realm = Socure
    version = "1.0"
    secret.key="""ENC(1QNZbWDBudrPOTenVxra8ybfuGFNyoJtgLBnA9+l/6PUWpJ2isaPVPlPl+YB8hV0efpzxIICNZ7und1Q6jSwEw==)"""
    ttl=5
    time.interval=5
    strength=512
  }
}

memcached {
  host=product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link
  port=11211
}

#===================Dynamic Control Center==========================#

dynamic.control.center {
    s3 {
        bucketName = "globalconfig-************-us-east-1"
      }
    memcached {
        host=product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link
        port=11211
        ttl=86400
      }
    local {
      cache.timeout.minutes=2
    }
  }

#===================Dynamic Control Center==========================#

mask.response.fields = ["kycPlus.bestMatchedEntity.dob", "kycPlus.bestMatchedEntity.ssn", "deceasedCheck.dob", "deceasedCheck.nationalId"]

transaction.audit.bucket.name = transaction-audit-data-************-us-east-1

server.metrics.enabled = false

dynamodb {
  enabled = true
  table {
    name = "tbl_api_transaction_dev"
    partition.key = "transactionId"
    gsi {
      accountIdYearMonth.index.name = "accountIdYearMonth-index"
      customerUserIdAccountId.index.name = "customerUserIdAccountId-index"
    }
  }
  client {
    config {
      maxConcurrency = 100
      readTimeout = 20000
    }
  }
  max.records.fetch.limit = 500
}

