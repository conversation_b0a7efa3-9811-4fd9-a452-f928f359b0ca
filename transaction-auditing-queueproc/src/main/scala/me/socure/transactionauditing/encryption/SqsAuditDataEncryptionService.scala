package me.socure.transactionauditing.encryption

import me.socure.transaction.auditing.encryption.common.AuditDataPii
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.transaction.auditing.encryption.encryptor.v2.AuditDataEncryptorV2
import me.socure.transactionauditing.sqs.model.SqsApiTransaction
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class SqsAuditDataEncryptionService(auditDataEncryptor: AuditDataEncryptorV2) {
  private val logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get("client.specific.encryption.audit.encryptor")


  def encryptTransaction(transaction: SqsApiTransaction)(
  implicit ec: ExecutionContext): Future[SqsApiTransaction] = {
    val auditDataPiiPlain = AuditDataPii(
      requestUri = Option(transaction.requestURI),
      parameters = Option(transaction.parameters),
      details = Option(transaction.details),
      response = Option(transaction.response),
      customerUserId = None
    )

    Try {
      auditDataEncryptor.encrypt(
        accountId = transaction.accountId,
        apiKey = transaction.apiKey,
        auditDataPii = auditDataPiiPlain
      ).map(auditDataEncrypted => {
        transaction.copy(
          requestURI = auditDataEncrypted.requestUri.getOrElse(transaction.requestURI),
          parameters = auditDataEncrypted.parameters.getOrElse(transaction.parameters),
          details = auditDataEncrypted.details.getOrElse(transaction.details),
          response = auditDataEncrypted.response.getOrElse(transaction.response)
        )
      })
        .recoverWith({
        case ex: Throwable =>
          logger.error("[CLIENT_SPECIFIC_ENCRYPTION_ERROR]", ex)
          metrics.increment("exception", s"class:${ex.getClass.getSimpleName}")
          throw ex
      })
    } match {
      case Success(transaction) => transaction
      case Failure(ex) => {
        logger.error("[CLIENT_SPECIFIC_ENCRYPTION_ERROR]", ex)
        metrics.increment("exception", s"class:${ex.getClass.getSimpleName}")
        throw ex
      }
    }
  }
}
