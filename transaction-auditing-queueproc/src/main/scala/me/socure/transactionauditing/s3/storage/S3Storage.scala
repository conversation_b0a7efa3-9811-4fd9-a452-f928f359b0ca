package me.socure.transactionauditing.s3.storage


import com.amazonaws.services.s3.model.{ObjectListing, PutObjectResult}
import org.joda.time.DateTime

trait S3Storage {
  def constructS3Key(transactionId: String, apiName: String, date: DateTime, prefix: String, keyType: String): String

  def storeFile(s3Object: S3Object): PutObjectResult

  def getListOfFiles(bucketName: String, prefix: String): ObjectListing
}
