package me.socure.transactionauditing.s3.storage

import java.util.UUID
import me.socure.common.clock.Clock
import me.socure.common.data.core.consumer.DataConsumer
import me.socure.common.data.core.service._
import me.socure.common.retry.strategy.RetryStrategy
import me.socure.common.s3.v2.S3AsyncFiles
import me.socure.common.s3.v2.encryption.S3ServerSideEncryption
import me.socure.transactionauditing.sqs.marshaller.SqsMessageMarshaller
import me.socure.transactionauditing.sqs.model.SqsApiTransaction
import org.joda.time.DateTime
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.s3.S3AsyncClient

import scala.concurrent.{ExecutionContext, Future}

class S3AsyncTransactionStorage(
                                 s3Client: S3AsyncClient,
                                 bucketName: String,
                                 nestedDirectory: Option[String],
                                 clock: Clock,
                                 retryStrategy: RetryStrategy,
                                 s3SSE: S3ServerSideEncryption
                               )(implicit ec: ExecutionContext) {
  private val logger = LoggerFactory.getLogger(getClass)
  private val s3Files = new S3AsyncFiles(s3Client)

  def storeInvalidTransaction(rawSqsMessage: String): Future[Unit] = {
    val key = constructS3Key(null, null, clock.now(), "invalid", UUID.randomUUID().toString)

    upload()
      .retry(retryStrategy)
      .withLogging(
        logger = logger,
        inToStr = op =>  s"uploading ${op._1}"
      ).apply((key, rawSqsMessage))

  }

  def storeTransaction(transaction: SqsApiTransaction): Future[Unit] = {
    val rawMessage = SqsMessageMarshaller.marshal(transaction)
    val key = constructS3Key(transaction.transactionId, transaction.apiName, new DateTime(transaction.transactionDate), "valid", "transaction.txt")

    upload()
      .retry(retryStrategy)
      .withLogging(
        logger = logger,
        inToStr = op =>  s"uploading parsable transaction${op._1}"
      ).apply((key, rawMessage))
  }

  private def constructS3Key(transactionId: String, apiName: String, date: DateTime, prefix: String, keyType: String): String = {

    val bucketUrl = new StringBuffer

    nestedDirectory.foreach { nest =>
      bucketUrl.append(nest)
      bucketUrl.append("/")
    }

    val s3Date = date.toString("yyyy-MM-dd")
    bucketUrl.append(s3Date)
    bucketUrl.append("/")
    bucketUrl.append(if (transactionId != null && transactionId.length > 0) transactionId
    else "UnknownTransaction")
    bucketUrl.append("/")
    bucketUrl.append(if (apiName != null) apiName
    else "UnknownService")
    bucketUrl.append("/")
    bucketUrl.append(prefix)
    bucketUrl.append("_")
    bucketUrl.append(keyType)
    bucketUrl.toString
  }

  private def upload(): DataConsumer[(String, String)] = {
    DataConsumer { case (key, content) =>
      s3Files.upload(content, bucketName, key, s3SSE).map(_ => Unit)
    }
  }
}
