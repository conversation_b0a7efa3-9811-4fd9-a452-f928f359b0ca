package me.socure.transactionauditing.s3.storage

import java.io.ByteArrayInputStream
import java.nio.file.Paths
import com.amazonaws.services.s3.AmazonS3Client
import com.amazonaws.services.s3.model.{ObjectListing, ObjectMetadata, PutObjectResult}
import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.s3.bucket.BucketName
import me.socure.common.s3.encryption.S3ServerSideEncryption
import me.socure.common.s3.files.S3Files
import org.joda.time.DateTime

class S3TransactionStorage(awsS3Client: AmazonS3Client, homeBucket: String, s3SSE: S3ServerSideEncryption) extends S3Storage {
  private val logger = TransactionAwareLoggerFactory.getLogger(getClass)
  private val DEFAULT_ERROR_FOLDER = "thirdparty-stats-local"
  val s3Files = new S3Files(awsS3Client, BucketName(homeBucket))

  override def constructS3Key(transactionId: String, apiName: String, date: DateTime, prefix: String, keyType: String): String = {
    val bucketUrl = new StringBuffer
    val s3Date = date.toString("yyyy-MM-dd")
    bucketUrl.append(s3Date)
    bucketUrl.append("/")
    bucketUrl.append(if (transactionId != null && transactionId.length > 0) transactionId
    else "UnknownTransaction")
    bucketUrl.append("/")
    bucketUrl.append(if (apiName != null) apiName
    else "UnknownService")
    bucketUrl.append("/")
    bucketUrl.append(prefix)
    bucketUrl.append("_")
    bucketUrl.append(keyType)
    bucketUrl.toString
  }

  override def storeFile(s3Object: S3Object): PutObjectResult = {
    val meta = new ObjectMetadata()
    val dataBytes = s3Object.data.getBytes()
    meta.setContentLength(dataBytes.length)
    s3Files.upload(
      stream = new ByteArrayInputStream(dataBytes),
      s3FilePath = Paths.get(s3Object.key),
      encryption = s3SSE,
      meta = meta
    )
  }

  override def getListOfFiles(bucketName: String, prefix: String): ObjectListing = {
    s3Files.list(Paths.get(bucketName, prefix))
  }
}
