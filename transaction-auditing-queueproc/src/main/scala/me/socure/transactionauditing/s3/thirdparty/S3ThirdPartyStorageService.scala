package me.socure.transactionauditing.s3.thirdparty


import akka.stream.Materializer
import com.amazonaws.services.s3.AmazonS3Client
import com.amazonaws.services.s3.model.PutObjectResult
import me.socure.common.clock.RealClock
import me.socure.common.s3.bucket.BucketName
import me.socure.common.s3.encryption.KMSManaged
import me.socure.common.s3.files.S3Files
import me.socure.transactionauditing.s3.storage.{S3Object, S3TransactionStorage}
import me.socure.transactionauditing.sqs.model.SqsThirdPartyTransaction
import org.joda.time.DateTime

import scala.concurrent.{ExecutionContext, Future}

class S3ThirdPartyStorageService(awsS3Client: AmazonS3Client, homeBucket: String) {
  val s3Files = new S3Files(awsS3Client, BucketName(homeBucket))
  val clock = new RealClock()

  def saveThirdPartyTransaction(tpTransaction: SqsThirdPartyTransaction, timeStamp: Option[DateTime])(
    implicit ec: ExecutionContext,
    materializer: Materializer
  ): Future[Seq[Option[PutObjectResult]]] = {
    val serviceId = tpTransaction.serviceId
    val storageEngine = new S3TransactionStorage(awsS3Client, homeBucket, KMSManaged.default)
    val transactionDate: DateTime = timeStamp.getOrElse(clock.now())

    val saveRequestFuture = Future {
      tpTransaction.request.map(req => {
        val key = storageEngine.constructS3Key(
          tpTransaction.transactionId,
          null,
          timeStamp.getOrElse(transactionDate),
          tpTransaction.id.getOrElse(0L).toString,
          "request.txt"
        )
        storageEngine.storeFile(S3Object(homeBucket, key, req))
      })
    }

    val saveRequestBodyFuture = Future {
      tpTransaction.request_body.map(reqBody => {
        val key = storageEngine.constructS3Key(
          tpTransaction.transactionId,
          null,
          timeStamp.getOrElse(transactionDate),
          tpTransaction.id.getOrElse(0L).toString,
          "request_body.txt"
        )
        storageEngine.storeFile(S3Object(homeBucket, key, reqBody))
      })
    }

    val saveResponseFuture = Future {
      tpTransaction.response.map(response => {
      val key = storageEngine.constructS3Key(
        tpTransaction.transactionId,
        null,
        timeStamp.getOrElse(transactionDate),
        tpTransaction.id.getOrElse(0L).toString,
        "response.json"
      )
      storageEngine.storeFile(S3Object(homeBucket, key, response))
    })
    }
    Future.sequence(
      Seq(saveRequestFuture, saveRequestBodyFuture, saveResponseFuture)
    )
  }
}
