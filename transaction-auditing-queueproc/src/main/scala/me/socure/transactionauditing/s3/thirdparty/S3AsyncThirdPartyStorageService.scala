package me.socure.transactionauditing.s3.thirdparty

import me.socure.common.clock.Clock
import me.socure.common.s3.v2.S3AsyncFiles
import me.socure.common.s3.v2.encryption.S3ServerSideEncryption
import me.socure.transactionauditing.sqs.model.SqsThirdPartyTransaction
import org.joda.time.DateTime
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.s3.S3AsyncClient

import scala.concurrent.{ExecutionContext, Future}

@Deprecated
class  S3AsyncThirdPartyStorageService(s3Client: S3AsyncClient,
                                      bucketName: String,
                                      nestedDirectory: Option[String],
                                      clock: Clock,
                                      s3SSE: S3ServerSideEncryption)
                                     (implicit ec: ExecutionContext) {
  private val logger = LoggerFactory.getLogger(getClass)
  private val s3Files = new S3AsyncFiles(s3Client)

  def saveThirdPartyTransactions(tpTransactions: Seq[(SqsThirdPartyTransaction, Option[DateTime])]): Future[Unit] = {
    val process = tpTransactions.map {
      case (trx, optDate) if trx.serviceId != 9 /* FORM_VAL service id */ =>

        val request = trx.request.map {
          req =>
            (buildKey(trx, optDate, "request.txt"), req)
        }

        val requestBody = trx.request_body.map {
          reqBody =>
            (buildKey(trx, optDate, "request_body.txt"), reqBody)
        }

        val response = trx.response.map {
          res =>
            (buildKey(trx, optDate, "response.json"), res)
        }

        val batches = Seq(request, requestBody, response).flatMap {
          a =>
            a.map {
              case (key, content) =>
                s3Files.upload(content, bucketName, key, s3SSE)
            }
        }
        Future.sequence(batches).map(_ => Seq.empty[Unit]) // We don't do anything with the result
      case _ =>
        Future.successful(Seq.empty[Unit])
    }
    Future.sequence(process).map(_ => ())
  }

  private[s3] def buildKey(tpTransaction: SqsThirdPartyTransaction, optDate: Option[DateTime], keyName: String): String = {
    constructS3Key(
      transactionId = tpTransaction.transactionId,
      apiName = null,
      date = optDate.getOrElse(clock.now()),
      prefix = tpTransaction.id.getOrElse(0L).toString,
      keyType = keyName
    )
  }

  private def constructS3Key(transactionId: String, apiName: String, date: DateTime, prefix: String, keyType: String): String = {

    val bucketUrl = new StringBuffer

    nestedDirectory.foreach{nest =>
      bucketUrl.append(nest)
      bucketUrl.append("/")
    }

    val s3Date = date.toString("yyyy-MM-dd")
    bucketUrl.append(s3Date)
    bucketUrl.append("/")
    bucketUrl.append(if (transactionId != null && transactionId.length > 0) transactionId
    else "UnknownTransaction")
    bucketUrl.append("/")
    bucketUrl.append(if (apiName != null) apiName
    else "UnknownService")
    bucketUrl.append("/")
    bucketUrl.append(prefix)
    bucketUrl.append("_")
    bucketUrl.append(keyType)
    bucketUrl.toString
  }
}

object S3AsyncThirdPartyStorageService {
  def parseBucketName(bucketName: String): (String, Option[String]) = {

    bucketName.split("/").toList match {
      case h :: t => (h, Option(t.mkString("/")).filter(_.trim.nonEmpty))
      case _ => throw new Exception("third party bucketname shouldnot be emtpy")
    }
  }
}
