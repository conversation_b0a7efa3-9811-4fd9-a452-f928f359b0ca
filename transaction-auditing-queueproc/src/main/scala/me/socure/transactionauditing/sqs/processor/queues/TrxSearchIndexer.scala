package me.socure.transactionauditing.sqs.processor.queues

import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.options._
import me.socure.common.transaction.id.TrxId
import me.socure.transaction.search.client.common.HttpRes
import me.socure.transaction.search.client.indexer.TrxIndexerClient
import me.socure.transaction.search.index.model._
import me.socure.transactionauditing.sqs.model.SqsApiTransaction
import me.socure.transactionauditing.sqs.processor.queues.TrxSearchIndexer.TrxSearchIndexingException
import me.socure.transaction.search.index.model.BulkIndexFailures
import me.socure.transactionauditing.common.constants.Constants
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.{DefaultFormats, _}
import org.json4s.jackson.JsonMethods._

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import scala.util.control.NonFatal

class TrxSearchIndexer(trxSearchIndexer: TrxIndexerClient)(implicit ec: ExecutionContext) {

  private val logger = TransactionAwareLoggerFactory.getLogger(getClass)

  implicit val formats: DefaultFormats.type = DefaultFormats

  private def isAboveV3(apiNameOpt: Option[String])(implicit trxId: TrxId): Boolean = {
    apiNameOpt match {
      case Some(apiName) if (apiName.contains(Constants.emailAuthScoreApiName)) =>
        val majorVersion = apiName.split("/")(2).split("\\.")(0).toInt
        majorVersion >= 3
      case Some(apiName) => logger.info(s"Skipping non EmailAuthScore API : $apiName"); false
      case None => logger.warn("API Name not found"); false
    }
  }

  def index(trx: SqsApiTransaction): Future[Unit] = {
    val trxIdOpt = Option(trx.transactionId).clean()
    implicit val trxId: TrxId = TrxId(trxIdOpt.getOrElse("unknown"))

    try {
      val apiName = Option(trx.apiName).clean()
      if (isAboveV3(apiName)) {
        val trxSearchTrx = Transaction(
          meta = Some(Meta(
            dbId = trx.id,
            transactionId = trxIdOpt,
            transactionDate = Some(new DateTime(trx.transactionDate, DateTimeZone.UTC)),
            environment = Some(trx.environmentType.toInt),
            status = Some(!trx.error),
            processingTime = Some(trx.processingTime),
            accountId = Some(trx.accountId),
            accountIpAddress = Option(trx.accountIpAddress).clean(),
            apiKey = Option(trx.apiKey).clean(),
            apiName = Option(trx.apiName).clean()
          )),
          parameters = Option(trx.parameters).clean().map(ParametersParser.parse),
          response = Option(trx.response).clean().map(ResponseParser.parse)
        )
        val indexRequest = IndexRequest(trxSearchTrx)
        logger.debug(s"Indexing transaction with id=[${trx.id}], transaction-id=[$trxId]")
        trxSearchIndexer.index(indexRequest).map {
          case res@HttpRes(status, _) if status < 200 || status > 299 =>
            logger.info(s"Error while indexing transaction with id=[${trx.id}], transaction-id=[$trxId] : $res")
            throw TrxSearchIndexingException(res)
          case _ => logger.info(s"Successfully indexed transaction with id=[${trx.id}], transaction-id=[$trxId]")
        }
      } else {
        logger.info("ignoring non 3.0 ID+ transaction")
        Future.successful(())
      }
    } catch {
      case NonFatal(ex) =>
        logger.error("Error while indexing a transaction", ex)
        Future.failed(ex)
    }
  }

  def processTransactions(trxList: Seq[SqsApiTransaction]): (Seq[Transaction], Seq[SqsApiTransaction]) = {
    trxList.foldLeft[(Seq[Transaction], Seq[SqsApiTransaction])](Nil, Nil) {
      case ((success, failure), trx) =>
        val trxIdOpt = Option(trx.transactionId).clean()
        val apiName = Option(trx.apiName).clean()
        implicit val trxId: TrxId = TrxId(trxIdOpt.getOrElse("unknown"))
        Try {
          if (isAboveV3(apiName)) {
            val trxSearchTransaction = Transaction(
              meta = Some(Meta(
                dbId = trx.id,
                transactionId = trxIdOpt,
                transactionDate = Some(new DateTime(trx.transactionDate, DateTimeZone.UTC)),
                environment = Some(trx.environmentType.toInt),
                status = Some(!trx.error),
                processingTime = Some(trx.processingTime),
                accountId = Some(trx.accountId),
                accountIpAddress = Option(trx.accountIpAddress).clean(),
                apiKey = Option(trx.apiKey).clean(),
                apiName = Option(trx.apiName).clean()
              )),
              parameters = Option(trx.parameters).clean().map(ParametersParser.parse),
              response = Option(trx.response).clean().map(ResponseParser.parse)
            )
            Some(trxSearchTransaction)
          } else {
            None
          }
        } match {
          case Success(optTrx) => optTrx.map(t => (success :+ t, failure) ).getOrElse((success, failure))
          case Failure(ex) =>
          logger.error(s"Error while trying to prepare transaction index", ex.getMessage)
            (success, failure :+ trx)
        }

    }
  }

  def indices(validBatch: Seq[Transaction]): Future[Seq[BulkIndexFailures]] = {

    val res = validBatch match {
      case Seq() => Future.successful(Seq.empty[BulkIndexFailures])
      case valid =>
        val bulkRequest = BulkIndexRequest(transactions = valid)
        trxSearchIndexer.index(bulkRequest) map {

          case res@HttpRes(status, _) if status < 200 || status > 299 =>
            implicit val trxId: TrxId = TrxId("bulkTransactionSearch")
            logger.info(s"Error while indexing transaction bulk insert : $res")
            throw TrxSearchIndexingException(res)
          case HttpRes(_, body) =>
            if (body.trim.isEmpty)
              Seq.empty[BulkIndexFailures]
            else {
              parse(body.trim).extract[Seq[BulkIndexFailures]]
            }
        }
    }
    res
  }
}

object TrxSearchIndexer {

  case class TrxSearchIndexingException(httpRes: HttpRes) extends Exception(s"Unable to index transaction. Response : $httpRes")

}
