package me.socure.transactionauditing.sqs.processor.queues

import me.socure.service.constants.ParameterNames
import me.socure.service.constants.ParameterNames.ParameterName
import me.socure.transactionauditing.sqs.model.SqsApiTransaction
import org.json4s.jackson.JsonMethods
import org.json4s.{DefaultFormats, Formats, JValue}

import scala.util.Try


trait CustomerUserIdFixer {

  import CustomerUserIdFixer._

  private def parseParamJson(parameters: String): Option[JValue] = {
    Option(parameters)
      .map(_.trim)
      .filter(_.nonEmpty)
      .flatMap(p => Try(JsonMethods.parse(p)).toOption)
  }

  private def parseParam(parameters: JValue, paramName: ParameterName): Option[String] = {
    (parameters \ paramName.toString.toLowerCase).extractOpt[String]
      .map(_.trim)
      .filter(_.nonEmpty)
  }

  def fix(txn: SqsApiTransaction): SqsApiTransaction = {
    val paramsJsonOpt = parseParamJson(txn.parameters)
    val updatedCustomerUserId = paramsJsonOpt.flatMap { paramsJson =>
      parseParam(paramsJson, ParameterNames.USER_ID)
        .orElse(parseParam(paramsJson, ParameterNames.CUSTOMER_USER_ID))
    }
    txn.copy(
      customerUserId = updatedCustomerUserId
    )
  }
}

object CustomerUserIdFixer extends CustomerUserIdFixer {
  private implicit val jsonFormats: Formats = DefaultFormats
}
