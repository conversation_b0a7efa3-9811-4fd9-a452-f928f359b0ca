package me.socure.transactionauditing.sqs.processor.queues

import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.options._
import me.socure.common.session.marshaller.JWTMarshaller
import me.socure.common.session.model.JWTToken
import me.socure.common.transaction.id.TrxId
import me.socure.model.ErrorResponse
import me.socure.transaction.search.index.model.ParametersParser
import me.socure.transactionauditing.common.json.TrxJsonFormats
import me.socure.transactionauditing.sqs.model.SqsApiTransaction
import me.socure.transactionauditing.sqs.processor.queues.VelocitySearchIndexer.VelocitySearchIndexingException
import me.socure.velocity.search.client.{HttpRes, VelocitySearchClient}
import me.socure.velocity.search.index.model._

import java.util.Base64
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.Formats
import org.json4s.jackson.JsonMethods._
import org.json4s.jackson.Serialization.write

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}
import scala.util.control.NonFatal

class VelocitySearchIndexer(velocitySearchClient: VelocitySearchClient, jwtMarshaller: JWTMarshaller)(implicit ec: ExecutionContext) {
  private val logger = TransactionAwareLoggerFactory.getLogger(getClass)

  implicit def jsonFormats: Formats = TrxJsonFormats.value

  private def isIDPlusAPI(apiNameOpt: Option[String])(implicit trxId: TrxId): Boolean = {
    apiNameOpt match {
      case Some(apiName) if apiName.contains("EmailAuthScore") => true
      case Some(apiName) => logger.info(s"Skipping non EmailAuthScore API : $apiName"); false
      case None => logger.warn("API Name not found"); false
    }
  }

  def index(trx: SqsApiTransaction): Future[Unit] = {
    val trxIdOpt = Option(trx.transactionId).clean()
    implicit val trxId: TrxId = TrxId(trxIdOpt.getOrElse("unknown"))
    try {
      val apiName = Option(trx.apiName).clean()
      if (isIDPlusAPI(apiName)) {
        fetchVelocityAttributes(trx) match {
          case Right(velocityAttributes) =>
            val indexRequest = IndexRequest(velocityAttributes)
            velocitySearchClient.index(indexRequest).map {
              case res@HttpRes(status, _) if status < 200 || status > 299 =>
                logger.info(s"Error while indexing velocity attributes with id=[${trx.id}], transaction-id=[$trxId] : $res")
                throw VelocitySearchIndexingException(res)
              case _ => logger.info(s"Successfully indexed transaction with id=[${trx.id}], transaction-id=[$trxId]")
            }
          case Left(left) =>
            logger.error(s"invalid transactions ${left.message}")
            Future.successful()
        }
      } else {
        logger.info("ignoring non 3.0 ID+ transaction")
        Future.successful(())
      }

    } catch {
      case NonFatal(ex) =>
        logger.error("Error while indexing a transaction", ex)
        Future.failed(ex)
    }
  }

  def processTransaction(trxList: Seq[SqsApiTransaction]): (Seq[VelocityAttributes], Seq[SqsApiTransaction]) = {
    trxList.foldLeft[(Seq[VelocityAttributes], Seq[SqsApiTransaction])](Nil, Nil) {
      case ((success, failure), trx) =>
        val trxIdOpt = Option(trx.transactionId).clean()
        val apiName = Option(trx.apiName).clean()
        implicit val trxId: TrxId = TrxId(trxIdOpt.getOrElse("unknown"))
        Try {
          if (isIDPlusAPI(apiName)) {
            fetchVelocityAttributes(trx) match {
              case Right(velAttr) => Some(velAttr)
              case Left(left) =>
                logger.error(s"invalid transaction ${left.message}")
                None
            }
          } else {
            None
          }
        } match {
          case Success(optTrx) => optTrx.map(t => (success :+ t, failure)).getOrElse((success, failure))
          case Failure(ex) =>
            logger.error(s"Error while trying to prepare velocity index", ex.getMessage)
            (success, failure :+ trx)
        }
    }
  }

  def indices(validBatch: Seq[VelocityAttributes]): Future[Seq[BulkIndexFailures]] = {

    val res = validBatch match {
      case Seq() => Future.successful(Seq.empty[BulkIndexFailures])
      case valid =>
        val bulkRequest = BulkIndexRequest(velocityAttributesList = valid)
        velocitySearchClient.index(bulkRequest) map {

          case res@HttpRes(status, _) if status < 200 || status > 299 =>
            implicit val trxId: TrxId = TrxId("bulkTransactionSearch")
            logger.info(s"Error while indexing transaction bulk insert : $res")
            throw VelocitySearchIndexingException(res)
          case HttpRes(_, body) =>
            if (body.trim.isEmpty)
              Seq.empty[BulkIndexFailures]
            else {
              parse(body).extract[Seq[BulkIndexFailures]]
            }
        }
    }
    res
  }

  private def fetchVelocityAttributes(trx: SqsApiTransaction): Either[ErrorResponse, VelocityAttributes] = {
    val trxIdOpt = Option(trx.transactionId).clean()
    implicit val trxId: TrxId = TrxId(trxIdOpt.getOrElse("unknown"))
    val trxDateOpt = Option(trx.transactionDate).map(new DateTime(_, DateTimeZone.UTC))
    val parametersOpt = Option(trx.parameters).clean().map(ParametersParser.parse)

    val deviceSessionIdOpt = parametersOpt.flatMap(parameters => getJWTDeviceSessionID(parameters.deviceSessionId))
    val accountIdOpt = Option(trx.accountId)
    val device = parametersOpt.flatMap(params =>
      params.device.map { x =>
        Device(x.operatingSystem,
          x.deviceType,
          x.deviceInterface)
      }
    )
    val dtf = DateTimeFormat.forPattern("yyyy-MM-dd")

    val velocityPII = parametersOpt.flatMap(params =>
      params.pii.map { tPii =>
        Pii(email = tPii.email.map(_.trim),
          firstName = tPii.firstName.map(cleanString),
          surName = tPii.surName.map(cleanString),
          physicalAddress = tPii.physicalAddress.map(cleanString),
          physicalAddress2 = tPii.physicalAddress2.map(cleanString),
          zip = tPii.zip.map(_.replaceAll("-", "")),
          city = tPii.city.map(cleanString),
          state = tPii.state.map(cleanString),
          country = tPii.country.map(cleanString),
          ipAddress = tPii.ipAddress,
          mobileNumber = tPii.mobileNumber.map(cleanString),
          dob = tPii.dob.map(_.toString(dtf)),
          fullName = tPii.fullName.map(cleanString),
          addresses = tPii.addresses.map(_.map(x => Address(x.line1,
            x.line2, x.city, x.state, x.postalCode, x.country, x.`type`))),
          nationalId = tPii.nationalId,
          userName = tPii.userName)
      }
    )

    (trxIdOpt, trxDateOpt) match {
      case (Some(tId), Some(tDate)) =>
        logger.debug(s"Indexing velocity attributes")
        val velocityAttributes = VelocityAttributes(Option(Meta(trxIdOpt, trxDateOpt, deviceSessionIdOpt, accountIdOpt)),
          Option(Parameters(velocityPII, device)))
        Right(velocityAttributes)
      case _ => Left(ErrorResponse(400, "transactionId or transactionDate is missing"))
    }
  }

  private def cleanString(value: String): String = {
    value.trim
      .toLowerCase
      .replaceAll("[^\\s0-9a-z]", "")
      .replaceAll("\\s+", " ")
  }

  private def getJWTDeviceSessionID(deviceSessionId: Option[String]): Option[String] = {
    deviceSessionId.map(sid =>
      Try {
        val rawSID = Base64.getDecoder.decode(sid).map(_.toChar).mkString
        val token = JWTToken(Map("session_id" -> rawSID))
        jwtMarshaller.create(write(token))
      } match {
        case Success(s) => s
        case Failure(e) => sid
      }
    )
  }
}

object VelocitySearchIndexer {

  case class VelocitySearchIndexingException(httpRes: HttpRes) extends Exception(s"Unable to index velocity attributes. Response : $httpRes")

}
