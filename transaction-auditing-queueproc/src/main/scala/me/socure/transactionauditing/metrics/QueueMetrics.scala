package me.socure.transactionauditing.metrics

import me.socure.common.metrics.Metrics
import me.socure.common.transaction.id.TrxId

import scala.concurrent.{ExecutionContext, Future}

object QueueMetrics {
  def auditTime[T, U](metrics: Metrics, entity: T, consumer: T => Future[U])(startTimeProducer: T => Option[Long],
                                                                            executionContext: ExecutionContext,
                                                                            trxId: TrxId): Future[U] = {
    implicit val ec: ExecutionContext = executionContext
    implicit val txId = trxId
    val future = metrics.timeFuture("save.method_call.duration") {
      consumer(entity)
    }
    future.onComplete(_ => {
      startTimeProducer(entity)
        .foreach(startTime => metrics.time("receive.duration") {
          System.currentTimeMillis - startTime
        })
    })
    future
  }
}
