package me.socure.transactionauditing.metrics

import me.socure.account.client.AccountDetailsClient
import me.socure.common.error.reporter.ErrorReporterMail
import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.reliable.metrics.datadog.MetricsProvider
import me.socure.common.reliable.metrics.datadog.domain.{Metric, MetricTypes}
import me.socure.common.transaction.id.TrxId
import me.socure.constants.EnvironmentConstants
import me.socure.model.monitoring.audit.data.model.{Account, Transaction}
import me.socure.transactionauditing.sqs.model.SqsApiTransaction

import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal
import scala.util.{Failure, Success, Try}

class TransactionMetricsService(errorReporterMail: ErrorReporterMail,
                                metricsProvider: MetricsProvider[Transaction],
                                accountDetailsClient: AccountDetailsClient,
                                transactionMetricsEnabled: Boolean = true
                               ) {

  val logger = TransactionAwareLoggerFactory.getLogger(getClass)
  val metricPrefix = "model.monitoring"
  val metrics: Metrics = JavaMetricsFactory.get(metricPrefix) //retaining the same prefix as like today.

  def handle(transaction: SqsApiTransaction)(implicit executionContext: ExecutionContext): Future[Unit] = Future {
    //todo : fix check if (IConstants.APIName.isAScoringApi(transaction.apiName) && isProdEnv(transaction)) {
    implicit val trxId = TrxId(transaction.transactionId)
    if (transactionMetricsEnabled) {
      try {
        val metricsToAdd: Set[Metric] = metricsProvider.provide(toModelMonitoringTrx(transaction))
        metricsToAdd.foreach { metric =>
          try {
            if (metric.metricType == MetricTypes.COUNT) {
              metric.points.headOption match {
                case Some(point) =>
                  metrics.count(fixMetricName(metric.name), point._2.toLong, metric.tags.mkString(","))
                case _ =>
              }
            } else if (metric.metricType == MetricTypes.GAUGE) {
              metric.points.headOption match {
                case Some(point) =>
                  metrics.gauge(fixMetricName(metric.name), point._2, metric.tags.mkString(","))
                case _ =>
              }
            }
          } catch {
            case NonFatal(ex) =>
              //Not throwing the exception as we could still process the remaining metrics.
              logger.error(s"Exception occured while adding metrics - ${ex.getMessage}")
          }
        }
      } catch {
        case NonFatal(ex) =>
          errorReporterMail.report("Unable to handle metrics for transaction", Option.apply(ex))
          throw ex
      }
    }
  }

  private def getAccount(accountId: Long)(implicit trxId: TrxId): Account = {
    val accountDetailsTry = accountDetailsClient.getById(accountId)
    accountDetailsTry match {
      case Success(accountDetails) =>
        Account(
          id = accountId,
          name = Option(accountDetails.accountDetails.getName),
          isInternal = Option(accountDetails.isInternal)
        )
      case Failure(ex) =>
        logger.error(s"Unable to fetch account name for account id : $accountId", ex)
        Account(
          id = accountId,
          name = None,
          isInternal = None
        )
    }
  }

  private def toModelMonitoringTrx(transaction: SqsApiTransaction)(implicit trxId: TrxId): Transaction = {
    Transaction(
      transactionId = transaction.transactionId,
      account = getAccount(transaction.accountId),
      parametersJson = transaction.parameters,
      debugJson = transaction.debug,
      error = transaction.error,
      apiName = transaction.apiName,
      environmentId = transaction.environmentType.toInt
    )
  }

  private def fixMetricName(name: String): String = {
    Try(name.replace(s"${metricPrefix}.","")).getOrElse(name)
  }

  def isProdEnv(transaction: SqsApiTransaction) =  EnvironmentConstants.PRODUCTION_ENVIRONMENT.id == transaction.environmentType.intValue
}
