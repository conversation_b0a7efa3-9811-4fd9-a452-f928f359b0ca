package me.socure.transactionauditing.pii.mask

import java.net.InetSocketAddress

import me.socure.account.client.superadmin.{AccountInfoClient, AccountInfoClientFactory}
import me.socure.transactionauditing.s3.storage.S3TransactionStorage
import net.spy.memcached.MemcachedClient

import scala.concurrent.ExecutionContext
import scala.concurrent.duration._

/**
  * Created by jamesanto on 4/26/17.
  */
object PiiMaskServiceFactory {

  def get(memcachedHost: String,
          memcachedPort: Int,
          accountServiceEndpoint: String,
          errorBucket: String,
          s3StorageService: S3TransactionStorage
         )(
           implicit ec: ExecutionContext
         ): PiiMaskService = {

    val memcachedClient = new MemcachedClient(new InetSocketAddress(memcachedHost,memcachedPort))

    val accountInfoClient: AccountInfoClient = AccountInfoClientFactory.createCached(
      accountServiceEndpoint = accountServiceEndpoint,
      memcachedClient = memcachedClient,
      ttl = Some(60.minutes)
    )

    new PiiMaskService(
      accountInfoClient = accountInfoClient,
      s3StorageService = s3StorageService,
      errorBucket = errorBucket,
      paramsMaskService = ParamsMaskService,
      responseMaskService = ResponseMaskService
    )
  }
}
