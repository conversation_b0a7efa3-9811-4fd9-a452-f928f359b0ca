package me.socure.transactionauditing.pii.mask

import java.net.{URI, URLDecoder}
import java.nio.charset.StandardCharsets
import com.amazonaws.services.s3.model.PutObjectResult
import me.socure.account.client.superadmin.AccountInfoClient
import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.transaction.id.TrxId
import me.socure.model.encryption.ApiKeyString
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import me.socure.transactionauditing.s3.storage.{S3Object, S3TransactionStorage}
import me.socure.transactionauditing.sqs.marshaller.SqsMessageMarshaller
import me.socure.transactionauditing.sqs.model.SqsApiTransaction

import scala.collection.mutable
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

/**
  * Created by jam<PERSON>to on 5/8/17.
  */
class PiiMaskService(
                      accountInfoClient: AccountInfoClient,
                      s3StorageService: S3TransactionStorage,
                      errorBucket: String,
                      paramsMaskService: ParamsMaskService,
                      responseMaskService: ResponseMaskService
                    )(
                      implicit ec: ExecutionContext
                    ) {

  private val logger = TransactionAwareLoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get("mask.pii.audit")

  import PiiMaskService._

  private def maskRequestUri(requestUri: String): String = {
    val uri: URI = URI.create(requestUri)
    val rawQuery = uri.getRawQuery
    if(null != rawQuery) {
      val queryParams = rawQuery.split("&")
      val maskedQueryParams: Seq[String] = queryParams.map { param =>
        val keyValue = param.split("=")
        val key = keyValue(0)
        val value = URLDecoder.decode(keyValue(1), StandardCharsets.UTF_8.name())
        if (piiParameters.contains(key.trim.toLowerCase) && value.trim.nonEmpty) s"$key=$providedPlaceholder" else param
      }
      val maskedUriSb = new mutable.StringBuilder()
      if(null != uri.getScheme) maskedUriSb.append(uri.getScheme + "://")
      if(null != uri.getAuthority) maskedUriSb.append(uri.getAuthority)
      if(null != uri.getPath) maskedUriSb.append(uri.getPath)
      if(maskedQueryParams.nonEmpty) maskedUriSb.append("?").append(maskedQueryParams.mkString("&"))

      URI.create(maskedUriSb.toString).toString
    } else requestUri
  }

  private[mask] def mask(piiFields: PiiFields)(implicit trxId: TrxId): PiiFields = {
    val cleanedPiiFields = piiFields.clean()
    PiiFields(
      parameters = cleanedPiiFields.parameters.map(paramsMaskService.mask).orElse(piiFields.parameters), //This orElse is to retain the original param that could be empty
      requestUri = cleanedPiiFields.requestUri.map(maskRequestUri).orElse(piiFields.requestUri),
      response = cleanedPiiFields.response.map(responseMaskService.mask).orElse(piiFields.response)
    )
  }

  def mask(transaction: SqsApiTransaction): Future[SqsApiTransaction] = {
    implicit val trxId: TrxId = TrxId(transaction.transactionId)
    Try {
      accountInfoClient.hasRole(apiKeyString = ApiKeyString(Option(transaction.apiKey).getOrElse("")), role = BusinessUserRoles.MASK_PII.id)
        .map {
          case Left(ErrorResponse(code, _)) if code == ExceptionCodes.AccountNotFound.id => transaction //Do not mask
          case Left(errorResponse) => throw new PiiMaskException(s"Unable to check whether api key [${transaction.apiKey}] has PII_MASK permission due to : $errorResponse")
          case Right(false) => transaction //Do not mask because account doesn't have permission
          case Right(true) =>
            val piiFields = PiiFields(transaction)
            val maskedPiiFields = mask(piiFields)
            transaction.copy(
              parameters = maskedPiiFields.parameters.getOrElse(transaction.parameters),
              requestURI = maskedPiiFields.requestUri.getOrElse(transaction.requestURI),
              response = maskedPiiFields.response.getOrElse(transaction.response)
            )
        }
        .recoverWith {
          case ex =>
            logger.error("[PII_MASK_ERROR]", ex)
            metrics.increment("exception", s"class:${ex.getClass.getSimpleName}")
            Try(handleFailure(transaction)) match {
              case Success(_) => Future.successful(transaction)
              case Failure(ex1) => Future.failed(ex1)
            }
        }
    } match {
      case Success(txn) =>
        if (logger.isDebugEnabled) logger.debug("Successfully masked PII")
        txn
      case Failure(ex) =>
        logger.error("[PII_MASK_ERROR]", ex)
        metrics.increment("exception", s"class:${ex.getClass.getSimpleName}")
        handleFailure(transaction)
        throw ex
    }
  }

  private def handleFailure(transaction: SqsApiTransaction): PutObjectResult = {
    val transactionString = SqsMessageMarshaller.marshal(transaction)
    s3StorageService.storeFile(
      S3Object(
        bucket = errorBucket,
        key = transaction.transactionId,
        data = transactionString
      )
    )
  }
}

object PiiMaskService {

  val piiParameters: Set[String] = Set(
    "country",
    "ipaddress",
    "firstname",
    "mobilenumber",
    "city",
    "nationalid",
    "physicaladdress2",
    "surname",
    "state",
    "email",
    "zip",
    "physicaladdress",
    "driverlicensestate",
    "companyname",
    "dob",
    "geocode",
    "fullname",
    "driverlicense",
    "line1",
    "line2",
    "postalcode",
    "accountnumber",
    "routingnumber",
    "ein",
    "recipientcountry",
    "paymenttype",
    "disbursementtype",
    "paymenttypedetail",
    "producttype",
    "cardfirst6",
    "cardlast4",
    "currencytype"
  )

  val piiResponse: Set[String] = Set(
    "documentdata",
    "prefill",
    "bestmatchedentity"
  )

  val piiResponseNodes: List[List[String]] = List(
    "deceasedCheck" ::  "firstName" :: Nil,
    "deceasedCheck" ::  "middleName" :: Nil,
    "deceasedCheck" ::  "surName" :: Nil,
    "deceasedCheck" ::  "nationalId" :: Nil,
    "deceasedCheck" ::  "dob" :: Nil,
    "deceasedCheck" ::  "dod" :: Nil,
    "deceasedCheck" ::  "age" :: Nil
  )

  val providedPlaceholder = "[PROVIDED]"

  class PiiMaskException(msg: String) extends Exception(msg)

}
