package me.socure.transactionauditing.pii.mask

import java.net.URLDecoder
import java.nio.charset.StandardCharsets

import org.json4s._
import org.json4s.jackson.JsonMethods

import scala.util.{Failure, Success, Try}

trait ParamsMaskService {

  private def tryUrlDecode(s: String): String = {
    Try {
      //replace non-encoded '%' and '+' to encoded values
      val cleanS = s.replaceAll("%(?![0-9a-fA-F]{2})", "%25").replaceAll("\\+", "%2B")
      URLDecoder.decode(cleanS, StandardCharsets.UTF_8.name())
    } match {
      case Success(decodedStr) => decodedStr
      case Failure(ex) => throw ex
    }
  }

  private def shouldMaskParam(paramName: String): Boolean = {
    Option(paramName).map(_.trim.toLowerCase).exists(PiiMaskService.piiParameters.contains)
  }

  private def shouldMaskValue(paramValue: JValue): Boolean = {
    Option(paramValue).flatMap(_.toOption) match {
      case Some(_: JObject) => false
      case Some(_: JArray) => false
      case Some(_: JSet) => false
      case Some(j) if Option(tryUrlDecode(j.values.toString)).exists(_.trim.nonEmpty) => true
      case _ => false
    }
  }

  def shouldMask(paramName: String, paramValue: JValue): Boolean = {
    shouldMaskParam(paramName) && shouldMaskValue(paramValue)
  }

  def mask(params: JValue): JValue = {
    params.transformField {
      case (paramName, paramValue) if shouldMask(paramName, paramValue) => paramName -> JString(PiiMaskService.providedPlaceholder)
    }
  }

  def mask(params: String): String = {
    val originalJson = JsonMethods.parse(params)
    val maskedJson = mask(originalJson)
    val maskedParams = JsonMethods.compact(maskedJson)
    maskedParams
  }
}

object ParamsMaskService extends ParamsMaskService
