package me.socure.transactionauditing.pii.mask

import me.socure.transactionauditing.sqs.model.SqsApiTransaction

/**
  * Created by jamesanto on 5/8/17.
  */
case class PiiFields(parameters: Option[String], requestUri: Option[String], response: Option[String]) {
  def clean(): PiiFields = PiiFields(
    parameters = parameters.flatMap(Option(_).map(_.trim).filter(_.nonEmpty)),
    requestUri = requestUri.flatMap(Option(_).map(_.trim).filter(_.nonEmpty)),
    response = response.flatMap(Option(_).map(_.trim).filter(_.nonEmpty))
  )
}

object PiiFields {
  def apply(
             parameters: String,
             requestUri: String,
             response: String
           ): PiiFields = PiiFields(
    parameters = Option(parameters),
    requestUri = Option(requestUri),
    response = Option(response)
  )

  def apply(transaction: SqsApiTransaction): PiiFields = apply(
    parameters = transaction.parameters,
    requestUri = transaction.requestURI,
    response = transaction.response
  )
}
