package me.socure.transactionauditing.pii.mask

  import org.json4s._
  import org.json4s.jackson.JsonMethods

  trait ResponseMaskService {

    private def shouldMaskResponse(responseNode: String): Boolean = {
      Option(responseNode).map(_.trim.toLowerCase).exists(PiiMaskService.piiResponse.contains)
    }

    def shouldMask(responseNode: String): Boolean = {
      shouldMaskResponse(responseNode)
    }

    def mask(params: JValue): JValue = {
      val updatedResponse = params.transformField {
        case (paramName, _) if shouldMask(paramName) => paramName -> JString(PiiMaskService.providedPlaceholder)
      }
      PiiMaskService.piiResponseNodes.foldLeft(updatedResponse) {
        case (response, node) => response.replace(node, JString(PiiMaskService.providedPlaceholder))
      }
    }

    def mask(response: String): String = {
      val originalJson = JsonMethods.parse(response)
      val maskedJson = mask(originalJson)
      val maskedResponse = JsonMethods.compact(maskedJson)
      maskedResponse
    }
  }

  object ResponseMaskService extends ResponseMaskService
