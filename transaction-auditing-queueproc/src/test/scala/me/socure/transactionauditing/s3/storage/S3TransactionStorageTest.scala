package me.socure.transactionauditing.s3.storage

import akka.actor.ActorSystem
import akka.stream.{ActorMaterializer, Materializer}
import com.amazonaws.services.s3.model.PutObjectResult
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.{FunSuite, Matchers}

import scala.concurrent.ExecutionContext

class S3TransactionStorageTest extends FunSuite with Matchers with MockitoSugar with ScalaFutures {
  implicit val executionContext :ExecutionContext = ExecutionContext.global
  implicit val actorSystem = ActorSystem("test")
  implicit val materializer: Materializer = ActorMaterializer()

  val s3 = mock[S3TransactionStorage]

  test("upload a file with s3") {

    val s3Object = S3Object("bucket", "key", "data")
    val uploadResult: PutObjectResult =  new PutObjectResult()
    uploadResult.setETag("etag")

    Mockito.when(s3.storeFile(s3Object)).thenReturn(uploadResult)

    s3.storeFile(s3Object) shouldBe uploadResult
  }

}
