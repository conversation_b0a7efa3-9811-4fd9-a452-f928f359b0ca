package me.socure.transactionauditing.sqs.processor.queues

import me.socure.service.constants.ParameterNames._
import me.socure.transactionauditing.sqs.model.SqsAPITransactionValueGenerator
import org.scalatest.{FreeSpec, Matchers}

class CustomerUserIdFixerTest extends FreeSpec with Matchers {
  "should use userId when available" in {
    val txn = SqsAPITransactionValueGenerator.aAPITransaction(
      parameters = s"""{"${pn(USER_ID)}":"some-user-id", "${pn(CUSTOMER_USER_ID)}":"some-customer-user-id"}""",
      customerUserId = Some("some-generated-customer-user-id")
    )

    val expectedTxn = txn.copy(customerUserId = Some("some-user-id"))
    CustomerUserIdFixer.fix(txn) shouldBe expectedTxn
  }

  "should fallback to customer-user-id in the parameters when user-id is not available" in {
    val txn = SqsAPITransactionValueGenerator.aAPITransaction(
      parameters = s"""{"${pn(CUSTOMER_USER_ID)}":"some-customer-user-id"}""",
      customerUserId = Some("some-generated-customer-user-id")
    )

    val expectedTxn = txn.copy(customerUserId = Some("some-customer-user-id"))
    CustomerUserIdFixer.fix(txn) shouldBe expectedTxn
  }

  "should return None when neither userId nor customerUserId present in the parameters" in {
    val txn = SqsAPITransactionValueGenerator.aAPITransaction(
      parameters = "{}",
      customerUserId = Some("some-generated-customer-user-id")
    )

    val expectedTxn = txn.copy(customerUserId = None)
    CustomerUserIdFixer.fix(txn) shouldBe expectedTxn
  }

  private def pn(parameterName: ParameterName): String = parameterName.toString.toLowerCase
}
