package me.socure.transactionauditing.sqs.processor.queues

import me.socure.common.random.Random
import me.socure.common.session.marshaller.JWTMarshaller
import me.socure.transactionauditing.sqs.model.SqsAPITransactionValueGenerator
import me.socure.transactionauditing.sqs.processor.queues.VelocitySearchIndexer.VelocitySearchIndexingException
import me.socure.velocity.search.client.{GenericClient, HttpRes, VelocitySearchClient}
import me.socure.velocity.search.index.model._
import org.joda.time.{DateTime, DateTimeZone}
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Milliseconds, Seconds, Span}
import org.scalatest.{FreeSpec, Matchers}

import java.util.Base64
import scala.concurrent.{ExecutionContext, Future}

class VelocitySearchIndexerTest extends FreeSpec with Matchers with ScalaFutures with MockFactory {

  System.setProperty("user.timezone", "UTC")

  private implicit val ec: ExecutionContext = ExecutionContext.Implicits.global
  private implicit val patienceConf: PatienceConfig = PatienceConfig(
    timeout = Span(50, Seconds),
    interval = Span(50, Milliseconds)
  )

  private class MockableVelocitySearchClient extends VelocitySearchClient(null: GenericClient, "")
  private val velocitySearchClient = mock[MockableVelocitySearchClient]
  private val secretKey = "5nPZwNw1#KGvKtZ^PQoyA4doi9#S"
  private val marshaller = new JWTMarshaller(secretKey)
  private val velocitySearchIndexer = new VelocitySearchIndexer(velocitySearchClient, marshaller)

  "should skip if it's not EmailAuthScore" in {
    val trx = SqsAPITransactionValueGenerator.aAPITransaction(apiName = "/api/3.0/AuthScore")
    whenReady(velocitySearchIndexer.index(trx))(_ shouldBe())
  }

  "should index properly" in {
    testIndexing(success = true)
  }

  "should fail when the client returns a non successful response" in {
    testIndexing(success = false)
  }

  "should index JWT when JWT session id is provided" in {
    testIndexing(success = true, format = "jwt")
  }

  "should index JWT when base64 session id is provided" in {
    testIndexing(success = true, format = "base64")
  }

  "should index <how?> when neither JWT nor base64 is provided" in {
    testIndexing(success = true, format = "raw")
  }

  "should filter parsing error" in {
    val trx = SqsAPITransactionValueGenerator.aAPITransaction(parameters = "{")
    val (_, invalid) = velocitySearchIndexer.processTransaction(Seq(trx))
    invalid.size shouldBe 1
  }

  private def testIndexing(success: Boolean, format: String = "jwt") : Unit = {
    val id = Random.nextLong()
    val trxId = Random.uuids()
    val deviceSessionIdRaw = Random.uuids()
    val deviceSessionId =
      if (format == "base64") {
        Base64.getEncoder.encode(deviceSessionIdRaw.getBytes).map(_.toChar).mkString
      } else if (format == "jwt") {
        marshaller.create(s"""{"data":{"session_id":"$deviceSessionIdRaw"}}""")
      } else deviceSessionIdRaw
    val deviceSessionIdResult =
      if (format == "jwt" || format == "base64") {
        marshaller.create(s"""{"data":{"session_id":"$deviceSessionIdRaw"}}""")
      } else deviceSessionIdRaw
    val environment = Random.shuffle((1 to 3).toList).head
    val date = System.currentTimeMillis()
    val status = Random.nextBoolean()
    val processingTime = Random.nextLong()
    val accountId = Random.nextLong()
    val accountIpAddress = s"192.168.1.${2 + Random.nextInt(250)}"
    val apiKey = Random.uuids()
    val apiName = "/api/3.0/EmailAuthScore"
    val trx = SqsAPITransactionValueGenerator.aAPITransaction(
      id = Some(id),
      transactionId = trxId,
      transactionDate = date,
      environmentType = environment.toLong,
      error = !status,
      processingTime = processingTime,
      accountId = accountId,
      accountIpAddress = accountIpAddress,
      apiKey = apiKey,
      apiName = apiName,
      parameters =
        s"""{
          | "firstname" : "john",
          | "surname" : "doe",
          | "runid": "test-run-id-2",
          | "devicesessionid" : "$deviceSessionId"
          |}""".stripMargin
    )

    val velocityAttributes = VelocityAttributes(
      meta = Option(Meta(
        transactionId = Option(trxId),
        transactionDate = Option(new DateTime(date, DateTimeZone.UTC)),
        deviceSessionId = Option(deviceSessionIdResult),
        accountId = Option(accountId)
      )),
      parameters = Some(Parameters.Empty.copy(
        device = None,
        pii = Option(Pii.Empty.copy(
          firstName = Some("john"),
          surName =  Some("doe")
        ))
      ))
    )

    if(success) {
      (velocitySearchClient.index(_: IndexRequest)).expects(IndexRequest(velocityAttributes)) returning Future.successful(HttpRes(200, Random.nextString(1024)))
      whenReady(velocitySearchIndexer.index(trx))(_ shouldBe ())
    } else {
      val httpRes = HttpRes(500, Random.nextString(1024))
      (velocitySearchClient.index(_: IndexRequest)).expects(IndexRequest(velocityAttributes)) returning Future.successful(httpRes)
      whenReady(velocitySearchIndexer.index(trx).failed) (_ shouldBe VelocitySearchIndexingException(httpRes))
    }
  }
}
