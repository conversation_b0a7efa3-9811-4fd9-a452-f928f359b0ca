package me.socure.transactionauditing.sqs.processor.queues

import me.socure.common.random.Random
import me.socure.transaction.search.client.common.HttpRes
import me.socure.transaction.search.client.indexer.TrxIndexerClient
import me.socure.transaction.search.index.model._
import me.socure.transactionauditing.sqs.model.{SqsAPITransactionValueGenerator, SqsApiTransaction}
import me.socure.transactionauditing.sqs.processor.queues.TrxSearchIndexer.TrxSearchIndexingException
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Milliseconds, Seconds, Span}
import org.scalatest.{FreeSpec, Matchers}

import scala.concurrent.{ExecutionContext, Future}

class TrxSearchIndexerTest extends FreeSpec with Matchers with ScalaFutures with MockFactory {

  System.setProperty("user.timezone", "UTC")

  private implicit val ec: ExecutionContext = ExecutionContext.Implicits.global
  private implicit val patienceConf: PatienceConfig = PatienceConfig(
    timeout = Span(3, Seconds),
    interval = Span(50, Milliseconds)
  )
  private implicit val formats: DefaultFormats.type = DefaultFormats

  private class MockableTrxIndexerClient extends TrxIndexerClient(null: dispatch.Http, "")

  private val trxIndexerClient = mock[MockableTrxIndexerClient]
  private val trxSearchIndexer = new TrxSearchIndexer(trxIndexerClient)

  private val TestResponse =
  """
      |{
      |  "referenceId": "35bcbb7d-b735-47c5-85b9-0a312724e75c",
      |  "nameAddressCorrelation": {
      |    "reasonCodes": [
      |      "R705",
      |      "R703"
      |    ],
      |    "score": 0.03
      |  },
      |  "nameEmailCorrelation": {
      |    "reasonCodes": [
      |      "R559"
      |    ],
      |    "score": 0.07
      |  },
      |  "addressRisk": {
      |    "reasonCodes": [
      |      "R705",
      |      "R703",
      |      "I720",
      |      "R713"
      |    ],
      |    "score": 0.306
      |  },
      |  "emailRisk": {
      |    "reasonCodes": [
      |      "R559",
      |      "I568",
      |      "I551",
      |      "I555",
      |      "I566",
      |      "I570"
      |    ],
      |    "score": 0.6693
      |  },
      |  "decision": {
      |    "value": "refer",
      |    "modelName": "Best Practice",
      |    "modelVersion": "1.0"
      |  },
      |  "customerProfile": {
      |    "customerUserId": "BANK",
      |    "userId": "********"
      |  },
      |  "documentVerification": {
      |    "reasonCodes": [
      |      "R810",
      |      "R824"
      |    ],
      |    "documentType": {
      |      "type": "Drivers License",
      |      "country": "USA",
      |      "state": "FL"
      |    },
      |    "decision": {
      |      "name": "standard",
      |      "value": "reject"
      |    },
      |    "documentData": {
      |      "firstName": "BENVENUTO",
      |      "surName": "ANDREW",
      |      "fullName": "BENVENUTO ANDREW",
      |      "address": "1835 EAST SANDPOINTE LN, VERO BEACH, FL 32963",
      |      "parsedAddress": {
      |        "physicalAddress": "1835 EAST SANDPOINTE LN",
      |        "city": "VERO BEACH",
      |        "state": "FL",
      |        "country": "USA",
      |        "zip": "32963"
      |      },
      |      "documentNumber": "B515-000-95-254-0",
      |      "dob": "1995-07-14",
      |      "expirationDate": "2023-07-14",
      |      "issueDate": "2016-07-20"
      |    }
      |  }
      |}""".stripMargin

  "should skip if it's not EmailAuthScore" in {
    val trx = SqsAPITransactionValueGenerator.aAPITransaction(apiName = "/api/3.0/AuthScore", response = TestResponse)
    whenReady(trxSearchIndexer.index(trx))(_ shouldBe())
  }

  "should skip if it's not 3.0" in {
    val trx = SqsAPITransactionValueGenerator.aAPITransaction(apiName = "/api/2.5/EmailAuthScore", response = TestResponse)
    whenReady(trxSearchIndexer.index(trx))(_ shouldBe())
  }

  "should index properly" in {
    testIndexing(success = true)
  }

  "should fail when the client returns a non successful response" in {
    testIndexing(success = false)
  }

  "should return failures during bulk indexing" in {

    val (trx, expectedTransaction) = getMockPair

    val failures = Serialization.write(Seq(BulkIndexFailures(trx.transactionId, "test")))

    val httpRes = HttpRes(200, failures)
    (trxIndexerClient.index(_: BulkIndexRequest)).expects(BulkIndexRequest(Seq(expectedTransaction))).returning(Future.successful(httpRes))
    val (valid, _) = trxSearchIndexer.processTransactions(Seq(trx))
    whenReady(trxSearchIndexer.indices(valid))(_.size shouldBe 1)

  }

  private def getMockPair: (SqsApiTransaction, Transaction) = {
    val id = Random.nextLong()
    val trxId = Random.uuids()
    val environment = Random.shuffle((1 to 3).toList).head
    val date = DateTime.now(DateTimeZone.UTC).minusDays(2 + Random.nextInt(5))
    val status = Random.nextBoolean()
    val processingTime = Random.nextLong()
    val accountId = Random.nextLong()
    val accountIpAddress = s"192.168.1.${2 + Random.nextInt(250)}"
    val apiKey = Random.uuids()
    val apiName = "/api/3.0/EmailAuthScore"

    val trx = SqsAPITransactionValueGenerator.aAPITransaction(
      id = Some(id),
      transactionId = trxId,
      transactionDate = date.getMillis,
      environmentType = environment.toLong,
      error = !status,
      processingTime = processingTime,
      accountId = accountId,
      accountIpAddress = accountIpAddress,
      apiKey = apiKey,
      apiName = apiName,
      response = TestResponse,
      parameters =
        """{
          | "firstname" : "John",
          | "surname" : "Doe",
          | "runid": "test-run-id-2"
          |}""".stripMargin
    )

    val expectedTransaction = Transaction(
      meta = Some(Meta(
        dbId = Some(id),
        transactionId = Some(trxId),
        transactionDate = Some(date),
        environment = Some(environment),
        status = Some(status),
        processingTime = Some(processingTime),
        accountId = Some(accountId),
        accountIpAddress = Some(accountIpAddress),
        apiKey = Some(apiKey),
        apiName = Some(apiName)
      )),
      parameters = Some(Parameters.Empty.copy(
        runId = Some("test-run-id-2"),
        pii = Some(Pii.Empty.copy(
          firstName = Some("John"),
          surName = Some("Doe")
        ))
      )),
      response = Some(ResponseData(
        decisionResult = Some("refer"),
        reasonCodes = Some(Set("R705", "R703", "R559", "R705", "R703", "I720", "R713", "R559", "I568", "I551", "I555", "I566", "I570", "R810", "R824")),
        docvDocumentData = Some(DocumentData(
          firstName = Some("BENVENUTO"),
          lastName = Some("ANDREW"),
          fullName = Some("BENVENUTO ANDREW"),
          addressLine1 = Some("1835 EAST SANDPOINTE LN"),
          dob = DateUtil.parseDate("1995-07-14")
        ))
      ))
    )

    (trx, expectedTransaction)
  }

  private def testIndexing(success: Boolean): Unit = {

    val (trx, expectedTransaction) = getMockPair

    if (success) {
      (trxIndexerClient.index(_: IndexRequest)).expects(IndexRequest(expectedTransaction)).returning(Future.successful(HttpRes(200, Random.nextString(1024))))
      whenReady(trxSearchIndexer.index(trx))(_ shouldBe())
    } else {
      val httpRes = HttpRes(500, Random.nextString(1024))
      (trxIndexerClient.index(_: IndexRequest)).expects(IndexRequest(expectedTransaction)).returning(Future.successful(httpRes))
      whenReady(trxSearchIndexer.index(trx).failed)(_ shouldBe TrxSearchIndexingException(httpRes))
    }
  }
}
