package me.socure.transactionauditing.sqs.processor.queues

import akka.actor.ActorSystem
import akka.stream.ActorMaterializer
import com.amazon.sqs.javamessaging.{AmazonSQSExtendedClient, ExtendedClientConfiguration}
import com.amazonaws.auth.{AWSStaticCredentialsProvider, AnonymousAWSCredentials}
import com.amazonaws.services.s3.AmazonS3Client
import com.amazonaws.services.sqs.model.{MessageAttributeValue, SendMessageRequest}
import me.socure.common.docker.sqsservice.SqsSupport
import me.socure.sqs.SqsServerAwait
import org.json4s.DefaultFormats
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, FunSuite, Matchers}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class BaseSqsTestConfiguration extends FunSuite with MockitoSugar with Matchers with ScalaFutures with BeforeAndAfterAll with SqsSupport {
  implicit val formats: DefaultFormats.type = DefaultFormats
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  implicit val system: ActorSystem = ActorSystem()
  implicit val mat: ActorMaterializer = ActorMaterializer()
  implicit val patience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(5, Seconds))

  val credentialsProvider = new AWSStaticCredentialsProvider(new AnonymousAWSCredentials)

  def createExtendedClient(): AmazonSQSExtendedClient = {
    val  extendedClientConfiguration = new ExtendedClientConfiguration()
      .withLargePayloadSupportEnabled(mock[AmazonS3Client], "test-bucket")
      .withAlwaysThroughS3(false)

    val client = new AmazonSQSExtendedClient(SqsClient, extendedClientConfiguration)

    SqsServerAwait.await(client, 1000, 10)
    client
  }

  override def afterAll {
    cleanupSqs()
  }

  def handleFailure(throwable: Throwable): Unit = {
    println(throwable)
  }

  def withAttribute(request: SendMessageRequest, attributeName: String, attributeValue: String): SendMessageRequest = {
    val messageAttributeValue = new MessageAttributeValue()
    messageAttributeValue.setDataType("String")
    messageAttributeValue.setStringValue(attributeValue)
    request.addMessageAttributesEntry(attributeName, messageAttributeValue)
  }
}
