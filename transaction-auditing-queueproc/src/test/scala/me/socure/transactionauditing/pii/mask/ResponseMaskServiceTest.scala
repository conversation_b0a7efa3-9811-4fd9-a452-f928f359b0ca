package me.socure.transactionauditing.pii.mask

import me.socure.transactionauditing.pii.mask.JsonCompareSupport.jsonEquals
import org.scalatest.{FreeSpec, Matchers}

import scala.io.Source

class ResponseMaskServiceTest extends FreeSpec with Matchers {
  "should mask properly the response with documentdata node" in {
    test("/pii/mask/response_documentdata.json", "/pii/mask/response_documentdata_masked.json")
  }

  "should mask properly the response with prefill" in {
    test("/pii/mask/response_prefill.json", "/pii/mask/response_prefill_masked.json")
  }

  "should mask properly the response with kycPlus" in {
    test("/pii/mask/response_kycPlus.json", "/pii/mask/response_kycPlus_masked.json")
  }

  "should mask properly the response with deceasedcheck" in {
    test("/pii/mask/response_deceasedcheck.json", "/pii/mask/response_deceasedcheck_masked.json")
  }

  private def test(originalRes: String, maskedRes: String): Unit = {
    val originalResponse = res(originalRes)
    val expectedResponse = res(maskedRes)
    val actualResponse = ResponseMaskService.mask(originalResponse)
    jsonEquals(actualResponse, expectedResponse) shouldBe true
  }

  private def res(res: String): String = Source.fromInputStream(getClass.getResourceAsStream(res)).mkString
}
