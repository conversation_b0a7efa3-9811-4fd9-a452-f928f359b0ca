package me.socure.transactionauditing.pii.mask

import me.socure.transactionauditing.pii.mask.JsonCompareSupport._
import org.scalatest.{FreeSpec, Matchers}

import scala.io.Source

class ParamsMaskServiceTest extends FreeSpec with Matchers {
  "should mask properly with addresses node for full params" in {
    test("/pii/mask/params_full_with_addresses.json", "/pii/mask/params_masked_full_with_addresses.json")
  }

  "should mask properly with addresses node for partial params" in {
    test("/pii/mask/params_partial_with_addresses.json", "/pii/mask/params_masked_partial_with_addresses.json")
  }

  "should mask properly the new params added for accountintelligence module" in {
    test("/pii/mask/params_full_with_accountintelligence.json", "/pii/mask/params_masked_full_with_accountintelligence.json")
  }

  private def test(originalRes: String, maskedRes: String): Unit = {
    val originalParams = res(originalRes)
    val expectedParams = res(maskedRes)
    val actualParams = ParamsMaskService.mask(originalParams)
    jsonEquals(actualParams, expectedParams) shouldBe true
  }

  private def res(res: String): String = Source.fromInputStream(getClass.getResourceAsStream(res)).mkString
}
