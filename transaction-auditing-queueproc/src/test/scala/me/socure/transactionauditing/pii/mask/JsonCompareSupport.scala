package me.socure.transactionauditing.pii.mask

import org.json4s.jackson.JsonMethods
import org.json4s.{DefaultFormats, Formats}

object JsonCompareSupport {

  private implicit val jsonFormats: Formats = DefaultFormats

  def jsonEquals(j1: String, j2: String): Boolean = {
    val json1 = JsonMethods.parse(j1)
    val json2 = JsonMethods.parse(j2)
    val diff = json1.diff(json2)
    diff.added.toOption.isEmpty && diff.changed.toOption.isEmpty && diff.deleted.toOption.isEmpty
  }
}
