package me.socure.transactionauditing.pii.mask

import akka.actor.ActorSystem
import akka.stream.{ActorMaterializer, Materializer}
import me.socure.account.client.superadmin.AccountInfoClient
import me.socure.common.transaction.id.TrxId
import me.socure.model.BusinessUserRoles
import me.socure.model.encryption.ApiKeyString
import me.socure.transactionauditing.pii.mask.JsonCompareSupport._
import me.socure.transactionauditing.s3.storage.S3TransactionStorage
import me.socure.transactionauditing.sqs.model.{SqsAPITransactionValueGenerator, SqsApiTransaction}
import org.json4s.Formats
import org.json4s.jackson.JsonMethods
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{FreeSpec, Matchers}

import scala.concurrent.{ExecutionContext, Future}
import scala.io.Source

/**
  * Created by jamesanto on 5/8/17.
  */
class PiiMaskServiceTest extends FreeSpec with Matchers with MockFactory with ScalaFutures {

  private implicit val trxId: TrxId = TrxId("test")
  private val response = res("/pii/mask/response_prefill.json")
  private val maskedResponse = res("/pii/mask/response_prefill_masked.json")
  private val parameters = res("/pii/mask/params_full.json")
  private val maskedParameters = res("/pii/mask/params_masked_full.json")
  private val requestUri = "/api/2.5/EmailAuthScore?email=<EMAIL>&country=US&state=NY&debug=true&forcerefresh=true&datascience=true&showdecision=true&socurekey=ak&customeruserid=cuid&kyc=true&nationalid=*********&dob=1990-01-01&physicaladdress=New%20York&city=New%20York&zip=12345&mobilenumber=+**********&fullname=John%20Doe&impersonatorapikey=iak&nocache=1&runid=123&geocode=1%202&watchlist=1&driverlicense=12345&ipaddress=*******&physicaladdress2=pa2&firstname=John&surname=Doe&companyname=Socure"
  private val maskedRequestUri = "/api/2.5/EmailAuthScore?email=[PROVIDED]&country=[PROVIDED]&state=[PROVIDED]&debug=true&forcerefresh=true&datascience=true&showdecision=true&socurekey=ak&customeruserid=cuid&kyc=true&nationalid=[PROVIDED]&dob=[PROVIDED]&physicaladdress=[PROVIDED]&city=[PROVIDED]&zip=[PROVIDED]&mobilenumber=[PROVIDED]&fullname=[PROVIDED]&impersonatorapikey=iak&nocache=1&runid=123&geocode=[PROVIDED]&watchlist=1&driverlicense=[PROVIDED]&ipaddress=[PROVIDED]&physicaladdress2=[PROVIDED]&firstname=[PROVIDED]&surname=[PROVIDED]&companyname=[PROVIDED]"

  private val parametersPartial = res("/pii/mask/params_partial.json")
  private val maskedParametersPartial = res("/pii/mask/params_masked_partial.json")
  private val requestUriPartial = "/api/2.5/EmailAuthScore?email=<EMAIL>&country=US&state=NY&debug=true&forcerefresh=true&datascience=true&showdecision=true&socurekey=ak&customeruserid=cuid&kyc=true&nationalid=%20%20&dob=1990-01-01&physicaladdress=New%20York&city=New%20York&zip=12345&mobilenumber=**********&fullname=John%20Doe&impersonatorapikey=iak&nocache=1&runid=123&geocode=1%202&watchlist=1&driverlicense=12345&ipaddress=*******&physicaladdress2=pa2&firstname=John"
  private val maskedRequestUriPartial = "/api/2.5/EmailAuthScore?email=[PROVIDED]&country=[PROVIDED]&state=[PROVIDED]&debug=true&forcerefresh=true&datascience=true&showdecision=true&socurekey=ak&customeruserid=cuid&kyc=true&nationalid=%20%20&dob=[PROVIDED]&physicaladdress=[PROVIDED]&city=[PROVIDED]&zip=[PROVIDED]&mobilenumber=[PROVIDED]&fullname=[PROVIDED]&impersonatorapikey=iak&nocache=1&runid=123&geocode=[PROVIDED]&watchlist=1&driverlicense=[PROVIDED]&ipaddress=[PROVIDED]&physicaladdress2=[PROVIDED]&firstname=[PROVIDED]"

  private val parametersNotEncoded = res("/pii/mask/params_not_encoded.json")

  private val errorBucket = "transaction_errors"
  private val apiKeyString = ApiKeyString("api key")

  private implicit val executionContext:ExecutionContext = ExecutionContext.global
  private implicit val actorSystem: ActorSystem = ActorSystem("test")
  private implicit val materializer: Materializer = ActorMaterializer()
  private implicit val formats: Formats = org.json4s.DefaultFormats

  private val accountInfoClient = mock[AccountInfoClient]
  private val s3StorageService: S3TransactionStorage = null
  private val service = new PiiMaskService(
    accountInfoClient = accountInfoClient,
    s3StorageService = s3StorageService,
    errorBucket = errorBucket,
    paramsMaskService = ParamsMaskService,
    responseMaskService = ResponseMaskService
  )

  private def trx(): SqsApiTransaction = {
    SqsAPITransactionValueGenerator.aAPITransaction(
      apiKey = apiKeyString.value,
      apiName = "/api/2.5/EmailAuthScore",
      parameters = parameters,
      requestURI = requestUri,
      customerUserId = Some("cuid")
    )
  }

  "PiiMaskService" - {
    Seq(
      (false, parameters, maskedParameters, requestUri, maskedRequestUri),
      (true, parametersPartial, maskedParametersPartial, requestUriPartial, maskedRequestUriPartial)
    ) foreach { case (isPartial, params, maskedParams, reqUri, maskedReqUri) =>
      s"when ${if (isPartial) "partial" else "full"} data available" - {
        "should mask pii fields properly" in {
          val piiFields = PiiFields(
            parameters = Some(params),
            requestUri = Some(reqUri),
            response = Some(response)
          )
          val maskedPiiFields = service.mask(piiFields)
          val expectedMaskedPiiFields = PiiFields(
            parameters = Some(maskedParams),
            requestUri = Some(maskedReqUri),
            response = Some(maskedResponse)
          )
          jsonEquals(maskedPiiFields.parameters.get, expectedMaskedPiiFields.parameters.get) shouldBe true
        }

        "should handle null pii fields properly" in {
          val piiFields = PiiFields(
            parameters = None,
            requestUri = None,
            response = None
          )
          val maskedPiiFields = service.mask(piiFields)
          val expectedMaskedPiiFields = PiiFields(
            parameters = None,
            requestUri = None,
            response = None
          )
          jsonEquals(maskedPiiFields.parameters.getOrElse("{}"), expectedMaskedPiiFields.parameters.getOrElse("{}")) shouldBe true
        }
      }
    }

    "should handle invalid url encoding gracefully" in {
      val piiFields = PiiFields(
        parameters = Some(parametersNotEncoded),
        requestUri = None,
        response = None
      )
      val maskedPiiFields = service.mask(piiFields = piiFields)
      (JsonMethods.parse(maskedPiiFields.parameters.get) \ "firstname").extract[String] shouldBe PiiMaskService.providedPlaceholder
    }

    "should not mask API Transaction if account does not have PII_MASK permission" in {
      (accountInfoClient.hasRole _).expects(apiKeyString, BusinessUserRoles.MASK_PII.id).returns(Future.successful(Right(false)))
      val aPITransaction = trx()
      val maskedAPITransactionFuture = service.mask(aPITransaction)
      whenReady(maskedAPITransactionFuture) { maskedApiTransaction =>
        maskedApiTransaction.parameters shouldBe parameters
        maskedApiTransaction.requestURI shouldBe requestUri
        maskedApiTransaction.customerUserId shouldBe aPITransaction.customerUserId
      }
    }
  }

  private def res(res: String): String = Source.fromInputStream(getClass.getResourceAsStream(res)).mkString
}
