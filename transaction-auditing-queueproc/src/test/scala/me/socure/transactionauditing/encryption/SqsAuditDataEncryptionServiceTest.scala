package me.socure.transactionauditing.encryption

import me.socure.common.random.Random
import me.socure.transaction.auditing.encryption.common.AuditDataPii
import me.socure.transaction.auditing.encryption.encryptor.AuditDataEncryptor
import me.socure.transaction.auditing.encryption.encryptor.v2.AuditDataEncryptorV2
import me.socure.transactionauditing.sqs.model.SqsAPITransactionValueGenerator
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Milliseconds, Seconds, Span}
import org.scalatest.{FreeSpec, Matchers}

import scala.concurrent.{ExecutionContext, Future}

class SqsAuditDataEncryptionServiceTest extends FreeSpec with Matchers with ScalaFutures with MockFactory {

  private implicit val ec: ExecutionContext = ExecutionContext.Implicits.global
  private implicit val patienceConf: PatienceConfig = PatienceConfig(
    timeout = Span(5, Seconds),
    interval = Span(50, Milliseconds)
  )

  private class MockableAuditDataEncryptor extends AuditDataEncryptorV2(null, null, null)

  private val encryptor = mock[MockableAuditDataEncryptor]
  private val service = new SqsAuditDataEncryptionService(encryptor)

  "should not encrypt customer user id" in {
    val transaction = SqsAPITransactionValueGenerator
      .aAPITransaction()
      .copy(customerUserId = Some(Random.alphaNumeric(10)))

    val auditDataPlain = AuditDataPii(
      requestUri = Option(transaction.requestURI),
      parameters = Option(transaction.parameters),
      details = Option(transaction.details),
      response = Option(transaction.response),
      customerUserId = None
    )

    val encryptedRequestUri = Random.alphaNumeric(10)
    val encryptedParameters = Random.alphaNumeric(10)
    val encryptedDetails = Random.alphaNumeric(10)
    val encryptedResponse = Random.alphaNumeric(10)
    val encryptedCustomerUserId = Random.alphaNumeric(10)

    val auditDataEncrypted = AuditDataPii(
      requestUri = Some(encryptedRequestUri),
      parameters = Some(encryptedParameters),
      details = Some(encryptedDetails),
      response = Some(encryptedResponse),
      customerUserId = Some(encryptedCustomerUserId)
    )

    val expectedTransaction = transaction.copy(
      requestURI = encryptedRequestUri,
      parameters = encryptedParameters,
      details = encryptedDetails,
      response = encryptedResponse
    )

    (encryptor.encrypt(_: Long, _: String, _: AuditDataPii))
      .expects(transaction.accountId, transaction.apiKey, auditDataPlain)
      .returns(Future.successful(auditDataEncrypted))

    whenReady(service.encryptTransaction(transaction))(_ shouldBe expectedTransaction)
  }
}
