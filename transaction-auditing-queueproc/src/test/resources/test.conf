withDBMigration=true

database {
  audit {
    jdbcUrl="testconnectionstring"
    user=vpcadmin
    password="password"
    driver="com.mysql.cj.jdbc.Driver"

    maxPoolSize=25
    minPoolSize=5
    testConnectionOnCheckIn=true
    testConnectionOnCheckOut=false
    idleConnectionTestPeriod=0
    dataSourceName="socureauditing"
  }
}

account.service {
  endpoint = "https://socure-account-service-stage2.us-east-1.elasticbeanstalk.com"
}

memcached {
  host=stage-vpc-east-cache.ps2mlp.cfg.use1.cache.amazonaws.com
  port=11211
}

client.specific.encryption {
  encryption.context.account_id.key = "socure_account_id"
  kms.ids {

    "us-east-1" = """arn:aws:kms:us-east-1:************:alias/client-specific-encryption-stage"""
    "us-west-1" = """arn:aws:kms:us-west-1:************:alias/client-specific-encryption-stage"""
  }
}

transaction-auditing {
  threadpool {
    transaction {
      poolSize=125
    }
    thirdparty {
      poolSize=175
    }
    rest {
      poolSize=50
    }
  }

  server {
    port = 5000
  }

  aws {
    access.key="testKey"
    access.secret="testSecret"

    sqs {
      region=us-east-1
      transaction {
        queueName=transaction-auditing-stage
        waitTimeSeconds=20
        maxBatchSize=10
        maxBufferSize=60
        parallelism=30
      }
      third-party {
        queueName=third-party-transaction-auditing-stage
        waitTimeSeconds=20
        maxBatchSize=10
        maxBufferSize=30
        parallelism=60
      }
    }

    s3 {
      largefiles {
        folder="sqs-storage-local"
      }
      third-party {
        region=us-east-1
        bucket="thirdparty-stats-stage"
      }
    }
  }
}

#============= Datadog config =============#
datadog {
  series_url="https://app.datadoghq.com/api/v1/series"
  api_key="""ENC(0mr4Kt+F5JGy9znfYWzRQC2JHuyhbHCPU4pzLxOL/DN3f/3HBSwGT34L/yyRW/YyhaCDL40api9fg4+c3do3fQ==)"""
}
#============= Datadog config =============#

#============= Mailgun config =============#
mailgun {
  endpoint="https://api.mailgun.net/v2/socure.com/messages"
  key="""ENC(ibSiwI/xh3BYUTxuV1A8DrltfPfAx3SgHhcwQjcH2wYuQ8vMvR6xRdxBYFy5iw+FMNRFFdYiskBLqvRnzn0gSA==)"""
  domain="socure.com"
  subject="Model Monitoring Metrics Reporting Failure - Stage"
  from="<EMAIL>"
  to=["<EMAIL>"]
}
#============= Mailgun config =============#