package me.socure.transaction.auditing.encryption.encryptor

import me.socure.account.client.encryption.EncryptionKeysClient
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.transaction.auditing.encryption.common.{AccountIdResolver, AuditDataPii}
import me.socure.model.encryption.{AccountId, ApiKeyString}
import me.socure.transaction.auditing.encryption.common.AuditDataPii
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by jamesanto on 4/26/17.
  */
class AuditDataEncryptor(
                          encryptionKeysClient: EncryptionKeysClient,
                          encryptor: Encryptor
                        )(implicit exe: ExecutionContext) {

  private val logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get("transaction.auditing." + this.getClass.getSimpleName)

  def encrypt(accountId: Long, apiKey: String, auditDataPii: AuditDataPii): Future[AuditDataPii] = {
    val accountIdFinalFuture = new AccountIdResolver(encryptionKeysClient).resolve(accountId = Some(AccountId(accountId)),
      apiKeyString = Some(ApiKeyString(apiKey)))
    accountIdFinalFuture.flatMap { accountIdOpt =>
      encrypt(accountIdOpt = accountIdOpt, auditDataPii)
    }
  }

  def encrypt(accountIdOpt: Option[AccountId], auditDataPii: AuditDataPii): Future[AuditDataPii] = {

    val sanitizedPii = auditDataPii.sanitize

    def enc(s: Option[String], original: Option[String], piiType: String): Future[Option[String]] = {
      (accountIdOpt, s) match {
        case (Some(accountId), Some(str)) =>
          logger.debug(s"Encrypting data[$piiType] with account id $accountId")
          encryptor.encrypt(accountId = accountId.value, data = str).map(Option(_))
        case (None, Some(str)) =>
          logger.debug(s"Encrypting data[$piiType] with account id = 0")
          encryptor.encrypt(accountId = 0, data = str).map(Option(_))
        case _ =>
          logger.debug(s"Not encrypting data[$piiType] because the account id and data are None")
          Future.successful(original)
      }
    }
    metrics.timeFuture("encrypt_call.duration") {
      for {
        requestUri <- enc(sanitizedPii.requestUri, auditDataPii.requestUri, "request_uri")
        parameters <- enc(sanitizedPii.parameters, auditDataPii.parameters, "parameters")
        details <- enc(sanitizedPii.details, auditDataPii.details, "details")
        response <- enc(sanitizedPii.response, auditDataPii.response, "response")
      } yield AuditDataPii(
        requestUri = requestUri,
        parameters = parameters,
        details = details,
        response = response,
        customerUserId = sanitizedPii.customerUserId
      )
    }
  }
}
