package me.socure.transaction.auditing.encryption.encryptor.v2

import java.nio.charset.StandardCharsets
import java.util.Base64
import me.socure.common.crypto.generic.GenericEncryptor
import me.socure.common.crypto.kms.AccountData
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.model.encryption.{AccountId, ApiKeyString}
import me.socure.transaction.auditing.encryption.common.{AccountIdResolver, AuditDataPii, EncryptionDecryptionDecider, ResponseAndParamsTransformer}
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}

class AuditDataEncryptorV2(accountIdResolver: AccountIdResolver,
                           encryptionDecryptionDecider: EncryptionDecryptionDecider,
                           encryptor: GenericEncryptor[AccountData])
                          (implicit exe: ExecutionContext) {

  private val logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get("transaction.auditing." + this.getClass.getSimpleName)

  def encrypt(accountId: Long, apiKey: String, auditDataPii: AuditDataPii): Future[AuditDataPii] = {
    val accountIdFinalFuture = accountIdResolver.resolve(accountId = Some(AccountId(accountId)),
      apiKeyString = Some(ApiKeyString(apiKey)))
    accountIdFinalFuture flatMap { accountIdOpt =>
      encryptInternal(accountIdOpt = Some(accountIdOpt.getOrElse(AccountId(0))), apiKey, auditDataPii)
    }
  }

  def encryptInternal(accountIdOpt: Option[AccountId], apiKey: String, auditDataPii: AuditDataPii): Future[AuditDataPii] = {

    def enc(s: Option[String], original: Option[String], piiType: String): Future[Option[String]] = {
      (accountIdOpt, s) match {
        case (Some(accountId), Some(str)) =>
          logger.debug(s"Encrypting data[$piiType] with account id $accountId")
          encryptor.encrypt(AccountData(accountId.value, str.getBytes(StandardCharsets.UTF_8)))
            .map(res => Some(Base64.getEncoder.encodeToString(res.getResult)))
        case (None, Some(str)) =>
          logger.debug(s"Encrypting data[$piiType] with account id = 0")
          encryptor.encrypt(AccountData(0, str.getBytes(StandardCharsets.UTF_8)))
            .map(res => Some(Base64.getEncoder.encodeToString(res.getResult)))
        case _ =>
          logger.debug(s"Not encrypting data[$piiType] because the data is None")
          Future.successful(original)
      }
    }

    encryptionDecryptionDecider.canProcess(accountIdOpt.get, Some(apiKey)) flatMap {
      case true =>
        val sanitizedPii = auditDataPii.sanitize
        metrics.timeFuture("encrypt_call.duration") {
          for {
            requestUri <- enc(sanitizedPii.requestUri, auditDataPii.requestUri, "request_uri")
            parameters <- enc(sanitizedPii.parameters, auditDataPii.parameters, "parameters")
            details <- enc(sanitizedPii.details, auditDataPii.details, "details")
            response <- enc(sanitizedPii.response, auditDataPii.response, "response")
          } yield AuditDataPii(
            requestUri = requestUri,
            parameters = parameters,
            details = details,
            response = response,
            customerUserId = sanitizedPii.customerUserId
          )
        }
      case false =>
        logger.debug(s"Not encrypting data since encryptionDecryptionDecider returned false for $accountIdOpt")
        metrics.increment("legacy.encryption.disabled", s"account_id:${accountIdOpt.fold("0")(_.value.toString)}", "flow:enc")
        Future.successful(ResponseAndParamsTransformer.transformForWrite(auditDataPii))
    }
  }

}
