package me.socure.transaction.auditing.encryption.encryptor

import com.amazonaws.encryptionsdk.CryptoMaterialsManager
import com.amazonaws.encryptionsdk.kms.KmsMasterKey
import me.socure.crypto.encryptor.key.AccountKeyBasedEncryptor
import me.socure.crypto.key.common._

import scala.concurrent.ExecutionContext

/**
  * Created by jam<PERSON><PERSON> on 4/24/17.
  */
object EncryptorFactory {
  def create(
              cryptoMaterialsManagerAccountsService: CryptoMaterialsManagerAccountsService,
              cryptoMaterialsManager: CryptoMaterialsManager,
              encryptionContextAccountIdKey: String
            )(implicit exe: ExecutionContext): Encryptor = {

    val keyBasedEncryptor = new AccountKeyBasedEncryptor[KmsMasterKey](
      cryptoMaterialsManagerAccountsService = cryptoMaterialsManagerAccountsService,
      encryptionContextValidator = EncryptionContextValidator,
      awsCrypto = AwsCryptoFactory.create(),
      cryptoMaterialsManager = cryptoMaterialsManager,
      encryptionContextAccountIdKey = encryptionContextAccountIdKey
    )
    new Encryptor(
      encryptor = keyBasedEncryptor,
      encryptionContextAccountIdKey = encryptionContextAccountIdKey
    )
  }
}
