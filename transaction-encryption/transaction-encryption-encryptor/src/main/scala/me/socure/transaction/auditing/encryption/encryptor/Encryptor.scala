package me.socure.transaction.auditing.encryption.encryptor

import java.nio.charset.StandardCharsets
import java.util.Base64

import me.socure.transaction.auditing.encryption.common.EncryptionStatusHandler
import me.socure.crypto.encryptor.{Encryptor => CEncryptor}
import me.socure.crypto.key.common.DataWithEncryptionContext
import me.socure.transaction.auditing.encryption.common.EncryptionStatusHandler

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by jamesanto on 4/24/17.
  */
class Encryptor(
                 encryptor: CEncryptor[DataWithEncryptionContext[Array[Byte]], Array[Byte]],
                 encryptionContextAccountIdKey: String
               )(implicit exe: ExecutionContext) {
  def encrypt(accountId: Long, data: Array[Byte]): Future[Array[Byte]] = {
    val encryptionContext = Map(
      encryptionContextAccountIdKey -> String.valueOf(accountId)
    )
    val result = encryptor.encrypt(DataWithEncryptionContext(
      encryptionContext = encryptionContext,
      data = data
    ))
    EncryptionStatusHandler.handle(result, "encrypt")
  }

  def encrypt(accountId: Long, data: String): Future[String] = {
    val inputBytes = data.getBytes(StandardCharsets.UTF_8)
    encrypt(
      accountId = accountId,
      data = inputBytes
    ).map {
      case output if output.deep == inputBytes.deep => data
      case output => Base64.getEncoder.encodeToString(output)
    }
  }
}
