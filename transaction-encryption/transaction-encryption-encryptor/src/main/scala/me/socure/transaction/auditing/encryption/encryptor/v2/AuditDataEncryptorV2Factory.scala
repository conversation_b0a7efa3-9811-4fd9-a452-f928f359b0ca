package me.socure.transaction.auditing.encryption.encryptor.v2

import java.net.InetSocketAddress
import com.typesafe.config.Config
import me.socure.account.client.encryption.{CachedEncryptionKeysClient, EncryptionKeysClient, EncryptionKeysClientFactory, EncryptionKeysClientV2Factory}
import me.socure.account.client.superadmin.{AccountInfoClient, AccountInfoClientFactory}
import me.socure.common.crypto.kms.KmsEncryptorDecryptorFactory
import me.socure.common.environment.AppRegion
import me.socure.transaction.auditing.encryption.common.{AccountIdResolver, AccountServiceEncryptedDataKeysProvider, EncryptionDecryptionDecider}
import net.spy.memcached.MemcachedClient

import scala.concurrent.duration._
import scala.concurrent.ExecutionContext
import scala.util.Try

object AuditDataEncryptorV2Factory {

  def create(config: Config, appRegion: AppRegion)
            (implicit ec: ExecutionContext): AuditDataEncryptorV2 = {
    val accountServiceConfig = config.getConfig("account.service")
    val accountServiceEndpoint = accountServiceConfig.getString("endpoint")
    val memcachedClient = new MemcachedClient(new InetSocketAddress(config.getConfig("memcached").getString("host"), config.getConfig("memcached").getInt("port")))

    val encryptionKeysClient: CachedEncryptionKeysClient = EncryptionKeysClientFactory.createCached(
      accountServiceEndpoint = accountServiceEndpoint,
      memcachedClient = memcachedClient,
      ttl = Some(1.minute)
    )
    val encryptionKeysClientV2 = EncryptionKeysClientV2Factory.createCachedUsingConfig(accountServiceConfig,
      memcachedClient,
      memcachedTtl = Try(Option(Duration(config.getString("memcached.ttl")))).getOrElse(Option(Duration("24 hours"))))
    val accountInfoClient: AccountInfoClient = AccountInfoClientFactory.createCached(
      accountServiceEndpoint = accountServiceEndpoint,
      memcachedClient = memcachedClient,
      ttl = Some(1.minute)
    )
    val accountIdResolver = new AccountIdResolver(encryptionKeysClient)
    val encryptionDecryptionDecider = new EncryptionDecryptionDecider(encryptionKeysClient, accountInfoClient)
    val encryptedDataKeysProvider = new AccountServiceEncryptedDataKeysProvider(encryptionKeysClientV2, appRegion)
    val encryptor = KmsEncryptorDecryptorFactory.createEncryptor(encryptedDataKeysProvider)
    new AuditDataEncryptorV2(
      accountIdResolver = accountIdResolver,
      encryptionDecryptionDecider = encryptionDecryptionDecider,
      encryptor = encryptor
    )
  }

}
