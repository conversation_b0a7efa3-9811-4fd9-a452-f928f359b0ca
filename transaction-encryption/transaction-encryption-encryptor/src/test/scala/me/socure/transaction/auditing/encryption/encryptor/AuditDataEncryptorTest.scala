package me.socure.transaction.auditing.encryption.encryptor

import me.socure.account.client.encryption.EncryptionKeysClient
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.transaction.auditing.encryption.common.AuditDataPii
import me.socure.common.exception.AuthenticationException
import me.socure.model.encryption.{AccountId, ApiKeyString}
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{FreeSpec, Matchers}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by jamesanto on 5/3/17.
  */
class AuditDataEncryptorTest extends FreeSpec with Matchers with ScalaFutures with MockFactory {

  implicit val defaultPatience = PatienceConfig(timeout = Span(10, Seconds), interval = Span(50, Millis))
  private implicit val ec = ExecutionContext.Implicits.global
  private val encryptionKeysClient = mock[EncryptionKeysClient]

  private class MockableEncryptor extends Encryptor(null, null)

  private val encryptor = mock[MockableEncryptor]
  private val auditDataEncryptor = new AuditDataEncryptor(
    encryptionKeysClient = encryptionKeysClient,
    encryptor = encryptor
  )
  private val auditDataPii = AuditDataPii(
    requestUri = Some("req_uri"),
    parameters = Some("params"),
    details = Some("details"),
    response = Some("response"),
    customerUserId = Some("cuid")
  )

  private val auditDataPiiEncrypted = AuditDataPii(
    requestUri = Some("req_uri_enc"),
    parameters = Some("params_enc"),
    details = Some("details_enc"),
    response = Some("response_enc"),
    customerUserId = Some("cuid")
  )

  private val validAcccountId = AccountId(5)
  private val unknownAccountId = AccountId(0)
  private val apiKeyString = ApiKeyString("api_key_1")

  "AuditDataEncryptor" - {
    "should encrypt data when valid account id is provided" in {
      testEnc(accountIdExpected = validAcccountId, accountIdActual = validAcccountId)
    }

    "should encrypt data when valid account id is not provided but valid api key is provided" in {
      (encryptionKeysClient.getAccountIdByApiKey _).expects(apiKeyString).returns(Future.successful(Right(validAcccountId)))
      testEnc(accountIdExpected = validAcccountId, accountIdActual = unknownAccountId)
    }

    "should encrypt data when valid account id is not provided but and api key is also not found" in {
      (encryptionKeysClient.getAccountIdByApiKey _).expects(apiKeyString).returns(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))))
      testEnc(accountIdExpected = unknownAccountId, accountIdActual = unknownAccountId)
    }

    "should not encrypt data when valid account id is not provided and account service returns an error for account id by api key endpoint" in {
      (encryptionKeysClient.getAccountIdByApiKey _).expects(apiKeyString).returns(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
      whenReady(auditDataEncryptor.encrypt(accountId = unknownAccountId.value, apiKey = apiKeyString.value, auditDataPii = auditDataPii).failed) { result =>
        result shouldBe an[AuthenticationException]
      }
    }

    "should not encrypt empty or null data" in {
      val auditDataPiiEmpty = AuditDataPii(
        requestUri = Some(""),
        parameters = Some("   "),
        details = None,
        response = None,
        customerUserId = None
      )

      whenReady(auditDataEncryptor.encrypt(accountId = validAcccountId.value, apiKey = apiKeyString.value, auditDataPii = auditDataPiiEmpty)) { result =>
        result shouldBe auditDataPiiEmpty
      }
    }
  }

  private def testEnc(accountIdExpected: AccountId, accountIdActual: AccountId): Unit = {
    (encryptor.encrypt(_: Long, _: String)).expects(accountIdExpected.value, auditDataPii.requestUri.get).returns(Future.successful(auditDataPiiEncrypted.requestUri.get))
    (encryptor.encrypt(_: Long, _: String)).expects(accountIdExpected.value, auditDataPii.parameters.get).returns(Future.successful(auditDataPiiEncrypted.parameters.get))
    (encryptor.encrypt(_: Long, _: String)).expects(accountIdExpected.value, auditDataPii.details.get).returns(Future.successful(auditDataPiiEncrypted.details.get))
    (encryptor.encrypt(_: Long, _: String)).expects(accountIdExpected.value, auditDataPii.response.get).returns(Future.successful(auditDataPiiEncrypted.response.get))

    whenReady(auditDataEncryptor.encrypt(accountId = accountIdActual.value, apiKey = apiKeyString.value, auditDataPii = auditDataPii)) { result =>
      result shouldBe auditDataPiiEncrypted
    }
  }
}
