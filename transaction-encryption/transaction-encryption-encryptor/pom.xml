<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>transaction-encryption</artifactId>
        <groupId>me.socure</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>transaction-encryption-encryptor</artifactId>

    <dependencies>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>transaction-encryption-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-crypto-encryptor-key</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-crypto-kms</artifactId>
        </dependency>
    </dependencies>
</project>
