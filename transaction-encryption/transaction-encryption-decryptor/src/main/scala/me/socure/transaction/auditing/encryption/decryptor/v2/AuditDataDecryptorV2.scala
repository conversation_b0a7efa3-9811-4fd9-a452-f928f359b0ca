package me.socure.transaction.auditing.encryption.decryptor.v2

import me.socure.common.crypto.generic.GenericDecryptor
import me.socure.common.crypto.kms.AccountData
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.model.encryption.{AccountId, ApiKeyString}
import me.socure.transaction.auditing.encryption.common.{AccountIdResolver, AuditDataPii, EncryptionDecryptionDecider, ResponseAndParamsTransformer}
import org.slf4j.LoggerFactory

import java.nio.charset.StandardCharsets
import java.util.Base64
import scala.concurrent.{ExecutionContext, Future}

class AuditDataDecryptorV2(accountIdResolver: AccountIdResolver,
                           encryptionDecryptionDecider: EncryptionDecryptionDecider,
                           decryptor: GenericDecryptor[AccountData]
                          )(implicit exe: ExecutionContext) {
  private val logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get("transaction.auditing." + this.getClass.getSimpleName)

  def decrypt(accountId: Option[Long], apiKey: Option[String], auditDataPii: AuditDataPii): Future[AuditDataPii] = {
    val accountIdFinalFuture = accountIdResolver.resolve(accountId = accountId.map(AccountId),
      apiKeyString = apiKey.map(ApiKeyString))
    accountIdFinalFuture flatMap { accountIdOpt =>
      decryptInternal(accountIdOpt = Some(accountIdOpt.getOrElse(AccountId(0))), apiKey, auditDataPii)
    }
  }

  def decryptInternal(accountIdOpt: Option[AccountId], apiKey: Option[String], auditDataPii: AuditDataPii): Future[AuditDataPii] = {

    def dec(s: Option[String], original: Option[String], piiType: String): Future[Option[String]] = {
      (accountIdOpt, s) match {
        case (Some(accountId), Some(str)) =>
          decryptor.decrypt(AccountData(accountId.value, Base64.getDecoder.decode(str)))
            .map(res => Some(new String(res.getResult, StandardCharsets.UTF_8)))
        case (None, Some(str)) =>
          decryptor.decrypt(AccountData(0, Base64.getDecoder.decode(str)))
            .map(res => Some(new String(res.getResult, StandardCharsets.UTF_8)))
        case _ =>
          Future.successful(original)
      }
    }

    encryptionDecryptionDecider.canProcess(accountIdOpt.get, apiKey) flatMap  {
      case true =>
        val sanitizedPii = auditDataPii.sanitize
        for {
          requestUri <- dec(sanitizedPii.requestUri, auditDataPii.requestUri, "request_uri")
          parameters <- dec(sanitizedPii.parameters, auditDataPii.parameters, "parameters")
          details <- dec(sanitizedPii.details, auditDataPii.details, "details")
          response <- dec(sanitizedPii.response, auditDataPii.response, "response")
        } yield AuditDataPii(
          requestUri = requestUri,
          parameters = parameters,
          details = details,
          response = response,
          customerUserId = sanitizedPii.customerUserId //FIXME: Update this once this column is migrated to a larger space.
        )
      case false =>
        metrics.increment("legacy.decryption.disabled", s"account_id:${accountIdOpt.fold("0")(_.value.toString)}", "flow:dec")
        Future.successful(ResponseAndParamsTransformer.transformForRead(auditDataPii))
    }

  }
}
