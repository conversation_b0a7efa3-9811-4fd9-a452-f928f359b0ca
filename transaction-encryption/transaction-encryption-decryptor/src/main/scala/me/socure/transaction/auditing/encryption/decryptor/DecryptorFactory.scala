package me.socure.transaction.auditing.encryption.decryptor

import com.amazonaws.auth.AWSCredentialsProvider
import com.amazonaws.encryptionsdk.CryptoMaterialsManager
import com.amazonaws.encryptionsdk.kms.KmsMasterKey
import com.amazonaws.regions.Regions
import me.socure.account.client.encryption.EncryptionKeysClient
import me.socure.crypto.decryptor.key.AccountKeyBasedDecryptor
import me.socure.crypto.key.common._

import scala.concurrent.ExecutionContext

/**
  * Created by jam<PERSON><PERSON> on 4/24/17.
  */
object DecryptorFactory {
  def create(cryptoMaterialsManagerAccountsService: CryptoMaterialsManagerAccountsService,
              cryptoMaterialsManager: CryptoMaterialsManager,
              encryptionContextAccountIdKey: String
            )(implicit exe: ExecutionContext): Decryptor = {

    val keyBasedDecryptor = new AccountKeyBasedDecryptor[KmsMasterKey](
      cryptoMaterialsManagerAccountsService = cryptoMaterialsManagerAccountsService,
      cryptoMaterialsManager = cryptoMaterialsManager,
      encryptionContextValidator = EncryptionContextValidator,
      encryptionContextAccountIdKey = encryptionContextAccountIdKey,
      awsCrypto = AwsCryptoFactory.create()
    )
    new Decryptor(
      decryptor = keyBasedDecryptor,
      encryptionContextAccountIdKey = encryptionContextAccountIdKey
    )
  }
}
