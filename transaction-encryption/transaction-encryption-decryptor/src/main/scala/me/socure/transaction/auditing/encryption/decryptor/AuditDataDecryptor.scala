package me.socure.transaction.auditing.encryption.decryptor

import me.socure.account.client.encryption.EncryptionKeysClient
import me.socure.transaction.auditing.encryption.common.{AccountIdResolver, AuditDataPii}
import me.socure.model.encryption.{AccountId, ApiKeyString}
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by jamesanto on 4/26/17.
  */
class AuditDataDecryptor(
                          encryptionKeysClient: EncryptionKeysClient,
                          decryptor: Decryptor
                        )(implicit exe: ExecutionContext) {

  private val logger = LoggerFactory.getLogger(getClass)

  def decrypt(accountId: Option[Long], apiKey: Option[String], auditDataPii: AuditDataPii): Future[AuditDataPii] = {
    val accountIdFinalFuture = new AccountIdResolver(encryptionKeysClient).resolve(accountId = accountId.map(AccountId), apiKeyString = apiKey.map(ApiKeyString))
    accountIdFinalFuture.flatMap { accountIdOpt =>
      decrypt(accountIdOpt = accountIdOpt, auditDataPii)
    }
  }

  def decrypt(accountIdOpt: Option[AccountId], auditDataPii: AuditDataPii): Future[AuditDataPii] = {

    val sanitizedPii = auditDataPii.sanitize

    def dec(s: Option[String], original: Option[String], piiType: String): Future[Option[String]] = {
      (accountIdOpt, s) match {
        case (Some(accountId), Some(str)) =>
          decryptor.decrypt(accountId = accountId.value, data = str).map(Option(_))
        case (None, Some(str)) =>
          decryptor.decrypt(accountId = 0, data = str).map(Option(_))
        case _ =>
          Future.successful(original)
      }
    }

    for {
      requestUri <- dec(sanitizedPii.requestUri, auditDataPii.requestUri, "request_uri")
      parameters <- dec(sanitizedPii.parameters, auditDataPii.parameters, "parameters")
      details <- dec(sanitizedPii.details, auditDataPii.details, "details")
      response <- dec(sanitizedPii.response, auditDataPii.response, "response")
    } yield AuditDataPii(
      requestUri = requestUri,
      parameters = parameters,
      details = details,
      response = response,
      customerUserId = sanitizedPii.customerUserId //FIXME: Update this once this column is migrated to a larger space.
    )
  }
}
