package me.socure.transaction.auditing.encryption.decryptor

import java.nio.charset.StandardCharsets
import java.util.Base64

import me.socure.transaction.auditing.encryption.common.EncryptionStatusHandler
import me.socure.crypto.common.CryptoException
import me.socure.crypto.decryptor.{Decryptor => CDecryptor}
import me.socure.crypto.key.common.DataWithEncryptionContext
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

/**
  * Created by jamesanto on 4/24/17.
  */
class Decryptor(
                 decryptor: CDecryptor[DataWithEncryptionContext[Array[Byte]], Array[Byte]],
                 encryptionContextAccountIdKey: String
               )(implicit exe: ExecutionContext) {

  private val logger = LoggerFactory.getLogger(getClass)

  def decrypt(accountId: Long, data: Array[Byte]): Future[Array[Byte]] = {
    val encryptionContext = Map(
      encryptionContextAccountIdKey -> String.valueOf(accountId)
    )
    val result = decryptor.decrypt(DataWithEncryptionContext(
      encryptionContext = encryptionContext,
      data = data
    )).recoverWith {
      case e: Throwable =>
        logger.error(s"Error while decrypting with account id $accountId", e)
        decryptor.decryptAlternate(DataWithEncryptionContext(
          encryptionContext = encryptionContext,
          data = data
        ))
    }
    EncryptionStatusHandler.handle(result, "decrypt")
  }

  def decrypt(accountId: Long, data: String): Future[String] = {
    Try(Base64.getDecoder.decode(data)) match {
      case Success(base64DecodedData) => decrypt(
        accountId = accountId,
        data = base64DecodedData
      ).map(d => new String(d, StandardCharsets.UTF_8))
      case Failure(e: IllegalArgumentException) => //Not a valid base64 data. It was not encrypted in the first place. Just return it as it is.
        Future.successful(data)
      case Failure(e) => Future.failed(new CryptoException(Some("Unable to decrypt data"), Some(e)))
    }
  }
}
