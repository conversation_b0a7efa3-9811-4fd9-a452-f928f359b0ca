package me.socure.transaction.auditing.encryption.decryptor

import me.socure.account.client.encryption.EncryptionKeysClient
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.transaction.auditing.encryption.common.AuditDataPii
import me.socure.common.exception.AuthenticationException
import me.socure.model.encryption.{AccountId, ApiKeyString}
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{FreeSpec, Matchers}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by jamesanto on 5/3/17.
  */
class AuditDataDecryptorTest extends FreeSpec with Matchers with ScalaFutures with MockFactory {
  private implicit val ec = ExecutionContext.Implicits.global
  private val encryptionKeysClient = mock[EncryptionKeysClient]

  implicit override val patienceConfig = PatienceConfig(timeout = Span(10, Seconds), interval = Span(50, Millis))

  private class MockableDecryptor extends Decryptor(null, null)

  private val decryptor = mock[MockableDecryptor]
  private val auditDataDecryptor = new AuditDataDecryptor(
    encryptionKeysClient = encryptionKeysClient,
    decryptor = decryptor
  )
  private val auditDataPiiEncrypted = AuditDataPii(
    requestUri = Some("req_uri_enc"),
    parameters = Some("params_enc"),
    details = Some("details_enc"),
    response = Some("response_enc"),
    customerUserId = Some("cuid")
  )

  private val auditDataPiiDecrypted = AuditDataPii(
    requestUri = Some("req_uri_dec"),
    parameters = Some("params_dec"),
    details = Some("details_dec"),
    response = Some("response_dec"),
    customerUserId = Some("cuid")
  )

  private val validAcccountId = AccountId(5)
  private val unknownAccountId = AccountId(0)
  private val apiKeyString = ApiKeyString("api_key_1")

  "AuditDataEncryptor" - {
    "should decrypt data when valid account id is provided" in {
      testEnc(accountIdExpected = validAcccountId, accountIdActual = validAcccountId)
    }

    "should decrypt data when valid account id is not provided but valid api key is provided" in {
      (encryptionKeysClient.getAccountIdByApiKey _).expects(apiKeyString).returns(Future.successful(Right(validAcccountId)))
      testEnc(accountIdExpected = validAcccountId, accountIdActual = unknownAccountId)
    }

    "should decrypt data when valid account id is not provided but and api key is also not found" in {
      (encryptionKeysClient.getAccountIdByApiKey _).expects(apiKeyString).returns(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))))
      testEnc(accountIdExpected = unknownAccountId, accountIdActual = unknownAccountId)
    }

    "should not decrypt data when valid account id is not provided and account service returns an error for account id by api key endpoint" in {
      (encryptionKeysClient.getAccountIdByApiKey _).expects(apiKeyString).returns(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
      whenReady(auditDataDecryptor.decrypt(accountId = Some(unknownAccountId.value), apiKey = Some(apiKeyString.value), auditDataPii = auditDataPiiDecrypted).failed) { result =>
        result shouldBe an[AuthenticationException]
      }
    }

    "should not decrypt empty or null data" in {
      val auditDataPiiEmpty = AuditDataPii(
        requestUri = Some(""),
        parameters = Some("   "),
        details = None,
        response = None,
        customerUserId = None
      )

      whenReady(auditDataDecryptor.decrypt(accountId = Some(validAcccountId.value), apiKey = Some(apiKeyString.value), auditDataPii = auditDataPiiEmpty)) { result =>
        result shouldBe auditDataPiiEmpty
      }
    }
  }

  private def testEnc(accountIdExpected: AccountId, accountIdActual: AccountId): Unit = {
    (decryptor.decrypt(_: Long, _: String)).expects(accountIdExpected.value, auditDataPiiDecrypted.requestUri.get).returns(Future.successful(auditDataPiiDecrypted.requestUri.get))
    (decryptor.decrypt(_: Long, _: String)).expects(accountIdExpected.value, auditDataPiiDecrypted.parameters.get).returns(Future.successful(auditDataPiiDecrypted.parameters.get))
    (decryptor.decrypt(_: Long, _: String)).expects(accountIdExpected.value, auditDataPiiDecrypted.details.get).returns(Future.successful(auditDataPiiDecrypted.details.get))
    (decryptor.decrypt(_: Long, _: String)).expects(accountIdExpected.value, auditDataPiiDecrypted.response.get).returns(Future.successful(auditDataPiiDecrypted.response.get))

    whenReady(auditDataDecryptor.decrypt(accountId = Some(accountIdActual.value), apiKey = Some(apiKeyString.value), auditDataPii = auditDataPiiDecrypted)) { result =>
      result shouldBe auditDataPiiDecrypted
    }
  }
}
