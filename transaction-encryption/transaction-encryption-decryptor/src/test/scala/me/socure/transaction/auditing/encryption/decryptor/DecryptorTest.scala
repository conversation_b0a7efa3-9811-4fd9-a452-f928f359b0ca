package me.socure.transaction.auditing.encryption.decryptor

import com.amazonaws.auth.{AWSStaticCredentialsProvider, BasicAWSCredentials}
import com.amazonaws.encryptionsdk.AwsCrypto
import com.amazonaws.regions.Regions
import com.amazonaws.services.kms.AWSKMS
import com.amazonaws.services.kms.model.GenerateDataKeyWithoutPlaintextRequest
import com.typesafe.config.ConfigFactory
import me.socure.account.client.encryption.EncryptionKeysClient
import me.socure.common.encryption.SocurePBEConfig
import me.socure.crypto.key.common._
import me.socure.model.encryption.AccountId
import me.socure.transaction.auditing.encryption.encryptor.{Encryptor, EncryptorFactory}
import org.jasypt.encryption.pbe.StandardPBEStringEncryptor
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Milliseconds, Seconds, Span}
import org.scalatest.{FreeSpec, Matchers}

import java.nio.file.Paths
import scala.collection.JavaConverters._
import scala.concurrent.{ExecutionContext, Future}
import scala.io.Source

/**
  * Created by jamesanto on 4/25/17.
  */
class DecryptorTest extends FreeSpec with Matchers with ScalaFutures with MockFactory {
  private implicit val ec = ExecutionContext.Implicits.global
  override implicit val patienceConfig = PatienceConfig(timeout = Span(100000, Seconds), interval = Span(50, Milliseconds))
  private val accountId = AccountId(5)
  private val encryptionContextAccountIdKey = "socure_account_id"
  private val encryptionKeysClient = mock[EncryptionKeysClient]
  private val encryptionContext = Map(
    encryptionContextAccountIdKey -> String.valueOf(accountId.value)
  )
  private val encryptionContextJava = encryptionContext.asJava
  private val sampleResponse = Source.fromInputStream(getClass.getResourceAsStream("/sample-response.json")).mkString

  private val awsAccessKey = decrypt("8amZ4BdHJp9Y7eTpjSo6VIaTsi8rxdWBiZ5+R6ElBubGiHnU3odhttymH12ksHa+")
  private val awsSecretKey = decrypt("YPbN3ycirN0Wt2bwkrf4pC1s4y/vPMbjdZqNoMSR0MphV6qgjac6VRS3Ej1pSl/RnbP4sRm+399huX/aMDRT6w==")

  private val awsCredentialsProvider = new AWSStaticCredentialsProvider(new BasicAWSCredentials(awsAccessKey, awsSecretKey))
  private val kmsIdUsEast1 = "arn:aws:kms:us-east-1:************:alias/client-specific-encryption-stage"
  private val kmsIdUsWest1 = "arn:aws:kms:us-west-1:************:alias/client-specific-encryption-stage"

  val config = ConfigFactory.parseString(
    """client.specific.encryption {
      |  encryption.context.account_id.key = "socure_account_id"
      |  data.key.len = 32 //Please dont change it
      |  kms.ids {
      |    "us-east-1" = "arn:aws:kms:us-east-1:************:alias/client-specific-encryption-stage"
      |    "us-west-1" = "arn:aws:kms:us-west-1:************:alias/client-specific-encryption-stage"
      |  }
      |  service.kms.ids {
      |      etlv3 {
      |          "us-east-1" = "arn:aws:kms:us-east-1:************:alias/etlv3_kms_stage"
      |          "us-west-2" = "arn:aws:kms:us-west-2:************:alias/etlv3_kms_stage"
      |      }
      |  }
      |}""".stripMargin
  )


  private val encryptedKeyUsEast1Bytes = null //genKey(createClient(Regions.US_EAST_1), kmsIdUsEast1)
  private val encryptedKeyUsWest1Bytes = null //genKey(createClient(Regions.US_WEST_1), kmsIdUsWest1)

  private def encryptedKeyUsEast1 = null //EncryptedKey(encryptedKeyUsEast1Bytes.clone())

  private def encryptedKeyUsWest1 = null //EncryptedKey(encryptedKeyUsWest1Bytes.clone())

  private def encryptedKeys = Map(
    Regions.US_EAST_1 -> encryptedKeyUsEast1,
    Regions.US_WEST_1 -> encryptedKeyUsWest1
  )

  private val kmsIdsConfig = Map(
    Regions.US_EAST_1 -> kmsIdUsEast1,
    Regions.US_WEST_1 -> kmsIdUsWest1
  )

  private val kmsIdsConfigs = Set(
    kmsIdsConfig,
    kmsIdsConfig + (Regions.US_EAST_1 -> "failing key"),
    kmsIdsConfig + (Regions.US_WEST_1 -> "failing key")
  )

  val cryptoMaterialsManagerAccountsService = new CryptoMaterialsManagerAccountsService(encryptionKeysClient, kmsIdsConfig )
  val cryptoMaterialsManager = SocureCryptoMaterialsManagerFactory.create(
    config,
    cryptoMaterialsManagerAccountsService,
    awsCredentialsProvider
  )

  "Encryptor and Decryptor" - {
    // ignoring the tests since the test class makes actual aws calls
    // with AWS SSO in place, the tests are not running
    // TODO use mockito to mock aws calls
    "should encrypt and decrypt properly" ignore {
      val encryptor: Encryptor =  EncryptorFactory.create(
        cryptoMaterialsManagerAccountsService,
        cryptoMaterialsManager,
        encryptionContextAccountIdKey
      )

      kmsIdsConfigs.foreach { kmsIdsConfigDecryption =>
        s"when decryption kms ids = $kmsIdsConfigDecryption" in {
          val decryptor: Decryptor = DecryptorFactory.create(
            cryptoMaterialsManagerAccountsService,
            cryptoMaterialsManager,
            encryptionContextAccountIdKey
          )

          (encryptionKeysClient.getKeys _).expects(accountId).returns(Future.successful(Right(encryptedKeys)))
          (encryptionKeysClient.hasKeys _).expects(accountId).returns(Future.successful(Right(true)))
          val resultFuture = for {
            encrypted <- encryptor.encrypt(accountId = accountId.value, sampleResponse)
            decrypted <- decryptor.decrypt(accountId = accountId.value, encrypted)
          } yield decrypted
          whenReady(resultFuture)(_ shouldBe sampleResponse)
        }
      }
    }
  }

  private def decrypt(s: String): String = {
    val password = Option(Source.fromFile(
      Paths.get(
        System.getProperty("user.home"),
        ".socure",
        ".pbe",
        ".password"
      ).toFile
    ).mkString.trim).filter(_.nonEmpty).get
    val socurePBEConfig = new SocurePBEConfig(password)
    val decryptor = new StandardPBEStringEncryptor()
    decryptor.setConfig(socurePBEConfig)
    decryptor.decrypt(s)
  }

  private def genKey(aWSKMS: AWSKMS, keyId: String): Array[Byte] = {
    val generateDataKeyWithoutPlaintextRequest = new GenerateDataKeyWithoutPlaintextRequest()
      .withKeyId(keyId)
      .withNumberOfBytes(AwsCryptoFactory.algorithm.getDataKeyLength)
      .withEncryptionContext(encryptionContextJava)
    val result = aWSKMS.generateDataKeyWithoutPlaintext(generateDataKeyWithoutPlaintextRequest)
    val bytes = new Array[Byte](result.getCiphertextBlob.remaining())
    result.getCiphertextBlob.get(bytes)
    bytes
  }
}
