package me.socure.transaction.auditing.encryption.common

import me.socure.common.environment.{AppName, AppNameResolver}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

/**
  * Created by jamesanto on 4/28/17.
  */
object EncryptionStatusHandler {

  private val logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get("client.specific.encryption.status")
  private val appName = getAppName

  def handle[T](future: Future[T], operation: String)(implicit exe: ExecutionContext): Future[T] = {
    future.onComplete {
      case Success(_) =>
        logger.debug(s"[$appName] $operation successful")
      case Failure(exception) =>
        logger.error(s"[$appName] [CLIENT_SPECIFIC_ENCRYPTION_FAILURE] $operation failed", exception)
        metrics.increment(s"$operation.failure", s"class:${exception.getClass.getSimpleName}", s"app_name:${appName.n}")
    }
    future
  }

  private def getAppName: AppName = {
    Try(AppNameResolver.resolve()).getOrElse(AppName("unknown"))
  }
}
