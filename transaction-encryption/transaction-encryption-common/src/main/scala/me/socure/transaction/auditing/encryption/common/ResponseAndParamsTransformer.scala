package me.socure.transaction.auditing.encryption.common

import org.json4s.jackson.JsonMethods

import java.nio.charset.StandardCharsets
import java.util.Base64
import scala.util.{Failure, Success, Try}

object ResponseAndParamsTransformer {

  def transformForRead(auditDataPii: AuditDataPii): AuditDataPii = {
    val newAuditDataPii = auditDataPii.response match {
      case Some(response) =>
        Try(JsonMethods.parse(response)) match {
          case Success(_) => auditDataPii
          case Failure(_) => auditDataPii.copy(response = auditDataPii.response.map(e => new String(Base64.getDecoder.decode(e), StandardCharsets.UTF_8)))
        }
      case None => auditDataPii
    }
    newAuditDataPii.parameters match {
      case Some(parameters) =>
        Try(JsonMethods.parse(parameters)) match {
          case Success(_) => newAuditDataPii
          case Failure(_) => newAuditDataPii.copy(parameters = newAuditDataPii.parameters.map(e => new String(Base64.getDecoder.decode(e), StandardCharsets.UTF_8)))
        }
      case None => newAuditDataPii
    }
  }

  def transformForWrite(auditDataPii: AuditDataPii): AuditDataPii = {
    auditDataPii.copy(
        response = auditDataPii.response.map(e => Base64.getEncoder.encodeToString(e.getBytes(StandardCharsets.UTF_8))),
        parameters = auditDataPii.parameters.map(e => Base64.getEncoder.encodeToString(e.getBytes(StandardCharsets.UTF_8)))
    )
  }

}