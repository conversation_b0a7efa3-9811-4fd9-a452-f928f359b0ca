package me.socure.transaction.auditing.encryption.common

import org.json4s.jackson.JsonMethods

import java.nio.charset.StandardCharsets
import java.util.Base64
import scala.util.{Failure, Success, Try}

object ResponseTransformer {

  def transformForRead(auditDataPii: AuditDataPii): AuditDataPii = {
    auditDataPii.response match {
      case Some(response) =>
        Try(JsonMethods.parse(response)) match {
          case Success(_) => auditDataPii
          case Failure(_) => auditDataPii.copy(response = auditDataPii.response.map(e => new String(Base64.getDecoder.decode(e), StandardCharsets.UTF_8)))
        }
      case None => auditDataPii
    }
  }

  def transformForWrite(auditDataPii: AuditDataPii): AuditDataPii = {
    auditDataPii.copy(
        response = auditDataPii.response.map(e => Base64.getEncoder.encodeToString(e.getBytes(StandardCharsets.UTF_8)))
    )
  }

}