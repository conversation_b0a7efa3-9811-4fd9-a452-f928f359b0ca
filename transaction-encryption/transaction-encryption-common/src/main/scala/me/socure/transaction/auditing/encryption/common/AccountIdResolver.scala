package me.socure.transaction.auditing.encryption.common

import me.socure.account.client.encryption.EncryptionKeysClient
import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.common.exception.AuthenticationException
import me.socure.model.ErrorResponse
import me.socure.model.encryption.{AccountId, ApiKeyString}
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by jamesanto on 4/26/17.
  */
class AccountIdResolver(encryptionKeysClient: EncryptionKeysClient)(implicit exe: ExecutionContext) {

  private val logger = LoggerFactory.getLogger(getClass)

  def resolve(accountId: Option[AccountId], apiKeyString: Option[ApiKeyString]): Future[Option[AccountId]] = {
    accountId match {
      case Some(id) if id.value > 0 => Future.successful(Some(id))
      case _ => apiKeyString match {
        case Some(apiKey) if isValidApiKey(apiKey) =>
          encryptionKeysClient.getAccountIdByApiKey(apiKey).map {
            case Right(accId) => Some(accId)
            case Left(ErrorResponse(code, _)) if code == ExceptionCodes.AccountNotFound.id => None //Unknown account
            case Left(errorResponse) => throw AuthenticationException(s"Account not found for api key : $apiKeyString due to : $errorResponse")
          }
        case Some(_) =>
          logger.warn("Null/Empty API key found. Not resolving account id.")
          Future.successful(None)
        case None => Future.failed(throw AuthenticationException("Account not found because neither accountId or apiKey provided"))
      }
    }
  }

  private def isValidApiKey(apiKeyString: ApiKeyString): Boolean = {
    Option(apiKeyString).flatMap(a => Option(a.value)).map(_.trim).exists(_.nonEmpty)
  }
}
