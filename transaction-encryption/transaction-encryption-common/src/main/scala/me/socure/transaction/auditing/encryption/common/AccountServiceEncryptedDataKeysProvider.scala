package me.socure.transaction.auditing.encryption.common

import java.util.Base64

import me.socure.account.client.encryption.EncryptionKeysClientV2
import me.socure.common.crypto.generic.EncryptedDataKey
import me.socure.common.crypto.kms.EncryptedData<PERSON>eysProvider
import me.socure.common.environment.AppRegion

import scala.concurrent.{ExecutionContext, Future}

class AccountServiceEncryptedDataKeysProvider(encryptionKeysClient: EncryptionKeysClientV2,
                                              appRegion: AppRegion)
                                             (implicit ec: ExecutionContext)
  extends EncryptedDataKeysProvider {

  private val appRegionStr: String = appRegion.region

  override def provide(accountId: Long): Future[Traversable[EncryptedDataKey]] = {
    encryptionKeysClient.getAllActiveKeys(accountId) map {
      case Left(err) => throw new Exception(s"unable to retrieve EDKs from account-service - ${err.code} - ${err.message}")
      case Right(resp) => {
        val customerKeys = getSortedEDKList(resp.customer_keys)
        val socureKeys = getSortedEDKList(resp.socure_keys)
        customerKeys ++ socureKeys
      }
    }
  }

  private def getSortedEDKList(inputKeys: Map[String, List[String]]): List[EncryptedDataKey] = {
    val edkIter = inputKeys flatMap {
      case (arn, edkList) => edkList map {
        edk => {
          val base64DecodedKey = Base64.getDecoder.decode(edk)
          EncryptedDataKey(arn, base64DecodedKey)
        }
      }
    }
    edkIter.toList.sortWith(sortByRegion)
  }

  private def sortByRegion(key1: EncryptedDataKey, key2: EncryptedDataKey): Boolean = {
    val key1Region: String = key1.keyId.split(":")(3)
    appRegionStr.equals(key1Region)
  }

}
