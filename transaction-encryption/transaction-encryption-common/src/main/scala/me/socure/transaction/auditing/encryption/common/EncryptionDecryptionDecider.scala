package me.socure.transaction.auditing.encryption.common

import me.socure.account.client.encryption.EncryptionKeysClient
import me.socure.account.client.superadmin.AccountInfoClient
import me.socure.model.BusinessUserRoles
import me.socure.model.encryption.{AccountId, ApiKeyString}

import scala.concurrent.{ExecutionContext, Future}

class EncryptionDecryptionDecider(encryptionKeysClient: EncryptionKeysClient,
                                  accountInfoClient: AccountInfoClient
                                 )(implicit ec: ExecutionContext) {

  def canProcess(accountId: AccountId, apiKey: Option[String]): Future[Boolean] = {
    val hasKeysFuture = encryptionKeysClient.hasKeys(accountId) map {
      case Right(result) => result
      case Left(errorResponse) => throw new Exception(s"Unable to get encryption keys for account-id $accountId due to : $errorResponse")
    }
    hasKeysFuture flatMap {
      case true => Future.successful(true)
      case false =>
        apiKey match {
          case Some(apiKeyStr) if apiKeyStr.trim.nonEmpty =>
            accountInfoClient.hasRole(apiKeyString = ApiKeyString(apiKey.get),
                role = BusinessUserRoles.BYOK.id) map {
              case Left(err) =>
                throw new Exception(s"Unable to check whether api-key [${apiKey.get}] has BYOK permission. Error : $err")
              case Right(byokRole) =>
                if (byokRole) {
                  throw new BYOKException(BYOKExceptionReason.NoDataKey.msg)
                } else {
                  false
                }
            }
          case _ => Future.successful(false)
        }
    }
  }

}
