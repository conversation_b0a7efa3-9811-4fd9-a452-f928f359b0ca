package me.socure.transaction.auditing.encryption.common

/**
  * Created by jamesanto on 4/26/17.
  */
final case class AuditDataPii(
                         requestUri: Option[String],
                         parameters: Option[String],
                         details: Option[String],
                         response: Option[String],
                         customerUserId: Option[String]
                       ) {

  import AuditDataPii._

  def sanitize: AuditDataPii = AuditDataPii(
    requestUri = sanitizeStrOpt(requestUri),
    parameters = sanitizeStrOpt(parameters),
    details = sanitizeStrOpt(details),
    response = sanitizeStrOpt(response),
    customerUserId = sanitizeStrOpt(customerUserId)
  )
}

object AuditDataPii {
  def sanitizeStrOpt(strOpt: Option[String]): Option[String] = {
    strOpt.flatMap(str => Option(str).map(_.trim).filter(_.nonEmpty))
  }
}
