package me.socure.transaction.auditing.encryption.common

import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{FreeSpec, Matchers}

/**
  * Created by jam<PERSON>to on 5/3/17.
  */
class ResponseTransformerTest extends FreeSpec with Matchers with ScalaFutures with MockFactory {

  "ResponseTransformer Write" - {
    "should return base64 encoded text" in {
      val auditDataPii = getAuditDataPii("{\"refId\":\"<PERSON>o <PERSON>\"}")
      ResponseTransformer.transformForWrite(auditDataPii).response shouldBe Some("eyJyZWZJZCI6Ik1hcmtvIMSQdXJpxIcifQ==")
    }
  }

  "ResponseTransformer Read" - {
    "should return json when base64 text is passed" in {
      val auditDataPii = getAuditDataPii("eyJyZWZJZCI6Ik1hcmtvIMSQdXJpxIcifQ==")
      ResponseTransformer.transformForRead(auditDataPii).response shouldBe Some("{\"refId\":\"<PERSON><PERSON>\"}")
    }

    "should return json as it is if json text is passed" in {
      val auditDataPii = getAuditDataPii("{\"refId\":\"Marko Đurić\"}")
      ResponseTransformer.transformForRead(auditDataPii).response shouldBe Some("{\"refId\":\"Marko Đurić\"}")
    }
  }

  private def getAuditDataPii(response: String): AuditDataPii = {
    AuditDataPii(
      requestUri = None,
      parameters = None,
      details = None,
      response = Some(response),
      customerUserId = None
    )
  }

}
