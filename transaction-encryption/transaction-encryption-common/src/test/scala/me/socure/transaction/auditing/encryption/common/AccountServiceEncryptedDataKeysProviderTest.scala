package me.socure.transaction.auditing.encryption.common

import java.util.Base64

import me.socure.account.client.encryption.EncryptionKeysClientV2
import me.socure.common.crypto.generic.EncryptedDataKey
import me.socure.common.environment.AppRegion
import me.socure.model.encryption.EncryptedKeyDetails
import org.scalamock.scalatest.MockFactory
import org.scalatest.{FreeSpec, Matchers}
import org.scalatest.concurrent.ScalaFutures

import scala.concurrent.{ExecutionContext, Future}

class AccountServiceEncryptedDataKeysProviderTest extends FreeSpec
  with Matchers with ScalaFutures with MockFactory {

  private implicit val ec = ExecutionContext.Implicits.global
  private val encryptionKeysClient = mock[EncryptionKeysClientV2]

  private val accountId = 1234

  val customerUsEastArn: String = "arn:aws:kms:us-east-1:************:alias/customer-key"
  val customerUsWestArn: String = "arn:aws:kms:us-west-1:************:alias/customer-key"
  val socureUsEastArn: String = "arn:aws:kms:us-east-1:************:alias/socure-key"
  val socureUsWestArn: String = "arn:aws:kms:us-west-1:************:alias/socure-key"

  val customerUsEastEncoded: String = Base64.getEncoder.encodeToString("customer-us-east".getBytes)
  val customerUsWestEncoded: String = Base64.getEncoder.encodeToString("customer-us-west".getBytes)
  val socureUsEastEncoded: String = Base64.getEncoder.encodeToString("socure-us-east".getBytes)
  val socureUsWestEncoded: String = Base64.getEncoder.encodeToString("socure-us-west".getBytes)

  val customerUsEastDecoded: Array[Byte] = Base64.getDecoder.decode(customerUsEastEncoded)
  val customerUsWestDecoded: Array[Byte] = Base64.getDecoder.decode(customerUsWestEncoded)
  val socureUsEastDecoded: Array[Byte] = Base64.getDecoder.decode(socureUsEastEncoded)
  val socureUsWestDecoded: Array[Byte] = Base64.getDecoder.decode(socureUsWestEncoded)

  "AccountServiceEncryptedDataKeysProvider" - {

    "test socure-keys us-east-1 ordered" in {
      val appRegion = "us-east-1"
      val customerKeys = Map.empty[String, List[String]]
      val socureKeys = Map(
        socureUsEastArn -> List(socureUsEastEncoded),
        socureUsWestArn -> List(socureUsWestEncoded)
      )
      val expectedKeyOrder: Traversable[EncryptedDataKey] = List(
        EncryptedDataKey(socureUsEastArn, socureUsEastDecoded),
        EncryptedDataKey(socureUsWestArn, socureUsWestDecoded)
      )
      test(appRegion, customerKeys, socureKeys, expectedKeyOrder)
    }

    "test socure-keys us-west-1 ordered" in {
      val appRegion = "us-west-1"
      val customerKeys = Map.empty[String, List[String]]
      val socureKeys = Map(
        socureUsEastArn -> List(socureUsEastEncoded),
        socureUsWestArn -> List(socureUsWestEncoded)
      )
      val expectedKeyOrder: Traversable[EncryptedDataKey] = List(
        EncryptedDataKey(socureUsWestArn, socureUsWestDecoded),
        EncryptedDataKey(socureUsEastArn, socureUsEastDecoded)
      )
      test(appRegion, customerKeys, socureKeys, expectedKeyOrder)
    }

    "test customer-keys us-east-1 ordered" in {
      val appRegion = "us-east-1"
      val customerKeys = Map(
        customerUsEastArn -> List(customerUsEastEncoded),
        customerUsWestArn -> List(customerUsWestEncoded)
      )
      val socureKeys = Map.empty[String, List[String]]
      val expectedKeyOrder: Traversable[EncryptedDataKey] = List(
        EncryptedDataKey(customerUsEastArn, customerUsEastDecoded),
        EncryptedDataKey(customerUsWestArn, customerUsWestDecoded)
      )
      test(appRegion, customerKeys, socureKeys, expectedKeyOrder)
    }

    "test customer-keys us-west-1 ordered" in {
      val appRegion = "us-west-1"
      val customerKeys = Map(
        customerUsEastArn -> List(customerUsEastEncoded),
        customerUsWestArn -> List(customerUsWestEncoded)
      )
      val socureKeys = Map.empty[String, List[String]]
      val expectedKeyOrder: Traversable[EncryptedDataKey] = List(
        EncryptedDataKey(customerUsWestArn, customerUsWestDecoded),
        EncryptedDataKey(customerUsEastArn, customerUsEastDecoded)
      )
      test(appRegion, customerKeys, socureKeys, expectedKeyOrder)
    }

    "test both-keys us-east-1 ordered" in {
      val appRegion = "us-east-1"
      val customerKeys = Map(
        customerUsEastArn -> List(customerUsEastEncoded),
        customerUsWestArn -> List(customerUsWestEncoded)
      )
      val socureKeys = Map(
        socureUsEastArn -> List(socureUsEastEncoded),
        socureUsWestArn -> List(socureUsWestEncoded)
      )
      val expectedKeyOrder: Traversable[EncryptedDataKey] = List(
        EncryptedDataKey(customerUsEastArn, customerUsEastDecoded),
        EncryptedDataKey(customerUsWestArn, customerUsWestDecoded),
        EncryptedDataKey(socureUsEastArn, socureUsEastDecoded),
        EncryptedDataKey(socureUsWestArn, socureUsWestDecoded)
      )
      test(appRegion, customerKeys, socureKeys, expectedKeyOrder)
    }

    "test both-keys us-west-1 ordered" in {
      val appRegion = "us-west-1"
      val customerKeys = Map(
        customerUsEastArn -> List(customerUsEastEncoded),
        customerUsWestArn -> List(customerUsWestEncoded)
      )
      val socureKeys = Map(
        socureUsEastArn -> List(socureUsEastEncoded),
        socureUsWestArn -> List(socureUsWestEncoded)
      )
      val expectedKeyOrder: Traversable[EncryptedDataKey] = List(
        EncryptedDataKey(customerUsWestArn, customerUsWestDecoded),
        EncryptedDataKey(customerUsEastArn, customerUsEastDecoded),
        EncryptedDataKey(socureUsWestArn, socureUsWestDecoded),
        EncryptedDataKey(socureUsEastArn, socureUsEastDecoded)
      )
      test(appRegion, customerKeys, socureKeys, expectedKeyOrder)
    }

  }

  private def test(appRegion: String,
                   customerKeys: Map[String, List[String]],
                   socureKeys: Map[String, List[String]],
                   expectedKeyOrder: Traversable[EncryptedDataKey]): Unit = {
    val encryptedKeyDetails = EncryptedKeyDetails(customer_keys = customerKeys, socure_keys = socureKeys)
    (encryptionKeysClient.getAllActiveKeys _).expects(accountId).returns(Future.successful(Right(encryptedKeyDetails)))
    val accountServiceEDKProvider = new AccountServiceEncryptedDataKeysProvider(
      encryptionKeysClient = encryptionKeysClient, appRegion = AppRegion(appRegion))
    whenReady(accountServiceEDKProvider.provide(accountId)) (verify(_, expectedKeyOrder) shouldBe true)
  }

  private def verify(orderedKeysFromProvider: Traversable[EncryptedDataKey],
             expectedOrderOfKeys: Traversable[EncryptedDataKey]): Boolean = {
    val orderedKeysFromProviderList = orderedKeysFromProvider.toList
    val expectedOrderOfKeysList = expectedOrderOfKeys.toList
    if (orderedKeysFromProviderList.size != expectedOrderOfKeysList.size) {
      return false
    }
    var idx = 0
    while (idx < orderedKeysFromProviderList.size) {
      val providerEdk = orderedKeysFromProviderList.apply(idx)
      val expectedEdk = expectedOrderOfKeysList.apply(idx)
      if (!providerEdk.keyId.equals(expectedEdk.keyId)) {
        return false
      }
      if (!new String(providerEdk.key).equals(new String(expectedEdk.key))) {
        return false
      }
      idx += 1
    }
    true
  }

}
