package me.socure.transaction.auditing.encryption.common

import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{FreeSpec, Matchers}

/**
  * Created by jam<PERSON>to on 5/3/17.
  */
class ResponseAndParamsTransformerTest extends FreeSpec with Matchers with ScalaFutures with MockFactory {

  "ResponseTransformer Write" - {
    "should return base64 encoded text" in {
      val auditDataPii = getAuditDataPii("{\"refId\":\"<PERSON>o <PERSON>\"}", "{\"surName\":\"Đurić\"}")
      val output = ResponseAndParamsTransformer.transformForWrite(auditDataPii)
      output.response shouldBe Some("eyJyZWZJZCI6Ik1hcmtvIMSQdXJpxIcifQ==")
      output.parameters shouldBe Some("eyJzdXJOYW1lIjoixJB1cmnEhyJ9")
      output.requestUri shouldBe Some("requestUri")
      output.details shouldBe Some("details")
      output.customerUserId shouldBe Some("customerUserId")
    }
  }

  "ResponseTransformer Read" - {
    "should return json when base64 text is passed" in {
      val auditDataPii = getAuditDataPii("eyJyZWZJZCI6Ik1hcmtvIMSQdXJpxIcifQ==", "eyJzdXJOYW1lIjoixJB1cmnEhyJ9")
      val output = ResponseAndParamsTransformer.transformForRead(auditDataPii)
      output.response shouldBe Some("{\"refId\":\"Marko Đurić\"}")
      output.parameters shouldBe Some("{\"surName\":\"Đurić\"}")
      output.requestUri shouldBe Some("requestUri")
      output.details shouldBe Some("details")
      output.customerUserId shouldBe Some("customerUserId")
    }

    "should return json as it is if json text is passed" in {
      val auditDataPii = getAuditDataPii("{\"refId\":\"Marko Đurić\"}", "{\"surName\":\"Đurić\"}")
      val output = ResponseAndParamsTransformer.transformForRead(auditDataPii)
      output.response shouldBe Some("{\"refId\":\"Marko Đurić\"}")
      output.parameters shouldBe Some("{\"surName\":\"Đurić\"}")
      output.requestUri shouldBe Some("requestUri")
      output.details shouldBe Some("details")
      output.customerUserId shouldBe Some("customerUserId")
    }
  }

  private def getAuditDataPii(response: String, parameters: String): AuditDataPii = {
    AuditDataPii(
      requestUri = Some("requestUri"),
      parameters = Some(parameters),
      details = Some("details"),
      response = Some(response),
      customerUserId = Some("customerUserId")
    )
  }

}
