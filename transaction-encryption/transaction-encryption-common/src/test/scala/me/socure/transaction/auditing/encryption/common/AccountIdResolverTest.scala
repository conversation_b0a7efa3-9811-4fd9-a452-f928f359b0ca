package me.socure.transaction.auditing.encryption.common

import me.socure.account.client.encryption.EncryptionKeysClient
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.exception.AuthenticationException
import me.socure.model.encryption.{AccountId, ApiKeyString}
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{FreeSpec, Matchers}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by jam<PERSON>to on 5/3/17.
  */
class AccountIdResolverTest extends FreeSpec with Matchers with ScalaFutures with MockFactory {
  private implicit val ec = ExecutionContext.Implicits.global

  private val encryptionKeysClient = mock[EncryptionKeysClient]
  private val accountIdResolver = new AccountIdResolver(encryptionKeysClient = encryptionKeysClient)
  private val validAccountId = AccountId(5)
  private val unknownAccountId = AccountId(0)
  private val apiKeyString = ApiKeyString("api_key_1")

  "AccountIdResolver" - {
    "when account id is valid it should just return it" in {
      whenReady(accountIdResolver.resolve(accountId = Some(validAccountId), apiKeyString = Some(apiKeyString)))(_ shouldBe Some(validAccountId))
    }

    "when account id is not valid and api key is valid it should get account id from account service" in {
      (encryptionKeysClient.getAccountIdByApiKey _).expects(apiKeyString).returns(Future.successful(Right(validAccountId)))
      whenReady(accountIdResolver.resolve(accountId = Some(unknownAccountId), apiKeyString = Some(apiKeyString)))(_ shouldBe Some(validAccountId))
    }

    "when account id is not valid and account service says that the account is not found it should return 0=UnknownAccount" in {
      (encryptionKeysClient.getAccountIdByApiKey _).expects(apiKeyString).returns(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))))
      whenReady(accountIdResolver.resolve(accountId = Some(unknownAccountId), apiKeyString = Some(apiKeyString)))(_ shouldBe None)
    }

    "when account id is not valid and account service returns an error it should propogate the errors" in {
      (encryptionKeysClient.getAccountIdByApiKey _).expects(apiKeyString).returns(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
      whenReady(accountIdResolver.resolve(accountId = Some(unknownAccountId), apiKeyString = Some(apiKeyString)).failed)(_ shouldBe an[AuthenticationException])
    }

    "when account id is not valid and api key string is null/empty it should return none" in {
      whenReady(accountIdResolver.resolve(accountId = Some(unknownAccountId), apiKeyString = Some(null.asInstanceOf[ApiKeyString])))(_ shouldBe None)
      whenReady(accountIdResolver.resolve(accountId = Some(unknownAccountId), apiKeyString = Some(ApiKeyString(null))))(_ shouldBe None)
      whenReady(accountIdResolver.resolve(accountId = Some(unknownAccountId), apiKeyString = Some(ApiKeyString(""))))(_ shouldBe None)
      whenReady(accountIdResolver.resolve(accountId = Some(unknownAccountId), apiKeyString = Some(ApiKeyString("   "))))(_ shouldBe None)
    }
  }
}
