<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>transaction-auditing</artifactId>
        <groupId>me.socure</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>transaction-encryption-encryptor</module>
        <module>transaction-encryption-decryptor</module>
        <module>transaction-encryption-common</module>
    </modules>


    <artifactId>transaction-encryption</artifactId>
    <packaging>pom</packaging>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>me.socure</groupId>
                <artifactId>account-service-client</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>me.socure</groupId>
                <artifactId>common-kms</artifactId>
                <version>${socure.common.version}</version>
            </dependency>
            <dependency>
                <groupId>me.socure</groupId>
                <artifactId>common-storage-scalacache</artifactId>
                <version>${socure.common.version}</version>
            </dependency>
            <dependency>
                <groupId>me.socure</groupId>
                <artifactId>common-storage-guava</artifactId>
                <version>${socure.common.version}</version>
            </dependency>
            <dependency>
                <groupId>me.socure</groupId>
                <artifactId>common-storage-core</artifactId>
                <version>${socure.common.version}</version>
            </dependency>
            <dependency>
                <groupId>me.socure</groupId>
                <artifactId>common-crypto-key-common</artifactId>
                <version>${socure.common.version}</version>
            </dependency>
            <dependency>
                <groupId>me.socure</groupId>
                <artifactId>common-crypto-encryptor-key</artifactId>
                <version>${socure.common.version}</version>
            </dependency>
            <dependency>
                <groupId>me.socure</groupId>
                <artifactId>common-crypto-decryptor-key</artifactId>
                <version>${socure.common.version}</version>
            </dependency>
            <dependency>
                <groupId>me.socure</groupId>
                <artifactId>common-crypto-kms</artifactId>
                <version>${socure.common.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_2.11</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.scalamock</groupId>
            <artifactId>scalamock-scalatest-support_2.11</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
