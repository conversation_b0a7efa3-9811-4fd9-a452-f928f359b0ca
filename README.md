Transaction Audit Service
---------------

The Transaction Audit Service is a micro-service responsible for storing and making accessible transaction data to other microservices in our production environment.

#transaction-auditing-queueproc
The transaction-auditing-queueproc module is responsible for consuming api responses from the queue

#transaction-auditing-rest
The transaction-auditing-rest module is responsible to serve all the enqueue/fetch/search transaction requests from dependent micro-services.

#transaction-auditing-client
The transaction-auditing-client module is responsible for enqueuing the message.

#transaction-auditing-storage
The transaction-auditing-storage module is responsible for encryption/decryption, saving into database.

#transaction-auditing-service
The transaction-auditing-service module is responsible for running the micro-service

# Specifications
More documentation is available here: https://docs.google.com/document/d/1paTQ9Eqg_wOEl4HYc9IwzEPuSu1UwANkV99kjxHkS8o