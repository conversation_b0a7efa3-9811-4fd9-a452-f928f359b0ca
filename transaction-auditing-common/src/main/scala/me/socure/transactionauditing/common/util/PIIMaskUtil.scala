package me.socure.transactionauditing.common.util

import me.socure.common.config.MsConfigProvider
import org.json4s._
import org.json4s.jackson.Json
import org.json4s.jackson.JsonMethods.parse

import scala.collection.JavaConversions._
import scala.util.Try


/**
 * Created by Vignesh V on Sep 14, 2022
 */
object PIIMaskUtil {

  private implicit val formats: DefaultFormats.type = DefaultFormats

  private val fieldsToMask: List[String] = getFieldsToMask

  private def getFieldsToMask: List[String] = {
    Try {
      val config = MsConfigProvider.provide().value
      config.getStringList("mask.response.fields").toList
    }.getOrElse(List("kycPlus.bestMatchedEntity.dob", "kycPlus.bestMatchedEntity.ssn"))
  }

  def maskPiiInResponse(response: String): String = {

    val fields = fieldsToMask.map(_.split("\\."))

    val jsonOpt = Try {
      parse(response)
    }.toOption
    jsonOpt.map { json =>
      val maskedResponse = fields.foldLeft(json) {
        case (updatedJson, path) => updatedJson.replace(path.toList, JString("******"))
      }
      Json(formats).write(maskedResponse)
    }.getOrElse(response)
  }
}
