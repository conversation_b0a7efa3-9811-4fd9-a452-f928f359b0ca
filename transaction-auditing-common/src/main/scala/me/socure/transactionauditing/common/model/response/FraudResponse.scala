package me.socure.transactionauditing.common.model.response

import me.socure.transactionauditing.common.model.ReasonCode

case class FraudResponse(reasonCodes: Set[ReasonCode], scores: List[FraudModelScore])

case class FraudModelScore(
                               name: String,
                               version: String,
                               score: Double
                             )