package me.socure.transactionauditing.common.transaction

import me.socure.types.scala.{ByM<PERSON>ber, Enum}

/**
  * Enum that represents the columns that we can choose to receive when querying the transaction auditing micro-service
  * for a transaction. Account ID and API key are always returned by default, since they are needed for decryption
  */
object TransactionColumns extends Enum with ByMember {
  type TransactionColumn = EnumVal

  case class EnumVal(name: String) extends Value

  val API = new TransactionColumn("api")
  val ID = new TransactionColumn("id")
  val API_NAME = new TransactionColumn("apiName")
  val ACCOUNT_ID = new TransactionColumn("accountId")
  val API_KEY = new TransactionColumn("apiKey")
  val ACCOUNT_IP_ADDRESS = new TransactionColumn("accountIpAddress")
  val TRANSACTION_DATE = new TransactionColumn("transactionDate")
  val AUTH_SCORE = new TransactionColumn("authScore")
  val API_REQUEST = new TransactionColumn("apiRequest")
  val API_RESPONSE = new TransactionColumn("apiResponse")
  val PROCESSING_TIME = new TransactionColumn("processingTime")
  val CUSTOMER_USER_ID = new TransactionColumn("customerUserId")
  val KYC = new TransactionColumn("kyc")
  val OFAC = new TransactionColumn("ofac")
  val REASON_CODES = new TransactionColumn("reasonCodes")
  val HAS_RISK_CODE = new TransactionColumn("hasRiskCode")
  val ERROR = new TransactionColumn("error")
  val ERROR_MSG = new TransactionColumn("errorMsg")
  val CACHES_USED = new TransactionColumn("cachesUsed")
  val PARAMETERS = new TransactionColumn("parameters")
  val RUN_ID = new TransactionColumn("runId")
  val CONFIDENCE = new TransactionColumn("confidence")
  val ENVIRONMENT_ID = new TransactionColumn("environmentId")
  val DETAILS = new TransactionColumn("details")
  val DEBUG = new TransactionColumn("debug")
  val UUID = new TransactionColumn("uuid")

  def byColumn(column: String): Option[TransactionColumn] = {
    values.find(_.name == column)
  }
}