package me.socure.transactionauditing.common.util

import java.util

import com.amazonaws.services.sqs.model.MessageAttributeValue
import me.socure.transactionauditing.common.util.CustomMessageAttributes.{SERVICE_ID, TRANSACTION_ID}
import org.slf4j.LoggerFactory

object SqsCustomAttributesLogger {
  private val logger = LoggerFactory.getLogger(getClass)

  def log(messageAttributes: util.Map[String, MessageAttributeValue]): Unit = {
    getTransactionAttribute(messageAttributes).foreach(id => logger.debug("Received SQS message with transactionId attribute value: " + id))
    getServiceIdAttribute(messageAttributes).foreach(id => logger.debug("Received SQS message with third party serviceId attribute value: " + id))
  }

  def getTransactionAttribute(attributes: util.Map[String, MessageAttributeValue]) = Option(attributes.get(TRANSACTION_ID.name)).map(_.getStringValue)
  def getServiceIdAttribute(attributes: util.Map[String, MessageAttributeValue]) = Option(attributes.get(SERVICE_ID.name)).map(_.getStringValue)
}
