package me.socure.transactionauditing.common.model.response.v3_0

import me.socure.transactionauditing.common.model.debug.Debug
import me.socure.transactionauditing.common.model.response._

case class TransactionResponseV3_0(referenceId: String,
                                   fraud: Option[FraudResponse],
                                   authenticity: Option[AuthResponse],
                                   blacklist: Option[BlacklistResponseV3_0],
                                   nameEmailCorrelation: Option[CorrelationResponse],
                                   nameAddressCorrelation: Option[CorrelationResponse],
                                   namePhoneCorrelation: Option[CorrelationResponse],
                                   emailRisk: Option[PiiRiskResponse],
                                   phoneRisk: Option[PiiRiskResponse],
                                   addressRisk: Option[PiiRiskResponse],
                                   watchlist: Option[WatchlistResponseV3_0],
                                   kyc: Option[KycResponse3_0],
                                   documentVerification: Option[DocumentVerification],
                                   decision: Option[DecisionResponse],
                                   social: Option[SocialResponse],
                                   debug: Option[Debug],
                                   alertList: Option[AlertListResponse],
                                   kycPlus: Option[KycPlusResponse3_0]
                   ) extends TransactionResponse
