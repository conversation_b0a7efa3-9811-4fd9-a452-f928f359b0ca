package me.socure.transactionauditing.common.domain

case class ApiTransactionCount(
                                accountId: Option[Long],
                                apiType: Short,
                                errorCount: Int,
                                kyc: <PERSON><PERSON>an,
                                watchlist1: <PERSON><PERSON><PERSON>,
                                watchlist2: <PERSON><PERSON><PERSON>,
                                watchlist3: <PERSON><PERSON><PERSON>,
                                totalCount: Int
                              ) {

}
