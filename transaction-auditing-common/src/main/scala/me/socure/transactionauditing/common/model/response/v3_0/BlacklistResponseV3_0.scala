package me.socure.transactionauditing.common.model.response.v3_0

import me.socure.transactionauditing.common.model.ReasonCode

case class BlacklistResponseV3_0(reasonCodes: Set[ReasonCode], matches: List[BlacklistMatchV3_0])

case class BlacklistMatchV3_0(
                                   reason: String,
                                   reportedDate: String,
                                   industry: String
                                   )
