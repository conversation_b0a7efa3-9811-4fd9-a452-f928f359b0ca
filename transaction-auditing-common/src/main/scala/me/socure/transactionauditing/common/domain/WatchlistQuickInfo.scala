package me.socure.transactionauditing.common.domain

import org.joda.time.DateTime

case class WatchlistQuickInfo(
                                 id: Long,
                                 accountId: Long,
                                 transactionId: String,
                                 environmentId: Long,
                                 tier: String,
                                 watchlistSettings: Option[String],
                                 transactionDate: DateTime
                               )
