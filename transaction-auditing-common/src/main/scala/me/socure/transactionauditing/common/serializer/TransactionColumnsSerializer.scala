package me.socure.transactionauditing.common.serializer

import me.socure.transactionauditing.common.transaction.TransactionColumns
import me.socure.transactionauditing.common.transaction.TransactionColumns.TransactionColumn
import org.joda.time.DateTime
import org.json4s.JsonAST.{JNull, J<PERSON><PERSON>, JValue}
import org.json4s.{CustomSerializer, Formats, JValue, JsonDSL, MappingException, Serializer, TypeInfo}


class TransactionColumnsSerializer
  extends CustomSerializer[TransactionColumn](format => (
    {
      case JNull ⇒ null
      case jstr:JString ⇒ TransactionColumns.byColumn(jstr.s) match {
        case Some(column) => column
        case None => throw new Exception("Invalid column name")
      }
    },
    {
      case column: TransactionColumn=> {
        JString(column.name)
      }
    }
  ))