package me.socure.transactionauditing.common.config

import com.typesafe.config.Config
import me.socure.transactionauditing.common.util.SqsQueueEnums.SqsQueueEnum

import scala.util.Try

class SqsBaseClientConfiguration(config: Config, queueEnum: SqsQueueEnum) {
  private val PRIMARY = "primary"
  private val SECONDARY = "fallback0"
  private val TERTIARY = "fallback1"

  val endpoints = Seq(
    loadSqsEndpoint(PRIMARY),
    loadSqsEndpoint(SECONDARY),
    loadSqsEndpoint(TERTIARY)
  )
  val minBackoff: Int = config.getInt("transaction-auditing.aws.sqs.backoff.min")
  val maxBackoff: Int = config.getInt("transaction-auditing.aws.sqs.backoff.max")
  val maxRetries: Int = config.getInt("transaction-auditing.aws.maxRetries")

  private def readQueueConfig(queueType: SqsQueueEnum, endpointName: String): ClientSqsQueueConfig = {
    val queueName = queueType.name
    val transactionQueueName = config.getString("transaction-auditing.aws.%s.sqs.%s.queueName".format(endpointName, queueName))
    val transactionWaitTimeSeconds = config.getInt("transaction-auditing.aws.%s.sqs.%s.waitTimeSeconds".format(endpointName, queueName))

    val transactionMaxBatchSize = config.getInt("transaction-auditing.aws.%s.sqs.%s.maxBatchSize".format(endpointName, queueName))
    val transactionMaxBufferSize = config.getInt("transaction-auditing.aws.%s.sqs.%s.maxBufferSize".format(endpointName, queueName))
    val transactionMaxInFlight = config.getInt("transaction-auditing.aws.%s.sqs.producer.%s.maxInFlight".format(endpointName, queueName))
    val queueOwnerAWSAccountId: Option[String] = Try {
      config.getString("transaction-auditing.aws.%s.sqs.%s.queueOwnerAWSAccountId".format(endpointName, queueName))
    }.toOption
    ClientSqsQueueConfig(queueName = transactionQueueName,
      waitTimeSeconds = transactionWaitTimeSeconds,
      maxBatchSize = transactionMaxBatchSize,
      maxBufferSize = transactionMaxBufferSize,
      maxInFlight = transactionMaxInFlight,
      queueOwnerAWSAccountId = queueOwnerAWSAccountId
    )
  }

  private def loadSqsEndpoint(endpointName: String): ClientSqsEndpointConfig = {
    val transactionQueue = readQueueConfig(queueEnum, endpointName)
    ClientSqsEndpointConfig(
      config.getString("transaction-auditing.aws.%s.sqs.region".format(endpointName)),
      transactionQueue
    )
  }
}
