package me.socure.transactionauditing.common.domain.fixture

import java.util.Date

import me.socure.common.clock.FakeClock
import me.socure.common.random.Random
import me.socure.transactionauditing.common.domain.{ApiTransaction, BlackListInfo, RequestProps, ScoringProps}
import org.joda.time.DateTime

object APITransactionValueGenerator {
  private val clock = new FakeClock(new Date().getTime)
  val fs = Random.nextDouble()

  def aAPITransaction(
                       id: Long = Random.nextLong(),
                       transactionId: String = Random.alphaNumeric(36),
                       transactionDate: Option[DateTime] = Some(clock.now()),
                       processingTime: Option[Long] = Some(Random.nextLong()),
                       api: Option[String] = Some("REST"),
                       fraudScore: Option[Double] = Some(fs),
                       productName: Option[String] = Some("EmailAuthScore"),
                       parameters: Option[String] = Option("{}"),
                       response: Option[String] = Some(s"""{"fraudscore" : $fs}"""),
                       error: Option[Boolean] = Some(false),
                       errorMsg: Option[String] = None,
                       accountIpAddress: Option[String] = Some("localhost"),
                       originOfInvocation: Option[String] = None,
                       geocode: Option[String] = None,
                       apiKey: Option[String] = Some("test-api-key"),
                       accountId: Option[Long] = Some(Random.nextLong()),
                       apiName: Option[String] = Some("/api/3.0/EmailAuthScore"),
                       UUID: Option[String] = Some(Random.alphaNumeric(39)),
                       cachesUsed: Option[String] = None,
                       debug: Option[String] = Some("""{"http_status" : 200}"""),
                       details: Option[String] = Some(Random.alphabetic(500)),
                       authScore: Option[Double] = Some(0),
                       confidence: Option[Double] = Some(0),
                       reasonCodes: Option[String] = Some(Random.alphaNumeric(250)),
                       hasRiskCode: Option[Boolean] = Some(false),
                       customerUserId: Option[String] = None,
                       requestURI: Option[String] = Some("/api/3.0/EmailAuthScore"),
                       kyc: Option[Boolean] = Some(false),
                       ofac: Option[Boolean] = Some(false),
                       runId: Option[String] = Some(Random.alphaNumeric(10)),
                       status: Option[String] = Some("Successful"),
                       environmentId: Option[Long] = Some(2),
                       blacklistInfo: Option[BlackListInfo] = None,
                       isBlacklist: Option[Boolean] = None,
                       internalWorkLogs: Option[String] = None
                     ): ApiTransaction = {

    ApiTransaction(
      id = id,
      accountId = accountId,
      transactionId = transactionId,
      api = api,
      apiResponse = response,
      fraudScore = fraudScore,
      productName = productName,
      apiName = apiName,
      transactionDate = transactionDate,
      processingTime = processingTime,
      error = error,
      errorMsg = errorMsg,
      apiKey = apiKey,
      environmentId = environmentId,
      cachesUsed = cachesUsed,
      status = status,
      requestProps = RequestProps(
        originOfInvocation = originOfInvocation,
        accountIpAddress = accountIpAddress,
        parameters = parameters,
        runId = runId,
        requestURI = requestURI,
        kyc = kyc,
        ofac = ofac,
        geocode = geocode,
        uuid = UUID,
        customerUserId = customerUserId
      ),
      scoringProps = ScoringProps(
        authScore = authScore,
        confidence = confidence,
        debug = debug,
        details = details,
        reasonCodes = reasonCodes,
        hasRiskCode = hasRiskCode
      ),
      blacklistInfo = blacklistInfo,
      isBlacklist = isBlacklist,
      internalWorkLogs = internalWorkLogs
    )
  }
}
