package me.socure.transactionauditing.common.util

import me.socure.types.scala.{ByM<PERSON>ber, Enum}

object SqsQueueEnums extends Enum with ByMember {
  type SqsQueueEnum = EnumVal

  case class EnumVal(name: String) extends Value

  val TRANSACTION = new SqsQueueEnum("transaction")
  val THIRD_PARTY = new SqsQueueEnum("third-party")
  val DEVICE_FINGERPRINT = new SqsQueueEnum("device-fingerprint")
  val DEVICE_FINGERPRINT_ETL = new SqsQueueEnum("device-fingerprint-etl")
}
