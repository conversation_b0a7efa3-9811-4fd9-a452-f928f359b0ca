package me.socure.transactionauditing.common.domain

case class ScoringProps(
                         authScore: Option[Double],
                         confidence: Option[Double],
                         debug: Option[String],
                         details: Option[String],
                         reasonCodes: Option[String],
                         hasRiskCode: Option[Boolean]
                       )