package me.socure.transactionauditing.common.factory

import com.typesafe.config.Config
import me.socure.account.client.encryption.{CachedEncryptionKeysClient, EncryptionKeysClientFactory}
import me.socure.transaction.auditing.encryption.common.AccountIdResolver
import net.spy.memcached.MemcachedClient

import java.net.InetSocketAddress
import scala.concurrent.ExecutionContext

object AccountIdResolverFactory {
  def create(config: Config)(
    implicit ec: ExecutionContext): AccountIdResolver = {

    val accountEndpoint = config.getConfig("account.service").getString("endpoint")
    val memcachedClient = new MemcachedClient(new InetSocketAddress(config.getConfig("memcached").getString("host"), config.getConfig("memcached").getInt("port")))

    val encryptionKeysClient: CachedEncryptionKeysClient = EncryptionKeysClientFactory.createCached(
      accountServiceEndpoint = accountEndpoint,
      memcachedClient = memcachedClient,
      ttl = None
    )
    new AccountIdResolver(encryptionKeysClient)
  }
}
