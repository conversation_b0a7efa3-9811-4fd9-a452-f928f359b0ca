package me.socure.transactionauditing.common.model.debug.codecs

import argonaut.Argonaut._
import argonaut._
import me.socure.transactionauditing.common.model.parameters.PII

/**
  * Created by jamesanto on 1/9/17.
  */
object PIICodecs {
  implicit val decoderPII: DecodeJson[PII] = jdecode20L(PII.apply)(
    "country",
    "ipaddress",
    "firstname",
    "mobilenumber",
    "city",
    "userid",
    "nationalid",
    "physicaladdress2",
    "surname",
    "state",
    "customeruserid",
    "email",
    "zip",
    "physicaladdress",
    "driverlicensestate",
    "companyname",
    "dob",
    "geocode",
    "fullname",
    "driverlicense"
  )
}
