package me.socure.transactionauditing.common.model

import argonaut.DecodeJson
import me.socure.transactionauditing.common.model.response.FraudModelScore

object CommonCodecs {
  implicit val reasonCodeDecoder: Decode<PERSON>son[ReasonCode] = DecodeJson { h =>
    for {
      reasonCode <- h.as[String]
    } yield {
      ReasonCode(
        reasonCode
      )
    }
  }

  implicit val fraudModelScoreDecoder: Decode<PERSON>son[FraudModelScore] = DecodeJson { h =>
    for {
      name <- (h --\ "name").as[String]
      version <- (h --\ "version").as[String]
      score <- (h --\ "score").as[Double]
    } yield FraudModelScore(
      name = name,
      version = version,
      score = score
    )
  }
}
