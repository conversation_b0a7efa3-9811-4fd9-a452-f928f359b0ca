package me.socure.transactionauditing.common.json


import org.joda.time.{DateTime, DateTimeZone}
import org.joda.time.format.DateTimeFormatter
import org.json4s.CustomSerializer
import org.json4s.JsonAST.{JNull, JString}
import org.json4s.ext.DateParser

import scala.util.Try

class DateTimeSerializerWithDefaultFallback(dateTimeFormat: DateTimeFormatter) extends CustomSerializer[DateTime](format => ( {

  case JNull ⇒ null
  case JString(s) ⇒ Try(dateTimeFormat.parseDateTime(s)).getOrElse({
    val zonedInstant = DateParser.parse(s, format)
    new DateTime(zonedInstant.instant, DateTimeZone.forTimeZone(zonedInstant.timezone))
  })
  }, {

  case d: DateTime ⇒ JString(d.toString(dateTimeFormat))

  }
))
