package me.socure.transactionauditing.common.util

import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.transaction.id.TrxId
import me.socure.transactionauditing.common.domain.v2.TransactionSearchResponseV2
import me.socure.transactionauditing.common.domain.{ApiTransaction, RequestProps, ScoringProps}
import me.socure.transactionauditing.common.transaction.TransactionColumns
import me.socure.transactionauditing.common.transaction.TransactionColumns.TransactionColumn
import me.socure.transactionauditstorage.mysqlschema.{ApiTransaction => SlickApiTransaction, RequestProps => SlickRequestProps, ScoringProps => SlickScoringProps}
import org.joda.time.DateTimeZone
import org.json4s._
import org.json4s.jackson.JsonMethods
import org.json4s.jackson.JsonMethods._

import scala.util.{Failure, Success, Try}

/**
 * Created by joonkang on 6/9/16.
 */
object SlickDomainConversion {
  implicit val defaulformat = DefaultFormats
  private val pattern = """/([a-zA-Z0-9_]+)/([a-zA-Z0-9_.]+)/([a-zA-Z]+)""".r
  private val watchlistProducts = Set("1", "2", "3")
  val obfuscateFields = Seq("dob", "nationalid", "driverlicense", "driverlicensestate", "ipaddress", "accountnumber", "routingnumber", "ein")
  private val httpStatus =  Map(200 -> "Successful", 400 -> "Parameter Error", 500 -> "Internal Error")
  private val logger = TransactionAwareLoggerFactory.getLogger(getClass)

  def toUtc(tx: SlickApiTransaction): SlickApiTransaction = {
    tx.copy(
      transactionDate = tx.transactionDate.map(_.withZoneRetainFields(DateTimeZone.UTC))
    )
  }

  def removeAccountIdAndApiKeyIfNecessary(tx: SlickApiTransaction, columns: Set[TransactionColumn]): SlickApiTransaction = {
    tx.copy(
      accountId = if(columns.contains(TransactionColumns.ACCOUNT_ID)) tx.accountId else None,
      apiKey = if(columns.contains(TransactionColumns.API_KEY)) tx.apiKey else None
    )
  }

  private def resolveProduct(tx:SlickApiTransaction ) = {
    val kyc = tx.kyc
    val watchlist = tx.parameters.flatMap(p ⇒ (parse(p) \ "watchlist").extractOpt[String]).map(_.trim).exists(watchlistProducts.contains)

    val product = tx.apiName.map(name => {
      val pattern(a, b, c) = name
      c
    })

    val specialProduct =  (kyc,watchlist) match {
      case (Some(true), false) ⇒ Some("KYC")
      case (Some(false), true) ⇒ Some("Watchlist")
      case (Some(true), true) ⇒ Some("Watchlist,KYC")
      case _ ⇒ None
    }
    specialProduct.flatMap(sp ⇒ product.map(p => s"$p ($sp)")).orElse(product)
  }

  def obfuscatePII(request : String) : String = {
    obfuscateFields.map(d => s"$d=.*?&" -> s"$d=******&").foldLeft(request + "&"){case (z, (s,r)) => z.replaceAll(s, r)}.dropRight(1)
  }

  def obfuscateParameters(params : String)(implicit trxId: TrxId) : String = {
    Try(JsonMethods.parse(params)) match {
      case Success(parsedParams) =>
        val maskedJson = parsedParams transformField  {
          case (k, v) if obfuscateFields.contains(k.toLowerCase) && v != JNull => k -> JString("******")
          case (k, v) => k -> v
        }
        JsonMethods.compact(maskedJson)
      case Failure(_) => "Invalid parameter structure"
    }
  }

  def getStatusFromDebug(debugNode: Option[String]): Option[String] = {
    val resp = debugNode.getOrElse("""{}""")
    val debug = Try{parse(resp)}.toOption
    debug.flatMap(p ⇒ (p \\ "http_status").extractOpt[Int]) match {
      case Some(a) if 200 to 299 contains(a) => httpStatus.get(200)
      case Some(b) if 400 to 499 contains(b) => httpStatus.get(400)
      case Some(c) if 500 to 599 contains(c) => httpStatus.get(500)
      case _ => None
    }
  }

  def maskPii(tx: SlickApiTransaction): SlickApiTransaction = {
    implicit val trxId = TrxId(tx.transactionId)
    tx.copy(
      requestProps = tx.requestProps.copy(
        requestUri = tx.requestUri.map(obfuscatePII),
        parameters = tx.parameters.map(obfuscateParameters)
      ),
      response = tx.response.map(PIIMaskUtil.maskPiiInResponse)
    )
  }

  def maskPiiV2(tsr: TransactionSearchResponseV2): TransactionSearchResponseV2 = {
    val records = tsr.records
    val transactions = records.transactions
    val maskedTransactions = transactions.map( transaction =>{
      implicit val trxId = TrxId(transaction.transactionId)
      transaction.copy(
        requestProps = transaction.requestProps.copy(
          requestUri = transaction.requestUri.map(obfuscatePII),
          parameters = transaction.parameters.map(obfuscateParameters)
        ),
        response = transaction.response.map(PIIMaskUtil.maskPiiInResponse)
      )
    })
    tsr.copy(records = records.copy(transactions = maskedTransactions))
  }

  def toApiTransaction(tx:SlickApiTransaction): ApiTransaction = {
    toApiTransaction(tx,false);
  }
  def toApiTransaction(tx:SlickApiTransaction , skipProduct:Boolean = false): ApiTransaction = {
    implicit val trxId = TrxId(tx.transactionId)
    val resp = tx.response.getOrElse("""{}""")
    val json = Try{parse(resp)}.toOption
    val fs = json.flatMap(p ⇒  (p \\ "fraudscore").extractOpt[Double])
    val apiRequest = tx.requestUri
    val parameters = tx.parameters

    var product : Option[String] = Some("");
    if(!skipProduct)
      product = resolveProduct(tx)

    ApiTransaction(
      id=tx.id.getOrElse(0),
      accountId = tx.accountId,
      transactionId = tx.transactionId,
      api = tx.api,
      apiResponse = tx.response,
      fraudScore = fs,
      productName = product,
      apiName=tx.apiName,
      transactionDate = tx.transactionDate,
      processingTime = tx.processingTime,
      error = tx.error,
      errorMsg = tx.errorMsg,
      apiKey=tx.apiKey,
      environmentId = tx.environmentType,
      cachesUsed = tx.cachesUsed,
      status = getStatusFromDebug(tx.debug),
      requestProps = RequestProps(
        originOfInvocation = tx.requestProps.originOfInvocation,
        accountIpAddress = tx.accountIpAddress,
        parameters = parameters,
        runId = tx.runId,
        requestURI = apiRequest,
        kyc = tx.kyc,
        ofac = tx.ofac,
        geocode = tx.geocode,
        uuid = tx.uuid,
        customerUserId = tx.customerUserId
      ),
      scoringProps = ScoringProps(
        authScore = tx.authScore,
        confidence = tx.confidence,
        debug =  tx.debug,
        details = tx.details,
        reasonCodes = tx.reasonCodes,
        hasRiskCode = tx.hasRiskCode
      ),
      blacklistInfo = None,
      isBlacklist = None,
      internalWorkLogs = tx.internalWorkLogs
    )
  }

  def toSlickTransaction(tx: ApiTransaction): SlickApiTransaction = {
    SlickApiTransaction(
      id=Option(tx.id),
      accountId = tx.accountId,
      transactionId = tx.transactionId,
      api = tx.api,
      response = tx.apiResponse,
      apiName=tx.apiName,
      transactionDate = tx.transactionDate,
      processingTime = tx.processingTime,
      error = tx.error,
      errorMsg = tx.errorMsg,
      apiKey=tx.apiKey,
      environmentType = tx.environmentId,
      cachesUsed = tx.cachesUsed,
      uuid = tx.requestProps.uuid,
      accountIpAddress = tx.requestProps.accountIpAddress,
      requestProps = SlickRequestProps(
        originOfInvocation = tx.requestProps.originOfInvocation,
        parameters = tx.parameters,
        runId = tx.runId,
        requestUri = tx.requestUri,
        kyc = tx.kyc,
        ofac = tx.ofac
      ),
      scoringProps = SlickScoringProps(
        authScore = tx.authScore,
        confidence = tx.confidence,
        debug =  tx.debug,
        details = tx.details,
        reasonCodes = tx.reasonCodes,
        hasRiskCode = tx.hasRiskCode
      ),
      internalWorkLogs = tx.internalWorkLogs
    )
  }

}
