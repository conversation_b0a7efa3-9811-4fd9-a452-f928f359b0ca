package me.socure.transactionauditing.common.domain.v2

import me.socure.common.random.Random
import me.socure.common.slick.domain.{Pagination, Sort}
import me.socure.constants.EnvironmentConstants
import me.socure.transactionauditing.common.domain.fixture.APITransactionValueGenerator
import me.socure.transactionauditing.common.product.Products
import me.socure.transactionauditing.common.transaction.TransactionColumns.TransactionColumn
import me.socure.transactionauditing.common.transaction.{CustomFilterTypes, TransactionColumns}
import me.socure.transactionauditing.common.util.SlickDomainConversion
import me.socure.transactionauditstorage.mysqlschema.ApiTransaction

object TransactionSearchTestObjectGenerator {

  val pagination = Pagination(page = 1,size = 100)
  val sorting = Seq(Sort[TransactionColumn](TransactionColumns.TRANSACTION_DATE, false))
  val columnFilters = ColumnFilters(
    accountId = Some(1),
    accountIds = Some(Set(1234)),
    startDate = None,
    endDate = None,
    environment = Some(Set(EnvironmentConstants.PRODUCTION_ENVIRONMENT)),
    product = Some(Set(Products.EmailAuthScore.V3_0)),
    isError = Some(false),
    isKyc = Some(false),
    customerUserId = Some("1234"),
    runId = Some("1234"),
    startRecordId = None,
    hasRiskCode = None
  )
  val customFilters = Map(
    "modules" -> CustomFilter(values = Set("watchlistplus", "kyc"), filterType = Some(CustomFilterTypes.All))
  )
  val filters = RequestFilters(
    columns = columnFilters,
    custom = Some(customFilters)
  )
  val requestParams = RequestParams(
    pagination = Some(pagination),
    sorting = Some(sorting),
    filters = filters
  )

  val responseParams = ResponseParams(
    options = Some(ResponseOptions(maskPii = Some(false))),
    columns = Set(TransactionColumns.ID, TransactionColumns.PARAMETERS)
  )

  val tsr: TransactionSearchRequestV2 = TransactionSearchRequestV2(
    requestParams = requestParams,
    responseParams = responseParams
  )

  val daoTransaction: ApiTransaction = SlickDomainConversion.toSlickTransaction(APITransactionValueGenerator.aAPITransaction(
    parameters = Some("{\"nationalid\": \"" + Random.alphaNumeric(10) + "\" }")
  ))

  val transactionRecords = Seq(daoTransaction)

  val trxResponse = TransactionSearchResponseV2(
    pagination = None,
    records = Records(
      count = 1,
      transactions = transactionRecords
    )
  )

  def generateWithFilters(columns: ColumnFilters, customFilters: Option[Map[String, CustomFilter]], pagination: Pagination): TransactionSearchRequestV2 = {
    TransactionSearchRequestV2(
      requestParams = RequestParams(
        pagination = Some(pagination),
        sorting = Some(Seq(Sort[TransactionColumn](TransactionColumns.TRANSACTION_DATE, false))),
        filters = RequestFilters(
          columns = columns,
          custom = customFilters
        )
      ),
      responseParams = ResponseParams(
        columns = Set(TransactionColumns.ACCOUNT_ID),
        options = None
      )
    )
  }

  def generateWithResponseParams(responseParams: ResponseParams): TransactionSearchRequestV2 = {
    tsr.copy(responseParams= responseParams)
  }


}
