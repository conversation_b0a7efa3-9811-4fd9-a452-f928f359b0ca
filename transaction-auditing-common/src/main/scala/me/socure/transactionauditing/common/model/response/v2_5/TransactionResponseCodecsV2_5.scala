package me.socure.transactionauditing.common.model.response.v2_5

import argonaut.DecodeJson
import me.socure.transactionauditing.common.model.CommonCodecs._
import me.socure.transactionauditing.common.model.debug.codecs.DebugCodecs._
import me.socure.transactionauditing.common.model.ReasonCode
import me.socure.transactionauditing.common.model.debug.Debug
import me.socure.transactionauditing.common.model.response.{FraudModelScore, KycCorrelationIndices, KycDecision}

object TransactionResponseCodecsV2_5 {
  implicit val blacklistResponseV2_5Decoder: DecodeJson[BlacklistResponseV2_5] = DecodeJson { h =>
    for {
      reason <- (h --\ "reason").as[String]
      reportedDate <- (h --\ "reporteddate").as[String]
      industry <- (h --\ "industry").as[String]
    } yield BlacklistResponseV2_5(
      reason = reason,
      reportedDate = reportedDate,
      industry = industry
    )
  }

  implicit val kycCorrelationIndicesDecoder: Decode<PERSON>son[KycCorrelationIndices] = DecodeJson { h =>
    for {
      nameAddressPhoneIndex <- (h --\ "name_address_phone_index").as[String]
      nameAddressSsnIndex <- (h --\ "name_address_ssn_index").as[String]
    } yield KycCorrelationIndices(
      nameAddressPhoneIndex = nameAddressPhoneIndex.toDouble.toLong,
      nameAddressSsnIndex = nameAddressSsnIndex.toDouble.toLong
    )
  }

  implicit val kycDecisionIndicesDecoder: DecodeJson[KycDecision] = DecodeJson { h =>
    for {
      modelName <- (h --\ "modelName").as[String]
      modelVersion <- (h --\ "modelVersion").as[String]
      status <- (h --\ "status").as[String]
    } yield KycDecision(
      modelName = modelName,
      modelVersion = modelVersion,
      status = status
    )
  }

  implicit val watchlistV2_5Decoder: DecodeJson[WatchlistResponseV2_5] = DecodeJson { h =>
    for {
      matches <- (h --\ "matches").as[Map[String, WatchlistMatch2_5]]
    } yield WatchlistResponseV2_5(
      matches = matches
    )
  }

  implicit val watchlistMatchV2_5Decoder: DecodeJson[WatchlistMatch2_5] = DecodeJson { h =>
    for {
      matchScore <- (h --\ "matchscore").as[String]
      matchFields <- (h --\ "matchfields").as[List[String]]
      sourceUrls <- (h --\ "sourceUrls").as[List[String]]
    } yield WatchlistMatch2_5(
      score = matchScore.toInt,
      fields = matchFields,
      sourceUrls = sourceUrls
    )
  }

  implicit val detailResponseDecoder: DecodeJson[DetailResponse] = DecodeJson { h =>
    for {
      fieldValidations <- ((h =\ 0) --\ "fieldValidation").as[Map[String, String]]
      blacklistResponse <- ((h =\ 0) --\ "blacklisted").as[BlacklistResponseV2_5]
      profilesFound <- ((h =\ 0) --\ "profilesFound").as[List[String]]
      correlationScores <- ((h =\ 0) --\ "correlationScores").as[Map[String, String]]
      watchlistResponse <- ((h =\ 0) --\ "watchLists").as[Option[WatchlistResponseV2_5]]

      kycFieldValidations <- ((h =\ 0) --\ "kyc").as[Map[String, String]]
      correlationIndices <- ((h =\ 0) --\ "kycCorrelationIndices").as[Option[KycCorrelationIndices]]
      decision <- ((h =\ 0) --\ "decision").as[Option[KycDecision]]
      cvi <- ((h =\ 0) --\ "kycCVI").as[Option[Int]]
    } yield DetailResponse(
      fieldValidations = fieldValidations,
      blacklistResponse = blacklistResponse,
      profilesFound = profilesFound,
      kycResponse = KycResponse2_5(
        cvi = cvi,
        correlationIndices = correlationIndices,
        decision = decision,
        fieldValidations = kycFieldValidations.mapValues(_.toDouble)
      ),
      correlationScores = correlationScores,
      watchlistResponse = watchlistResponse
    )
  }

  implicit val exceptionResponseV2_5Decoder: DecodeJson[ExceptionResponseV2_5] = DecodeJson { h =>
    for {
      status <- (h --\ "status").as[String]
      data <- (h --\ "data").as[Map[String, String]]
      message <- (h --\ "message").as[Option[String]]
    } yield ExceptionResponseV2_5(
      status = status,
      referenceId = data("referenceid"),
      data = data,
      message = message
    )
  }

  implicit val transactionResponseV2_5Decoder: DecodeJson[TransactionResponseV2_5] = DecodeJson { h =>
    for {
      status <- (h --\ "status").as[String]
      msg <- (h --\ "msg").as[Option[String]]
      referenceId <- (h --\ "data"  --\ "referenceid").as[String]
      authScore <- (h --\ "data" --\ "authscore").as[Int]
      fraudScore <- (h --\ "data" --\ "fraudscore").as[Double]
      reasonCodes <- (h --\ "data" --\ "reasoncodes").as[List[ReasonCode]]
      confidence <- (h --\ "data" --\ "confidence").as[Double]
      customModels <- (h --\ "data" --\ "custommodels").as[Option[List[FraudModelScore]]]
      details <- (h --\ "data" --\ "details").as[DetailResponse]
      debug <- (h --\ "data" --\ "debug").as[Option[Debug]]
    } yield TransactionResponseV2_5(
      status = status,
      msg = if(msg.getOrElse("").length > 0) msg else None,
      referenceId = referenceId,
      fraudScore = fraudScore,
      authScore = authScore,
      reasonCodes = reasonCodes,
      confidence = confidence,
      customModels = if(customModels.getOrElse(List.empty).nonEmpty) customModels else None,
      details = details,
      debug = debug
    )
  }
}
