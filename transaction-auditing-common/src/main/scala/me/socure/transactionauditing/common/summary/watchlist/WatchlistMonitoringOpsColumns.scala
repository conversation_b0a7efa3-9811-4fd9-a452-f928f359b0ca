package me.socure.transactionauditing.common.summary.watchlist

import me.socure.types.scala.{ByMember, Enum}

object WatchlistMonitoringOpsColumns extends Enum with ByMember {

  type WatchlistMonitoringOpsColumn = EnumVal

  case class EnumVal(name: String) extends Value

  val ID = new WatchlistMonitoringOpsColumn("id")
  val ACCOUNT_ID = new WatchlistMonitoringOpsColumn("accountId")
  val TRANSACTION_ID = new WatchlistMonitoringOpsColumn("transactionId")
  val WL_REFERENCE_ID = new WatchlistMonitoringOpsColumn("referenceId")
  val OPERATION = new WatchlistMonitoringOpsColumn("operation")
  val ENVIRONMENT_ID = new WatchlistMonitoringOpsColumn("environmentId")
  val UPDATED_AT = new WatchlistMonitoringOpsColumn("updatedAt")

  def byColumn(column: String): Option[WatchlistMonitoringOpsColumn] = {
    values.find(_.name == column)
  }

}
