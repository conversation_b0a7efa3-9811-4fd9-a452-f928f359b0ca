package me.socure.transactionauditing.common.model

import me.socure.transactionauditing.common.model.debug.Debug
import me.socure.transactionauditing.common.model.parameters.Parameters
import me.socure.transactionauditing.common.model.response.v3_0.TransactionResponseV3_0

case class Transaction(
                        accountId:Option[Long],
                        transactionId:String,
                        parameters: Parameters,
                        debug: Debug,
                        response: TransactionResponseV3_0
                      )