package me.socure.transactionauditing.common.domain

import org.joda.time.DateTime

case class ApiTransaction(id:Long,
                          accountId:Option[Long],
                          transactionId:String,
                          api: Option[String],
                          apiResponse:Option[String],
                          fraudScore:Option[Double],
                          productName:Option[String],
                          apiName:Option[String],
                          transactionDate:Option[DateTime],
                          processingTime:Option[Long],
                          error: Option[Boolean],
                          errorMsg: Option[String],
                          apiKey:Option[String],
                          environmentId:Option[Long],
                          cachesUsed: Option[String],
                          status: Option[String],
                          isBlacklist:Option[Boolean],
                          blacklistInfo:Option[BlackListInfo],
                          requestProps: RequestProps,
                          scoringProps: ScoringProps,
                          internalWorkLogs: Option[String] = None
                           ) {
  //Easy accessor methods for request props:
  def originOfInvocation = requestProps.originOfInvocation

  def parameters = requestProps.parameters

  def runId = requestProps.runId

  def requestUri = requestProps.requestURI

  def kyc = requestProps.kyc

  def ofac = requestProps.ofac

  def geocode = requestProps.geocode

  def customerUserId = requestProps.customerUserId

  def uuid = requestProps.uuid

  //Easy accessor methods for scoring props:
  def authScore = scoringProps.authScore

  def confidence = scoringProps.confidence

  def debug = scoringProps.debug

  def details = scoringProps.details

  def reasonCodes = scoringProps.reasonCodes

  def hasRiskCode = scoringProps.hasRiskCode
}