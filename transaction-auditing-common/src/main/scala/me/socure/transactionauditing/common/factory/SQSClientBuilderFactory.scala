package me.socure.transactionauditing.common.factory

import com.amazonaws.auth.AWSCredentialsProviderChain
import com.amazonaws.services.sqs.AmazonSQSClientBuilder
import me.socure.common.aws.ProfileBasedCredentialsProviderChain

object SQSClientBuilderFactory {

  def get() = {
    AmazonSQSClientBuilder
      .standard()
      .withCredentials(new ProfileBasedCredentialsProviderChain)
  }

  def get(providerChain: AWSCredentialsProviderChain) = {
    AmazonSQSClientBuilder
      .standard()
      .withCredentials(providerChain)
  }

}
