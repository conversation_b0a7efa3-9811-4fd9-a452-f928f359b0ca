package me.socure.transactionauditing.common.config

import akka.stream.alpakka.sqs.{SqsSinkSettings, SqsSourceSettings}
import com.amazon.sqs.javamessaging.AmazonSQSExtendedClient
import com.amazonaws.services.sqs.model.GetQueueUrlRequest
import me.socure.transactionauditing.common.model.SqsQueue
import me.socure.transactionauditing.common.util.SqsQueueEnums
import me.socure.transactionauditing.common.util.SqsQueueEnums.SqsQueueEnum
import org.slf4j.LoggerFactory

class ClientSqsEndpoint(val client: AmazonSQSExtendedClient, endpointConfig: ClientSqsEndpointConfig, val queueType: SqsQueueEnum) {
  val logger = LoggerFactory.getLogger(getClass)
  val queue: SqsQueue = getQueueUrl

  val queueSourceSettings: SqsSourceSettings = SqsSourceSettings(
    endpointConfig.queue.waitTimeSeconds,
    endpointConfig.queue.maxBufferSize,
    endpointConfig.queue.maxBatchSize
  )

  val queueSinkSettings: SqsSinkSettings = SqsSinkSettings(
    endpointConfig.queue.maxInFlight
  )

  private def getQueueUrl: SqsQueue = {
    logger.debug(s"Fetching QueueUrl for queueName ${endpointConfig.queue.queueName} region ${endpointConfig.region} with queueOwnerAWSAccountId ${endpointConfig.queue.queueOwnerAWSAccountId}")
    val queueUrl = endpointConfig.queue.queueOwnerAWSAccountId match {
      case Some(ownerAccountId) if ownerAccountId.trim.nonEmpty =>
        val getQueueUrlRequest = new GetQueueUrlRequest(endpointConfig.queue.queueName)
                                    .withQueueOwnerAWSAccountId(ownerAccountId)
        client.getQueueUrl(getQueueUrlRequest).getQueueUrl
      case _ =>
        client.getQueueUrl(endpointConfig.queue.queueName).getQueueUrl
    }
    logger.debug(s"QueueUrl for queueName ${endpointConfig.queue.queueName} region ${endpointConfig.region} set to $queueUrl")
    SqsQueue(queueUrl)
  }
}
