package me.socure.transactionauditing.common.model

import argonaut.Parse
import me.socure.transactionauditing.common.domain.ApiTransaction
import me.socure.transactionauditing.common.model.response.TransactionResponse
import me.socure.transactionauditing.common.model.response.v2_5.TransactionResponseV2_5
import me.socure.transactionauditing.common.model.response.v3_0.TransactionResponseV3_0
import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.transaction.id.TrxId
import me.socure.transactionauditing.common.model.response.v1_0.watchlist.{TransactionWatchlistResponseV1_0, WatchlistResponseV1_0}
import me.socure.transactionauditing.common.model.response.v2_5.TransactionResponseCodecsV2_5.transactionResponseV2_5Decoder
import me.socure.transactionauditing.common.model.response.v3_0.TransactionResponseCodecsV3_0.transactionResponseV3_0Decoder
import me.socure.transactionauditing.common.model.response.v1_0.watchlist.TransactionWatchlistResponseV1_0Codecs.transactionResponseV1_0Decoder

import scalaz.{-\/, \/-}


object TransactionResponseParser {
  private val logger = TransactionAwareLoggerFactory.getLogger(getClass)
  def parse(msg: ApiTransaction): Option[TransactionResponse] = {
    implicit val trxId: TrxId = TrxId(msg.transactionId)
    msg.apiResponse match {
      case Some(response) => {
        msg.apiName match {
          case Some(apiName) => {
            if(apiName.contains("3.0")) {
              Option(response).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[TransactionResponseV3_0](_) match {
                case \/-(transactionResponse) => Some(transactionResponse)
                case -\/(err) =>
                  logger.error("Unable to extract transaction response from 3.0 transaction", err)
                  None
              })
            }
            else if(apiName.contains("2.5")) {
              Option(response).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[TransactionResponseV2_5](_) match {
                case \/-(transactionResponse) => Some(transactionResponse)
                case -\/(err) =>
                  logger.error("Unable to extract transaction response from 2.5 transaction", err)
                  None
              })
            }
            else if(apiName.toLowerCase().contains("1/watchlist")) {
              Option(response).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[TransactionWatchlistResponseV1_0](_) match {
                case \/-(transactionResponse) => Some(transactionResponse)
                case -\/(err) =>
                  logger.error("Unable to extract transaction response from watchlist transaction", err)
                  None
              })
            }
            else {
              None
            }
          }
          case None => None
        }
      }
      case None => None
    }
  }


  def parse(msg: me.socure.transactionauditstorage.mysqlschema.ApiTransaction): Option[TransactionResponse] = {
    implicit val trxId: TrxId = TrxId(msg.transactionId)
    msg.response match {
      case Some(response) => {
        msg.apiName match {
          case Some(apiName) => {
            if(apiName.contains("3.0")) {
              Option(response).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[TransactionResponseV3_0](_) match {
                case \/-(transactionResponse) => Some(transactionResponse)
                case -\/(err) =>
                  logger.error("Unable to extract transaction response from 3.0 transaction", err)
                  None
              })
            }
            else if(apiName.contains("2.5")) {
              Option(response).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[TransactionResponseV2_5](_) match {
                case \/-(transactionResponse) => Some(transactionResponse)
                case -\/(err) =>
                  logger.error("Unable to extract transaction response from 2.5 transaction", err)
                  None
              })
            }
            else if(apiName.toLowerCase().contains("1/watchlist")) {
              Option(response).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[TransactionWatchlistResponseV1_0](_) match {
                case \/-(transactionResponse) => Some(transactionResponse)
                case -\/(err) =>
                  logger.error("Unable to extract transaction response from watchlist transaction", err)
                  None
              })
            }
            else {
              None
            }
          }
          case None => None
        }
      }
      case None => None
    }
  }
}
