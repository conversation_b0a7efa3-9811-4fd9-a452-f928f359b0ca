package me.socure.transactionauditing.common.model.response.v3_0

import argonaut.DecodeJson
import me.socure.transactionauditing.common.model.CommonCodecs._
import me.socure.transactionauditing.common.model.ReasonCode
import me.socure.transactionauditing.common.model.debug.Debug
import me.socure.transactionauditing.common.model.debug.codecs.DebugCodecs._
import me.socure.transactionauditing.common.model.response._

object TransactionResponseCodecsV3_0 {

  implicit val fraudResponseDecoder: DecodeJson[FraudResponse] = DecodeJson { h =>
    for {
      reasonCodes <- (h --\ "reasonCodes").as[Set[ReasonCode]]
      scores <- (h --\ "scores").as[List[FraudModelScore]]
    } yield FraudResponse(
      reasonCodes = reasonCodes,
      scores = scores
    )
  }

  implicit val authResponseDecoder: DecodeJson[AuthResponse] = DecodeJson { h =>
    for {
      reasonCodes <- (h --\ "reasonCodes").as[Set[ReasonCode]]
      score <- (h --\ "score").as[Int]
    } yield AuthResponse(
      reasonCodes = reasonCodes,
      score = score
    )
  }

  implicit val blacklistResponseDecoderV3_0: DecodeJson[BlacklistResponseV3_0] = DecodeJson { h =>
    for {
      reasonCodes <- (h --\ "reasonCodes").as[Set[ReasonCode]]
      matches <- (h --\ "matches").as[List[BlacklistMatchV3_0]]
    } yield BlacklistResponseV3_0(
      reasonCodes = reasonCodes,
      matches = matches
    )
  }

  implicit val blacklistMatchDecoderV3_0: DecodeJson[BlacklistMatchV3_0] = DecodeJson { h =>
    for {
      reason <- (h --\ "reason").as[String]
      reportedDate <- (h --\ "reportedDate").as[String]
      industry <- (h --\ "industry").as[String]
    } yield BlacklistMatchV3_0(
      reason = reason,
      reportedDate = reportedDate,
      industry = industry
    )
  }

  implicit val correlationResponseDecoder: DecodeJson[CorrelationResponse] = DecodeJson { h =>
    for {
      score <- (h --\ "score").as[Double]
      reasonCodes <- (h --\ "reasonCodes").as[Set[ReasonCode]]
    } yield CorrelationResponse(
      score = score,
      reasonCodes = reasonCodes
    )
  }

  implicit val piiRiskResponse: DecodeJson[PiiRiskResponse] = DecodeJson { h =>
    for {
      score <- (h --\ "score").as[Double]
      reasonCodes <- (h --\ "reasonCodes").as[Set[ReasonCode]]
    } yield PiiRiskResponse(
      score = score,
      reasonCodes = reasonCodes
    )
  }

  implicit val watchlistV3_0Decoder: DecodeJson[WatchlistResponseV3_0] = DecodeJson { h =>
    for {
      reasonCodes <- (h --\ "reasonCodes").as[Set[ReasonCode]]
      matches <- (h --\ "matches").as[Map[String, List[WatchlistMatchV3_0]]]
    } yield WatchlistResponseV3_0(
      reasonCodes = reasonCodes,
      matches = matches
    )
  }

  implicit val watchlistMatchV3_0Decoder: DecodeJson[WatchlistMatchV3_0] = DecodeJson { h =>
    for {
      matchScore <- (h --\ "matchscore").as[String]
      matchFields <- (h --\ "matchfields").as[List[String]]
      sourceUrls <- (h --\ "sourceUrls").as[List[String]]
      comments <- (h --\ "comments").as[Option[String]]
      offense <- (h --\ "offense").as[Option[String]]
    } yield WatchlistMatchV3_0(
      score = matchScore.toInt,
      fields = matchFields,
      sourceUrls = sourceUrls,
      comments = comments,
      offense = offense
    )
  }

  implicit val kycDecoder: DecodeJson[KycResponse3_0] = DecodeJson { h =>
    for {
      reasonCodes <- (h --\ "reasonCodes").as[Set[ReasonCode]]
      cvi <- (h --\ "cvi").as[Option[Int]]
      correlationIndices <- (h --\ "correlationIndices").as[Option[KycCorrelationIndices]]
      decision <- (h --\ "decision").as[Option[KycDecision]]
      fieldValidations <- (h --\ "fieldValidations").as[Map[String, Double]]
    } yield KycResponse3_0(
      reasonCodes = reasonCodes,
      cvi = cvi,
      correlationIndices = correlationIndices,
      decision = decision,
      fieldValidations = fieldValidations
    )
  }

  implicit val addressDecoder: DecodeJson[Address] = DecodeJson { h =>
    for {
      streetAddress <- (h --\ "streetAddress").as[String]
      city <- (h --\ "city").as[String]
      state <- (h --\ "state").as[String]
      zip <- (h --\ "zip").as[String]
    } yield Address(
      streetAddress = streetAddress,
      city = city,
      state = state,
      zip = zip
    )
  }

  implicit val phoneNumberDecoder: DecodeJson[PhoneNumber] = DecodeJson { h =>
    for {
      phoneNumber <- (h --\ "phoneNumber").as[String]
    } yield PhoneNumber(
      phoneNumber = phoneNumber
    )
  }

  implicit val kycBestMatchedEntityDecoder: DecodeJson[KycBestMatchedEntity] = DecodeJson { h =>
    for {
      firstName <- (h --\ "firstName").as[String]
      middleName <- (h --\ "middleName").as[String]
      surName <- (h --\ "surName").as[String]
      suffix <- (h --\ "suffix").as[String]
      ssn <- (h --\ "ssn").as[String]
      dob <- (h --\ "dob").as[String]
      mobileNumber <- (h --\ "mobileNumber").as[String]
      normalizedAddress <- (h --\ "normalizedAddress").as[Address]
      associatedPhoneNumbers <- (h --\ "associatedPhoneNumbers").as[List[PhoneNumber]]
      associatedAddresses <- (h --\ "associatedAddresses").as[List[Address]]
    } yield KycBestMatchedEntity(
      firstName = firstName,
      middleName = middleName,
      surName = surName,
      suffix = suffix,
      ssn = ssn,
      dob = dob,
      mobileNumber = mobileNumber,
      normalizedAddress = normalizedAddress,
      associatedPhoneNumbers = associatedPhoneNumbers,
      associatedAddresses = associatedAddresses
    )
  }

  implicit val kycPlusDecoder: DecodeJson[KycPlusResponse3_0] = DecodeJson { h =>
    for {
      reasonCodes <- (h --\ "reasonCodes").as[Set[ReasonCode]]
      cvi <- (h --\ "cvi").as[Option[Int]]
      correlationIndices <- (h --\ "correlationIndices").as[Option[KycCorrelationIndices]]
      decision <- (h --\ "decision").as[Option[KycDecision]]
      fieldValidations <- (h --\ "fieldValidations").as[Map[String, Double]]
      bestMatchedEntity <- (h --\ "bestMatchedEntity").as[Option[KycBestMatchedEntity]]
    } yield KycPlusResponse3_0(
      reasonCodes = reasonCodes,
      cvi = cvi,
      correlationIndices = correlationIndices,
      decision = decision,
      fieldValidations = fieldValidations,
      bestMatchedEntity = bestMatchedEntity
    )
  }

  implicit val kycCorrelationIndicesDecoder: DecodeJson[KycCorrelationIndices] = DecodeJson { h =>
    for {
      nameAddressPhoneIndex <- (h --\ "nameAddressPhoneIndex").as[Long]
      nameAddressSsnIndex <- (h --\ "nameAddressSsnIndex").as[Long]
    } yield KycCorrelationIndices(
      nameAddressPhoneIndex = nameAddressPhoneIndex,
      nameAddressSsnIndex = nameAddressSsnIndex
    )
  }

  implicit val kycDecisionIndicesDecoder: DecodeJson[KycDecision] = DecodeJson { h =>
    for {
      modelName <- (h --\ "modelName").as[String]
      modelVersion <- (h --\ "modelVersion").as[String]
      status <- (h --\ "status").as[String]
    } yield KycDecision(
      modelName = modelName,
      modelVersion = modelVersion,
      status = status
    )
  }

  implicit val decisionResponseDecoder: DecodeJson[DecisionResponse] = DecodeJson { h =>
    for {
      modelName <- (h --\ "modelName").as[Option[String]]
      modelVersion <- (h --\ "modelVersion").as[Option[String]]
      value <- (h --\ "value").as[Option[String]]
      response <- (h --\ "response").as[Option[String]]
    } yield DecisionResponse(
      modelName = modelName,
      modelVersion = modelVersion,
      value = value,
      response = response
    )
  }

  implicit val socialResponseDecoder: DecodeJson[SocialResponse] = DecodeJson { h =>
    for {
      profilesFound <- (h --\ "profilesFound").as[List[String]]
      reasonCodes <- (h --\ "reasonCodes").as[Set[ReasonCode]]
    } yield SocialResponse(
      profilesFound = profilesFound,
      reasonCodes = reasonCodes
    )
  }

  implicit val exceptionResponseV3_0Decoder: DecodeJson[ExceptionResponseV3_0] = DecodeJson { h =>
    for {
      status <- (h --\ "status").as[String]
      referenceId <- (h --\ "referenceId").as[String]
      data <- (h --\ "data").as[Option[Map[String, List[String]]]]
      message <- (h --\ "message").as[Option[String]]
    } yield ExceptionResponseV3_0(
      status = status,
      referenceId = referenceId,
      data = data,
      message = message
    )
  }

  implicit val alertListMatchDecoder: DecodeJson[AlertListMatch] = DecodeJson { h =>
    for {
      element <- (h --\ "element").as[String]
      datasetName <- (h --\ "datasetName").as[String]
      reason <- (h --\ "reason").as[Option[String]]
      industryName <- (h --\ "industryName").as[Option[String]]
      lastReportedDate <- (h --\ "lastReportedDate").as[Option[String]]
      reportedCount <- (h --\ "reportedCount").as[Option[Int]]
    } yield AlertListMatch(
      element = element,
      datasetName = datasetName,
      reason = reason,
      industryName = industryName,
      lastReportedDate = lastReportedDate,
      reportedCount = reportedCount
    )
  }

  implicit val documentVerificationDecoder: DecodeJson[DocumentVerification] = DecodeJson { h =>
    for {
      reasonCodes <- (h --\ "reasonCodes").as[Set[ReasonCode]]
      documentType <- (h --\ "documentType" --\ "type").as[String]
      documentVersion <- (h --\ "documentType" --\ "version").as[Option[String]]
      country <- (h --\ "documentType" --\ "country").as[String]
      state <- (h --\ "documentType" --\ "state").as[Option[String]]
      decisionName <- (h --\ "decision" --\ "name").as[String]
      decisionValue <- (h --\ "decision" --\ "value").as[String]
      firstName <- (h --\ "documentData" --\ "firstName").as[Option[String]]
      surName <- (h --\ "documentData" --\ "surName").as[Option[String]]
      fullName <- (h --\ "documentData" --\ "fullName").as[Option[String]]
      address <- (h --\ "documentData" --\ "address").as[Option[String]]
      documentNumber <- (h --\ "documentData" --\ "documentNumber").as[Option[String]]
      dob <- (h --\ "documentData" --\ "dob").as[Option[String]]
      expirationDate <- (h --\ "documentData" --\ "expirationDate").as[Option[String]]
      issueDate <- (h --\ "documentData" --\ "issueDate").as[Option[String]]
    } yield DocumentVerification(
      reasonCodes = reasonCodes,
      documentType = documentType,
      documentVersion = documentVersion,
      country = country,
      state = state,
      decisionName = decisionName,
      decisionValue = decisionValue,
      firstName = firstName,
      surName = surName,
      fullName = fullName,
      address = address,
      documentNumber = documentNumber,
      dob = dob,
      expirationDate = expirationDate,
      issueDate = issueDate
    )
  }

  implicit val alertListResponseDecoder: DecodeJson[AlertListResponse] = DecodeJson { h =>
    for {
      reasonCodes <- (h --\ "reasonCodes").as[Set[ReasonCode]]
      matches <- (h --\ "matches").as[Set[AlertListMatch]]
    } yield AlertListResponse(
      reasonCodes = reasonCodes,
      matches = matches
    )
  }

  implicit val transactionResponseV3_0Decoder: DecodeJson[TransactionResponseV3_0] = DecodeJson { h =>
    for {
      referenceId <- (h --\ "referenceId").as[String]
      authenticityResponse <- (h --\ "authenticity").as[Option[AuthResponse]]
      blacklistResponse <- (h --\ "blacklist").as[Option[BlacklistResponseV3_0]]
      nameAddressCorrelationResponse <- (h --\ "nameAddressCorrelation").as[Option[CorrelationResponse]]
      nameEmailCorrelationResponse <- (h --\ "nameEmailCorrelation").as[Option[CorrelationResponse]]
      namePhoneCorrelationResponse <- (h --\ "namePhoneCorrelation").as[Option[CorrelationResponse]]
      fraudResponse <- (h --\ "fraud").as[Option[FraudResponse]]
      kycResponse <- (h --\ "kyc").as[Option[KycResponse3_0]]
      documentVerification <- (h --\ "documentVerification").as[Option[DocumentVerification]]
      decisionResponse <- (h --\ "decision").as[Option[DecisionResponse]]
      socialResponse <- (h --\ "social").as[Option[SocialResponse]]
      addressRiskResponse <- (h --\ "addressRisk").as[Option[PiiRiskResponse]]
      emailRiskResponse <- (h --\ "emailRisk").as[Option[PiiRiskResponse]]
      phoneRiskResponse <- (h --\ "phoneRisk").as[Option[PiiRiskResponse]]
      watchlistResponse <- (h --\ "watchlist").as[Option[WatchlistResponseV3_0]]
      alertListResponseOpt <- (h --\ "alertList").as[Option[AlertListResponse]]
      debug <- (h --\ "debug").as[Option[Debug]]
      kycPlusOpt <- (h --\ "kycPlus").as[Option[KycPlusResponse3_0]]
    } yield TransactionResponseV3_0(
      referenceId = referenceId,
      fraud =fraudResponse,
      authenticity = authenticityResponse,
      nameAddressCorrelation = nameAddressCorrelationResponse,
      nameEmailCorrelation = nameEmailCorrelationResponse,
      namePhoneCorrelation = namePhoneCorrelationResponse,
      blacklist = blacklistResponse,
      addressRisk = addressRiskResponse,
      emailRisk = emailRiskResponse,
      phoneRisk = phoneRiskResponse,
      kyc = kycResponse,
      documentVerification = documentVerification,
      decision = decisionResponse,
      social = socialResponse,
      watchlist = watchlistResponse,
      debug = debug,
      alertList = alertListResponseOpt,
      kycPlus = kycPlusOpt
    )
  }
}
