package me.socure.transactionauditing.common.config

import com.typesafe.config.Config

object S3ConfigFactory {
  def get(config: Config, pipelineBasedConfig: Boolean = false): S3Config = {
    if (pipelineBasedConfig) {
      val largeFileBucket = config.getString("pipeline.transaction.sqs.large.files.s3.bucket.name")
      val tpBucket = if (config.hasPath("pipeline.thirdparty.sqs.large.files.s3.bucket.name")) Some(config.getString("pipeline.thirdparty.sqs.large.files.s3.bucket.name")) else None
      val awsRegion = config.getString("pipeline.region")
      val maskingErrorBucket: Option[String] = if (config.hasPath("pipeline.transaction.sqs.s3.masking-failures.bucket")) Some(config.getString("pipeline.transaction.sqs.s3.masking-failures.bucket")) else None
      val parsingErrorBucket: Option[String] = if (config.hasPath("pipeline.transaction.sqs.s3.parsing-failures.bucket")) Some(config.getString("pipeline.transaction.sqs.s3.parsing-failures.bucket")) else None
      val auditDataBucket: Option[String] = if(config.hasPath("pipeline.transaction.sqs.s3.audit-data.bucket")) Some(config.getString("pipeline.transaction.sqs.s3.audit-data.bucket")) else None

      S3Config(
        region = awsRegion,
        thirdPartyBucket = tpBucket,
        largeFileBucket = largeFileBucket,
        maskingErrorBucket = maskingErrorBucket,
        parsingErrorBucket = parsingErrorBucket,
        auditDataBucket = auditDataBucket
      )
    } else {
      val largeFileBucket = config.getString("transaction-auditing.aws.s3.largefiles.folder")
      val tpBucket = if (config.hasPath("transaction-auditing.aws.s3.third-party.bucket")) Some(config.getString("transaction-auditing.aws.s3.third-party.bucket")) else None
      val awsRegion = config.getString("transaction-auditing.aws.s3.third-party.region")
      val maskingErrorBucket: Option[String] = if (config.hasPath("transaction-auditing.aws.s3.masking-failures.bucket")) Some(config.getString("transaction-auditing.aws.s3.masking-failures.bucket")) else None
      val parsingErrorBucket: Option[String] = if (config.hasPath("pipeline.transaction.sqs.s3.parsing-failures.bucket")) Some(config.getString("pipeline.transaction.sqs.s3.parsing-failures.bucket")) else None

      S3Config(
        region = awsRegion,
        thirdPartyBucket = tpBucket,
        largeFileBucket = largeFileBucket,
        maskingErrorBucket = maskingErrorBucket,
        parsingErrorBucket = parsingErrorBucket,
        auditDataBucket = None
      )
    }

  }
}
