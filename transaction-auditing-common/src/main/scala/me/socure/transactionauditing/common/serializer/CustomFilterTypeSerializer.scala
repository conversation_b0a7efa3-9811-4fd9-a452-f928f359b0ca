package me.socure.transactionauditing.common.serializer

import me.socure.transactionauditing.common.transaction.CustomFilterTypes
import me.socure.transactionauditing.common.transaction.CustomFilterTypes.CustomFilterType
import org.json4s.CustomSerializer
import org.json4s.JsonAST.{JNull, JString}

/**
 * Created by sum<PERSON><PERSON> on 20/01/22
 **/
class CustomFilterTypeSerializer
  extends CustomSerializer[CustomFilterType](format => (
    {
      case JNull => null
      case jstr: JString => CustomFilterTypes.byFilterType(jstr.s) match {
        case Some(filterType) => filterType
        case None => throw new Exception(s"Invalid filterType given: '${jstr.s}'")
      }
    },
    {
      case column: CustomFilterType => {
        JString(column.name)
      }
    }
  ))
