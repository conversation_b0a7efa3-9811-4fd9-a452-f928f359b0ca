package me.socure.transactionauditing.common.json

import me.socure.constants.{EnvironmentConstants, JsonFormats}
import me.socure.transactionauditing.common.product.{ProductNames, ProductVersions, Products}
import me.socure.transactionauditing.common.serializer.{CustomFilterTypeSerializer, TransactionColumnsSerializer}
import me.socure.transactionauditing.common.summary.watchlist.WatchlistMonitoringOpsColumnsSerializer
import org.joda.time.format.DateTimeFormat
import org.json4s.Formats
import org.json4s.ext.DateTimeSerializer

object TrxJsonFormats {
  private[json] val fmt = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSSZ").withZoneUTC()

  private val jodaTimeSerializers = org.json4s.ext.JodaTimeSerializers.all diff List(DateTimeSerializer)

  val value: Formats = {
    JsonFormats.formats ++
      jodaTimeSerializers.toIterable +
      new DateTimeSerializerWithDefaultFallback(fmt) +
      new org.json4s.ext.EnumNameSerializer(EnvironmentConstants) +
      new org.json4s.ext.EnumNameSerializer(ProductNames) +
      new org.json4s.ext.EnumNameSerializer(ProductVersions) +
      new org.json4s.ext.EnumNameSerializer(Products) +
      new TransactionColumnsSerializer() +
    new CustomFilterTypeSerializer() +
      new WatchlistMonitoringOpsColumnsSerializer
  }
}
