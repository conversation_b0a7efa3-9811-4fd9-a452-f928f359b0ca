package me.socure.transactionauditing.common.model.response.v1_0.watchlist

import me.socure.transactionauditing.common.model.ReasonCode
import me.socure.transactionauditing.common.model.response.TransactionResponse

case class TransactionWatchlistResponseV1_0(
                         status: String,
                         referenceId: String,
                         reasonCodes: List[ReasonCode],
                         matches: WatchlistResponseV1_0,
                         msg: Option[String]
                       ) extends TransactionResponse