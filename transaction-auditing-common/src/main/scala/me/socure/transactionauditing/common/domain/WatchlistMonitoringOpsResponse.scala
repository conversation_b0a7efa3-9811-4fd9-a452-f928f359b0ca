package me.socure.transactionauditing.common.domain

trait WatchlistMonitoringOperationResponse

case class SingleOperationResponse(
                                      status: String,
                                      data: WatchlistMonitoringOperation
                                    ) extends WatchlistMonitoringOperationResponse

case class MultiOperationResponse(
                                     status: String,
                                     data: Seq[WatchlistMonitoringOperation]
                                   ) extends WatchlistMonitoringOperationResponse

