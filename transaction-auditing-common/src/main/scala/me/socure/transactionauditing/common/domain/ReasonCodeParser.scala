package me.socure.transactionauditing.common.domain

import me.socure.constants.JsonFormats
import org.json4s._
import org.json4s.native._

object ReasonCodeParser {
  private implicit val formats: Formats = JsonFormats.formats
  def parse(rcList: Option[String]): Set[String] = {
    rcList match {
      case Some(rcs) => JsonMethods.parse(rcs).extract[Set[String]]
      case None => Set.empty
    }
  }
}
