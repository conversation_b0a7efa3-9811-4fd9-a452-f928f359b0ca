package me.socure.transactionauditing.common.model.response.v3_0

import me.socure.transactionauditing.common.model.ReasonCode
import me.socure.transactionauditing.common.model.response.{KycCorrelationIndices, KycDecision}

case class KycResponse3_0(
                reasonCodes: Set[ReasonCode],
                cvi: Option[Int],
                correlationIndices: Option[KycCorrelationIndices],
                decision: Option[KycDecision],
                fieldValidations: Map[String, Double]
              )
