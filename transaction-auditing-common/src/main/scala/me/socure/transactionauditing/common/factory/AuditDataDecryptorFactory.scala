package me.socure.transactionauditing.common.factory

import java.net.InetSocketAddress

import com.amazonaws.encryptionsdk.CryptoMaterialsManager
import com.typesafe.config.Config
import me.socure.account.client.encryption.{CachedEncryptionKeysClient, EncryptionKeysClientFactory}
import me.socure.crypto.key.common.{CryptoMaterialsManagerAccountsService, SocureCryptoMaterialsManager}
import me.socure.transaction.auditing.encryption.decryptor.{AuditDataDecryptor, DecryptorFactory}
import net.spy.memcached.MemcachedClient

import scala.concurrent.ExecutionContext

object AuditDataDecryptorFactory {
  def create(config: Config,
             cryptoMaterialsManager: CryptoMaterialsManager,
             cryptoMaterialsManagerAccountsService: CryptoMaterialsManagerAccountsService
              )(
  implicit ec: ExecutionContext): AuditDataDecryptor = {
    val cseConfig = config.getConfig("client.specific.encryption")

    val accountEndpoint = config.getConfig("account.service").getString("endpoint")
    val memcachedClient = new MemcachedClient(new InetSocketAddress(config.getConfig("memcached").getString("host"), config.getConfig("memcached").getInt("port")))

    val encryptionKeysClient: CachedEncryptionKeysClient = EncryptionKeysClientFactory.createCached(
      accountServiceEndpoint = accountEndpoint,
      memcachedClient = memcachedClient,
      ttl = None
    )

    val decryptor = DecryptorFactory.create(
      cryptoMaterialsManagerAccountsService = cryptoMaterialsManagerAccountsService,
      cryptoMaterialsManager = cryptoMaterialsManager,
      encryptionContextAccountIdKey = cseConfig.getString("encryption.context.account_id.key")
    )

    new AuditDataDecryptor(
      encryptionKeysClient = encryptionKeysClient,
      decryptor = decryptor
    )
  }
}
