package me.socure.transactionauditing.common.model

import argonaut.Argonaut._
import argonaut._

object CodecHelpers {

  implicit def optionDecoder[T](implicit decoder: DecodeJson[T]): DecodeJson[DecodedResultOption[T]] = DecodeJson { h =>
    h.as[Json].flatMap {
      case j if j == jNull => DecodeResult.ok(DecodedResultNone())
      case _ => h.as[T].map(DecodedResultSome(_))
    }
  }

  implicit class RichACursor(val aCursor: ACursor) extends AnyVal {
    def hasField(field: String): DecodeResult[Boolean] = {
      aCursor.as[Json].map(_.hasField(field))
    }

    def getOrElseNone[T](field: String)(implicit decode:DecodeJson[T]): DecodeResult[Option[T]] = {
      for {
        hasField <- aCursor.as[Json].map(_.hasField(field))
        result <- {
          if (hasField)
            (aCursor --\ field).as[DecodedResultOption[T]]
          else
            DecodeResult.ok(DecodedResultNone(): DecodedResultOption[T])
        }
      } yield result.toOption
    }
  }

  //The default DecodeJson[Option[T]] hides errors, so I created one here.
  sealed trait DecodedResultOption[T] {
    def toOption: Option[T]
  }

  case class DecodedResultSome[T](value: T) extends DecodedResultOption[T] {
    override def toOption: Option[T] = Option(value)
  }

  case class DecodedResultNone[T]() extends DecodedResultOption[T] {
    override def toOption: Option[T] = None
  }
}
