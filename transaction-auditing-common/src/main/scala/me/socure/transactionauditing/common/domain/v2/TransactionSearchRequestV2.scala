package me.socure.transactionauditing.common.domain.v2

import me.socure.common.slick.domain.{Pagination, Sort}
import me.socure.constants.EnvironmentConstants.EnvironmentConstants
import me.socure.transactionauditing.common.product.Products.Product
import me.socure.transactionauditing.common.transaction.CustomFilterTypes.CustomFilterType
import me.socure.transactionauditing.common.transaction.TransactionColumns.TransactionColumn
import org.joda.time.DateTime

/**
 * Created by sum<PERSON><PERSON> on 21/01/22
 * */
case class TransactionSearchRequestV2(
                                       requestParams: RequestParams,
                                       responseParams: ResponseParams
                                     )

case class TransactionSearchRequestV2Dynamo(
                                             requestParams: RequestParamsDynamo,
                                             responseParams: ResponseParams
                                           )

case class RequestParams(
                          pagination: Option[Pagination],
                          sorting: Option[Seq[Sort[TransactionColumn]]],
                          filters: RequestFilters
                        )

case class RequestParamsDynamo(
                                pageSize: Option[Int],
                                exclusiveStartKey: Option[Map[String, String]],
                                filters: RequestFiltersDynamo
                              )

case class RequestFilters(
                          columns: ColumnFilters,
                          custom: Option[Map[String, CustomFilter]]
                         )

case class RequestFiltersDynamo(
                                 accountId: Option[Long],
                                 startDate: Option[DateTime],
                                 endDate: Option[DateTime],
                                 environment: Option[Set[EnvironmentConstants]],
                                 product: Option[Set[Product]],
                                 isError: Option[Boolean],
                                 isKyc: Option[Boolean],
                                 customerUserId: Option[String],
                                 runId: Option[String],
                                 hasRiskCode: Option[Boolean]
                               )

case class ColumnFilters(
                        accountId: Option[Long],
                        accountIds: Option[Set[Long]],
                        startDate: Option[DateTime],
                        endDate: Option[DateTime],
                        environment: Option[Set[EnvironmentConstants]],
                        product: Option[Set[Product]],
                        isError: Option[Boolean],
                        isKyc: Option[Boolean],
                        customerUserId: Option[String],
                        runId: Option[String],
                        startRecordId: Option[Long],
                        hasRiskCode: Option[Boolean]
                        )

case class ResponseOptions(
                  maskPii: Option[Boolean]
                  )

case class CustomFilter(
                         filterType: Option[CustomFilterType],
                         values: Set[String]
                       )

case class ResponseParams(
                         columns: Set[TransactionColumn],
                         options: Option[ResponseOptions]
                         )
