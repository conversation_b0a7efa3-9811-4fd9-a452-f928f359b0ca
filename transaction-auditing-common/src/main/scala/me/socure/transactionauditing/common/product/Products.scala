package me.socure.transactionauditing.common.product

import me.socure.transactionauditing.common.product.ProductNames._
import me.socure.transactionauditing.common.product.ProductVersions._

import scala.language.implicitConversions

object Products extends Enumeration {

  type Product = Value

  trait ScoringProductsVersions {
    val name: ProductName
    val V1: Product = create(name, ProductVersions.V1)
    val V2: Product = create(name, ProductVersions.V2)
    val V2_5: Product = create(name, ProductVersions.V2_5)
    val V2_5_1: Product = create(name, ProductVersions.V2_5_1)
    val V3_0: Product = create(name, ProductVersions.V3_0)

    val All = Set(
      V1,
      V2,
      V2_5,
      V2_5_1,
      V3_0
    )
  }

  object Watchlist {
    val V1: Product = create(ProductNames.Watchlist, ProductVersions.V1)

    val All = Set(
      V1
    )
  }

  val AuthScore = new {
    val name = ProductNames.AuthScore
  } with ScoringProductsVersions

  val EmailAuthScore = new {
    val name = ProductNames.EmailAuthScore
  } with ScoringProductsVersions

  object EncryptedEmailAuthScore {
    val V3: Product = create(ProductNames.EncryptedEmailAuthScore, ProductVersions.V3_0)
    val All = Set(
      V3
    )
  }
  
  def findByNameAndVersion(name: ProductName, version: ProductVersion): Option[Product] = {
    values.find { product =>
      product.toString == createName(
        name = name,
        version = version
      )
    }
  }

  private def createName(name: ProductName, version: ProductVersion): String = {
    s"/api/$version/$name"
  }

  private def create(name: ProductName, version: ProductVersion): Product = {
    Value(name = createName(name = name, version = version))
  }
}
