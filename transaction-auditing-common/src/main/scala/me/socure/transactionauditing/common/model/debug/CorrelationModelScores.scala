package me.socure.transactionauditing.common.model.debug

import me.socure.transactionauditing.common.domain.ModelScore


case class CorrelationModelScores(
                                   nameAddress: Option[ModelScore],
                                   nameEmail: Option[ModelScore],
                                   namePhone: Option[ModelScore]
                                 )

object CorrelationModelScores {
  val empty = CorrelationModelScores(None, None, None)
}