package me.socure.transactionauditing.common.status

import me.socure.types.scala.{ByMember, Enum}

object ProcessResults extends Enum with ByMember {
  type ProcessResult = EnumVal

  case class EnumVal(name: String) extends Value

  val SUCCESS = new ProcessResult("Success")
  val FAILURE = new ProcessResult("Failure")
  val TIMEOUT = new ProcessResult("Timed Out")
  val S3NOTFOUND = new ProcessResult("S3 Not Found")
}
