package me.socure.transactionauditing.common.factory

import com.amazonaws.auth.AWSCredentialsProviderChain
import com.amazonaws.services.s3.AmazonS3Client
import me.socure.s3.ExtendedSQSClientRegionFactory
import me.socure.transactionauditing.common.config.{ClientSqsEndpoint, ClientSqsEndpointConfig}
import me.socure.transactionauditing.common.util.SqsQueueEnums.SqsQueueEnum

object ClientSqsEndpointFactory {

  def get(s3Client: AmazonS3Client,  s3Bucket: String, endpointConfig: ClientSqsEndpointConfig, providerChain: AWSCredentialsProviderChain, queueType: SqsQueueEnum): ClientSqsEndpoint = {
    val amazonSQSExtendedClient = ExtendedSQSClientRegionFactory.get(
      s3Client = s3Client,
      s3BucketName = s3Bucket,
      amazonSQSClientBuilder = SQSClientBuilderFactory.get(providerChain),
      region = endpointConfig.region
    )
    new ClientSqsEndpoint(client = amazonSQSExtendedClient, endpointConfig = endpointConfig, queueType = queueType)
  }
}
