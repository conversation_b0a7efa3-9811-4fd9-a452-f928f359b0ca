package me.socure.transactionauditing.common.summary.watchlist

import me.socure.transactionauditing.common.summary.watchlist.WatchlistMonitoringOpsColumns.WatchlistMonitoringOpsColumn
import org.json4s.JsonAST.JNull
import org.json4s.{CustomSerializer, JString}

class WatchlistMonitoringOpsColumnsSerializer extends CustomSerializer[WatchlistMonitoringOpsColumn](format => ( {
  case JNull ⇒ null
  case jstr: JString ⇒ WatchlistMonitoringOpsColumns.byColumn(jstr.s) match {
    case Some(column) => column
    case None => throw new Exception("Invalid column name")
  }
}, {
  case column: WatchlistMonitoringOpsColumn => {
    JString(column.name)
  }
}
))

