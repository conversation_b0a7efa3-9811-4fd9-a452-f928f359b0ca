package me.socure.transactionauditing.common.domain

import org.joda.time.DateTime

case class WatchlistMonitoringOperation(
                                         id: Long,
                                         accountId: Long,
                                         transactionId: String,
                                         referenceId: String,
                                         operation: String,
                                         entityIds: String,
                                         environmentId: Long,
                                         updatedAt: DateTime
                                       )
