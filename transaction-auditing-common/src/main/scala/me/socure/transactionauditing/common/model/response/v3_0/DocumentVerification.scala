package me.socure.transactionauditing.common.model.response.v3_0

import me.socure.transactionauditing.common.model.ReasonCode

case class DocumentVerification(
                                 reasonCodes: Set[ReasonCode],
                                 documentType: String,
                                 documentVersion: Option[String],
                                 country: String,
                                 state: Option[String],
                                 decisionName: String,
                                 decisionValue: String,
                                 firstName: Option[String],
                                 surName: Option[String],
                                 fullName: Option[String],
                                 address: Option[String],
                                 documentNumber: Option[String],
                                 dob: Option[String],
                                 expirationDate: Option[String],
                                 issueDate: Option[String]
                               )