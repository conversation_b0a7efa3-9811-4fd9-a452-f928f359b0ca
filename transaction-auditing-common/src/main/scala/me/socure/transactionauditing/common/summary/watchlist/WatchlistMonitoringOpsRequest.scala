package me.socure.transactionauditing.common.summary.watchlist

import me.socure.common.slick.domain.{Pagination, Sort}
import me.socure.constants.EnvironmentConstants.EnvironmentConstants
import me.socure.transactionauditing.common.summary.watchlist.WatchlistMonitoringOpsColumns.WatchlistMonitoringOpsColumn
import org.joda.time.DateTime

case class WatchlistMonitoringOpsRequest(
                                          accountId: Option[Long],
                                          accountIds: Option[Set[Long]],
                                          start: Option[DateTime],
                                          end: Option[DateTime],
                                          environment: Option[Set[EnvironmentConstants]],
                                          operation: Option[String],
                                          pagination: Option[Pagination],
                                          columns: Set[WatchlistMonitoringOpsColumn],
                                          sorting: Option[Seq[Sort[WatchlistMonitoringOpsColumn]]]
                                        ) {
  def next: WatchlistMonitoringOpsRequest = {
    pagination match {
      case Some(paging) =>
        copy(
          pagination = Some(
            paging.next
          )
        )
      case None => throw new IllegalStateException("Cannot call next without defined pagination")
    }
  }
}
