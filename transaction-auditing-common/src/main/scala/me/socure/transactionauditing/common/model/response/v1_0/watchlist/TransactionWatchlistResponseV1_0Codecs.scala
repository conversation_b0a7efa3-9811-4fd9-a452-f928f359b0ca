package me.socure.transactionauditing.common.model.response.v1_0.watchlist

import argonaut.DecodeJson
import me.socure.transactionauditing.common.model.CommonCodecs._
import me.socure.transactionauditing.common.model.ReasonCode

object TransactionWatchlistResponseV1_0Codecs {

  implicit val watchlistV1_0Decoder: <PERSON><PERSON><PERSON><PERSON>[WatchlistResponseV1_0] = Decode<PERSON>son { h =>
    for {
      matches <- (h --\ "details").as[Map[String, WatchlistMatch1_0]]
    } yield WatchlistResponseV1_0(
      matches = matches
    )
  }

  implicit val watchlistMatchV1_0Decoder: DecodeJson[WatchlistMatch1_0] = DecodeJson { h =>
    for {
      matchScore <- (h --\ "matchscore").as[String]
      matchFields <- (h --\ "matchfields").as[List[String]]
      sourceUrls <- (h --\ "sourceUrls").as[List[String]]
    } yield WatchlistMatch1_0(
      score = matchScore.toInt,
      fields = matchFields,
      sourceUrls = sourceUrls
    )
  }


  implicit val transactionResponseV1_0Decoder: Decode<PERSON>son[TransactionWatchlistResponseV1_0] = DecodeJson { h =>
    for {
      status <- (h --\ "status").as[String]
      referenceId <- ((h --\ "data")  --\ "referenceid").as[String]
      reasonCodes <- ((h --\ "data") --\ "reasoncodes").as[List[ReasonCode]]
      watchlistResponse <- (h --\ "data").as[WatchlistResponseV1_0]
      msg <- (h --\ "msg").as[Option[String]]
    } yield TransactionWatchlistResponseV1_0(
      status = status,
      referenceId = referenceId,
      reasonCodes = reasonCodes,
      matches = watchlistResponse,
      msg = if(msg.getOrElse("").length > 0) msg else None
    )
  }
}
