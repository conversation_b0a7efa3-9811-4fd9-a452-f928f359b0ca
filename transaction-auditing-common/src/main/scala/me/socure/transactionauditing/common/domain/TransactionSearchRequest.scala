package me.socure.transactionauditing.common.domain

import me.socure.common.slick.domain.{Pagination, Sort}
import me.socure.constants.EnvironmentConstants.EnvironmentConstants
import me.socure.transactionauditing.common.product.Products.Product
import me.socure.transactionauditing.common.transaction.TransactionColumns.TransactionColumn
import org.joda.time.DateTime

case class TransactionSearchRequest(
                                     accountId: Option[Long],
                                     accountIds: Option[Set[Long]],
                                     start: Option[DateTime],
                                     end: Option[DateTime],
                                     environment: Option[Set[EnvironmentConstants]],
                                     pagination: Option[Pagination],
                                     product: Option[Set[Product]],
                                     isError: Option[Boolean],
                                     isKyc: Option[Boolean],
                                     customerUserId: Option[String],
                                     runId: Option[String],
                                     columns: Set[TransactionColumn],
                                     startTransactionId: Option[Long],
                                     sorting: Option[Seq[Sort[TransactionColumn]]],
                                     maskPii: Boolean
                                   ) {
  def next: TransactionSearchRequest = {
    pagination match {
      case Some(paging) =>
        copy(
          pagination = Some(
            paging.next
          )
        )
      case None => throw new IllegalStateException("Cannot call next without defined pagination")
    }
  }
}

case class TransactionSearchRequestDynamo (
                                            accountId: Option[Long],
                                            start: Option[DateTime],
                                            end: Option[DateTime],
                                            environment: Option[Set[EnvironmentConstants]],
                                            pagination: Option[Pagination],
                                            product: Option[Set[Product]],
                                            isError: Option[Boolean],
                                            isKyc: Option[Boolean],
                                            customerUserId: Option[String],
                                            runId: Option[String],
                                            hasRiskCode: Option[Boolean],
                                            columns: Set[TransactionColumn],
                                            maskPii: Boolean,
                                            pageSize: Option[Int] = None,
                                            exclusiveStartKey: Option[Map[String, String]] = None
                                          )