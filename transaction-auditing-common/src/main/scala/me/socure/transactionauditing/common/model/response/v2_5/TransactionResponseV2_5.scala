package me.socure.transactionauditing.common.model.response.v2_5

import me.socure.transactionauditing.common.model.ReasonCode
import me.socure.transactionauditing.common.model.debug.Debug
import me.socure.transactionauditing.common.model.response.{FraudModelScore, TransactionResponse}

case class TransactionResponseV2_5(
                                    status: String,
                                    msg: Option[String],
                                    referenceId: String,
                                    authScore: Int,
                                    reasonCodes: List[ReasonCode],
                                    confidence: Double,
                                    fraudScore: Double,
                                    customModels: Option[List[FraudModelScore]],
                                    details: DetailResponse,
                                    debug: Option[Debug]
                       ) extends TransactionResponse


