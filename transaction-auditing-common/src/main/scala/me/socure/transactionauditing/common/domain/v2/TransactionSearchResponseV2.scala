package me.socure.transactionauditing.common.domain.v2

import me.socure.common.slick.domain.Pagination
import me.socure.transactionauditstorage.mysqlschema.ApiTransaction

case class TransactionSearchResponseV2(
                                        pagination: Option[Pagination],
                                        records: Records
                                      )

case class Records(
                  count: Int,
                  transactions: Seq[ApiTransaction]
                  )
