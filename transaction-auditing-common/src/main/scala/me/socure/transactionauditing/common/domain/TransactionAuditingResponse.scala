package me.socure.transactionauditing.common.domain

import me.socure.transactionauditing.common.domain.v2.TransactionSearchResponseV2
import me.socure.transactionauditstorage.mysqlschema.{ApiTransaction => SlickApiTransaction}

sealed trait TransactionAuditingResponse

case class SingleTransactionResponse(
                                      status: String,
                                      data: SlickApiTransaction
                                      ) extends TransactionAuditingResponse

case class MultiTransactionResponse(
                                      status: String,
                                      data: Seq[SlickApiTransaction]
                                    ) extends TransactionAuditingResponse

case class MultiTransactionResponseDynamo(
                                           status: String,
                                           data: TransactionSearchResponseData
                                         ) extends TransactionAuditingResponse

case class MultiTransactionResponseV2(
                                     status: String,
                                     data: TransactionSearchResponseV2
                                     )

case class TransactionSearchResponseData(
                                          transactions: Seq[SlickApiTransaction],
                                          lastEvaluatedKey: Option[Map[String, String]] = None
                                        )