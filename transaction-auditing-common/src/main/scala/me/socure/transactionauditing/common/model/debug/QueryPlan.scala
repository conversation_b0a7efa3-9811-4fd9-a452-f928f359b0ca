package me.socure.transactionauditing.common.model.debug


import me.socure.service.constants.IConstants.Component

import scala.concurrent.duration.FiniteDuration

case class QueryPlan(
                      vendors: Set[Component],
                      schedulerTimeout: FiniteDuration,
                      vendorTimeouts: VendorTimeoutWrapper,
                      idPlusFeatures: DebugIdPlusFeatures
                    )