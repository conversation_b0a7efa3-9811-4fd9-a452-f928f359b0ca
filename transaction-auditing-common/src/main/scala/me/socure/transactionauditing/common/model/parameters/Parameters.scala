package me.socure.transactionauditing.common.model.parameters

case class Parameters(
                       pii: PII = PII.empty,
                       impersonatorApiKey: Option[String] = None,
                       isBlacklist: Option[Boolean] = None,
                       ofac: Option[Boolean] = None,
                       nocache: Option[Int] = None,
                       details: Option[Boolean] = None,
                       runId: Option[String] = None,
                       debug: Option[Boolean] = None,
                       datascience: Option[Boolean] = None,
                       kyc: Option[Boolean] = None,
                       forceRefresh: Option[Boolean] = None,
                       showCustommodelsMapped: Option[Boolean] = None,
                       socureKey: Option[String] = None,
                       watchlist: Option[String] = None
                     )

object Parameters {
  val empty = Parameters()
}