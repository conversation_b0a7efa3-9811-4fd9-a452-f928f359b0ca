package me.socure.transactionauditing.common.model.response.v3_0

import me.socure.transactionauditing.common.model.ReasonCode

case class WatchlistResponseV3_0(reasonCodes: Set[ReasonCode], matches: Map[String, List[WatchlistMatchV3_0]])

case class WatchlistMatchV3_0(
                            score: Int,
                            fields: List[String],
                            sourceUrls: List[String],
                            comments: Option[String],
                            offense: Option[String]
                         )