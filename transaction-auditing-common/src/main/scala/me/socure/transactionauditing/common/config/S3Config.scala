package me.socure.transactionauditing.common.config

case class S3Config(
                     region: String,
                     thirdPartyBucket: Option[String],
                     largeFileBucket: String,
                     maskingErrorBucket: Option[String],
                     parsingErrorBucket: Option[String] = None,
                     auditDataBucket: Option[String]
                   )