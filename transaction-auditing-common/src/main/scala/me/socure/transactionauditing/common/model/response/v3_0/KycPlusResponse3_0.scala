package me.socure.transactionauditing.common.model.response.v3_0

import me.socure.transactionauditing.common.model.ReasonCode
import me.socure.transactionauditing.common.model.response.{KycCorrelationIndices, KycDecision}

/**
 * Created by Vignesh V on Sep 14, 2022
 */
case class KycPlusResponse3_0(
                           reasonCodes: Set[ReasonCode],
                           cvi: Option[Int],
                           correlationIndices: Option[KycCorrelationIndices],
                           decision: Option[KycDecision],
                           fieldValidations: Map[String, Double],
                           bestMatchedEntity: Option[KycBestMatchedEntity]
                         )
