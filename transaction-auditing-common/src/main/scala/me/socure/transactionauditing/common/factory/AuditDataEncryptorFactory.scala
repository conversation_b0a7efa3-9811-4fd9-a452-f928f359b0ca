package me.socure.transactionauditing.common.factory

import java.net.InetSocketAddress

import com.amazonaws.encryptionsdk.CryptoMaterialsManager
import com.typesafe.config.Config
import me.socure.account.client.encryption.{CachedEncryptionKeysClient, EncryptionKeysClientFactory}
import me.socure.crypto.key.common.{CryptoMaterialsManagerAccountsService, SocureCryptoMaterialsManager}
import me.socure.transaction.auditing.encryption.encryptor.{AuditDataEncryptor, EncryptorFactory}
import net.spy.memcached.MemcachedClient

import scala.concurrent.ExecutionContext

object AuditDataEncryptorFactory {

  def create(config: Config,
             cryptoMaterialsManager: CryptoMaterialsManager,
             cryptoMaterialsManagerAccountsService: CryptoMaterialsManagerAccountsService)(
  implicit ec: ExecutionContext): AuditDataEncryptor = {
    val accountEndpoint = config.getConfig("account.service").getString("endpoint")
    val memcachedClient = new MemcachedClient(new InetSocketAddress(config.getConfig("memcached").getString("host"), config.getConfig("memcached").getInt("port")))

    val encryptionKeysClient: CachedEncryptionKeysClient = EncryptionKeysClientFactory.createCached(
      accountServiceEndpoint = accountEndpoint,
      memcachedClient = memcachedClient,
      ttl = None
    )
    val cseConfig = config.getConfig("client.specific.encryption")

    val encryptor = EncryptorFactory.create(
      cryptoMaterialsManagerAccountsService = cryptoMaterialsManagerAccountsService,
      cryptoMaterialsManager = cryptoMaterialsManager,
      encryptionContextAccountIdKey = cseConfig.getString("encryption.context.account_id.key")
    )

    new AuditDataEncryptor(
      encryptionKeysClient = encryptionKeysClient,
      encryptor = encryptor
    )
  }
}
