package me.socure.transactionauditing.common.model.response.v1_0.watchlist

import me.socure.transactionauditing.common.model.response.TransactionResponse

case class WatchlistResponseV1_0(matches: Map[String, WatchlistMatch1_0]) extends TransactionResponse

case class WatchlistMatch1_0(
                              score: Int,
                              fields: List[String],
                              sourceUrls: List[String]
                            )