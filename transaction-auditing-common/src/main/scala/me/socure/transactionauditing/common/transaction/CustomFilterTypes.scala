package me.socure.transactionauditing.common.transaction

import me.socure.types.scala.{ByM<PERSON>ber, Enum}

object CustomFilterTypes extends Enum with ByMember{
  type CustomFilterType  = EnumVal

  case class EnumVal(name: String) extends Value

  val All = new CustomFilterType("All")
  val Any = new CustomFilterType("Any")

  def byFilterType(filterType: String): Option[CustomFilterType] = {
    values.find(_.name == filterType)
  }

}
