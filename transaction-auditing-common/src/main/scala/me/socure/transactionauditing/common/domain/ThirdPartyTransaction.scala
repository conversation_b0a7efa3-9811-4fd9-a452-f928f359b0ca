package me.socure.transactionauditing.common.domain

import org.joda.time.DateTime

case class ThirdPartyTransaction(
                                  id: Long,
                                  accountId: Option[Long],
                                  isCache: Option[Boolean],
                                  processingTime: Option[Long],
                                  serviceId: Option[Int],
                                  startTime: Option[DateTime],
                                  transactionId: String,
                                  request: Option[String],
                                  response: Option[String],
                                  request_body: Option[String],
                                  uuid: Option[String],
                                  isError: Option[Boolean]
                                )
