package me.socure.transactionauditing.common.model.parameters

case class PII(
                country: Option[String] = None,
                ipAddress: Option[String] = None,
                firstName: Option[String] = None,
                mobileNumber: Option[String] = None,
                city: Option[String] = None,
                userId: Option[String] = None,
                nationalId: Option[String] = None,
                physicalAddress2: Option[String] = None,
                surname: Option[String] = None,
                state: Option[String] = None,
                customerUserId: Option[String] = None,
                email: Option[String] = None,
                zip: Option[String] = None,
                physicalAddress: Option[String] = None,
                driverLicenseState: Option[String] = None,
                companyName: Option[String] = None,
                dob: Option[String] = None,
                geocode: Option[String] = None,
                fullName: Option[String] = None,
                driverLicense: Option[String] = None
              )

object PII {
  val empty = PII()
}