package me.socure.transactionauditing.common.model.debug

import me.socure.transactionauditing.common.model.ReasonCode


case class Debug(
                  fraudModelScores: Option[FraudModelScores],
                  correlationModelScores: CorrelationModelScores,
                  piiRiskModelScores: PiiRiskModelScores,
                  ruleCodes: Option[List[RuleCode]],
                  reasonCodes: Option[List[ReasonCode]],
                  httpStatus: Option[Int],
                  queryPlan: Option[QueryPlan]
                )