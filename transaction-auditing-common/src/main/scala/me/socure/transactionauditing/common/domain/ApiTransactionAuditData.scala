package me.socure.transactionauditing.common.domain

case class ApiTransactionAuditData(
                                    accountId: Long,
                                    transactionId: String,
                                    errorMsg: Option[String],
                                    parameters: String,
                                    response: String,
                                    debug: String,
                                    details: String,
                                    reasonCodes: String,
                                    requestURI: String,
                                    internalWorkLogs: Option[String],
                                    parentTxnId: Option[String]
                                  )