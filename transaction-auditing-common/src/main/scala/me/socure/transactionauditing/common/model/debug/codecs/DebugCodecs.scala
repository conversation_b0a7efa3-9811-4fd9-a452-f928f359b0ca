package me.socure.transactionauditing.common.model.debug.codecs

import argonaut.Argonaut._
import argonaut._
import me.socure.model.idplus.v3_0.IdPlusFeatures
import me.socure.service.constants.IConstants._
import me.socure.transactionauditing.common.domain.ModelScore
import me.socure.transactionauditing.common.model.CodecHelpers.{DecodedResultNone, DecodedResultOption, _}
import me.socure.transactionauditing.common.model.CommonCodecs._
import me.socure.transactionauditing.common.model.ReasonCode
import me.socure.transactionauditing.common.model.debug.{Model, _}

import scala.concurrent.duration.FiniteDuration


object DebugCodecs {

  implicit val modelDecoder: DecodeJson[Model] = DecodeJson { h =>
    for {
      name <- (h --\ "name").as[String]
      version <- (h --\ "version").as[String]
      identifier <- (h --\ "identifier").as[String]
    } yield Model(
      name = name,
      version = version,
      identifier = identifier
    )
  }

  implicit val modelScoreDecoder: DecodeJson[ModelScore] = DecodeJson { h =>
    for {
      model <- (h --\ "model").as[Model]
      score <- (h --\ "score").as[Double]
    } yield ModelScore(
      model = model,
      score = score
    )
  }

  implicit val fraudModelScoresDecoder: DecodeJson[FraudModelScores] = DecodeJson { h =>
    for {
      primary <- (h --\ "primary").as[DecodedResultOption[ModelScore]].map(_.toOption) ||| (h --\ "primary").as[Option[ModelScore]]
      sigma <- (h --\ "sigma").as[DecodedResultOption[ModelScore]].map(_.toOption) ||| (h --\ "sigma").as[Option[ModelScore]]
      secondary <- (h --\ "custom").as[Map[String, ModelScore]]
    } yield {
      FraudModelScores(
        primary = primary,
        sigma = sigma,
        secondary = secondary.values.toSet
      )
    }
  }

  implicit val correlationModelScoresDecoder: DecodeJson[CorrelationModelScores] = DecodeJson { h =>
    for {
      nameAddress <- (h --\ "name_address").as[DecodedResultOption[ModelScore]].map(_.toOption) ||| (h --\ "nameAddress").as[DecodedResultOption[ModelScore]].map(_.toOption)
      nameEmail <- (h --\ "name_email").as[DecodedResultOption[ModelScore]].map(_.toOption) ||| (h --\ "nameEmail").as[DecodedResultOption[ModelScore]].map(_.toOption)
      namePhone <- (h --\ "name_phone").as[DecodedResultOption[ModelScore]].map(_.toOption) ||| (h --\ "namePhone").as[DecodedResultOption[ModelScore]].map(_.toOption)
    } yield {
      CorrelationModelScores(
        nameAddress = nameAddress,
        nameEmail = nameEmail,
        namePhone = namePhone
      )
    }
  }

  implicit val piiRiskModelScoresDecoder: DecodeJson[PiiRiskModelScores] = DecodeJson { h =>
    for {
      address <- (h --\ "address").as[Option[DecodedResultOption[ModelScore]]].map(_.flatMap(_.toOption))
      email <- (h --\ "email").as[Option[DecodedResultOption[ModelScore]]].map(_.flatMap(_.toOption))
      phone <- (h --\ "phone").as[Option[DecodedResultOption[ModelScore]]].map(_.flatMap(_.toOption))
    } yield {
      PiiRiskModelScores(
        address = address,
        email = email,
        phone = phone
      )
    }
  }

  implicit val debugDecoder: DecodeJson[Debug] = DecodeJson { h =>
    for {
      hasFraudModelScores <- (h =\ 0).hasField("fraud_score_info")
      hasLegacyFraudModelScores <- (h =\ 0).hasField("fraudScoreInfo")
      fraudModelScores <-  {
        if(hasFraudModelScores)
          ((h =\ 0) --\ "fraud_score_info").as[DecodedResultOption[FraudModelScores]]
        else if(hasLegacyFraudModelScores)
          ((h =\ 0) --\ "fraudScoreInfo").as[DecodedResultOption[FraudModelScores]]
        else
          DecodeResult.ok(DecodedResultNone(): DecodedResultOption[FraudModelScores])
      }

      hasCorrelationScoresSnakeCase <- (h =\ 0).hasField("correlation_score_info")
      hasCorrelationScoresCamelCase <- (h =\ 0).hasField("correlationScoreInfo")
      correlationModelScores <- {
        //The following is to ensure that we treat missing data and invalid data as expected
        if (hasCorrelationScoresSnakeCase)
          ((h =\ 0) --\ "correlation_score_info").as[DecodedResultOption[CorrelationModelScores]]
        else if (hasCorrelationScoresCamelCase)
          ((h =\ 0) --\ "correlationScoreInfo").as[DecodedResultOption[CorrelationModelScores]]
        else
          DecodeResult.ok(DecodedResultNone(): DecodedResultOption[CorrelationModelScores])
      }

      hasPiiRiskScoresSnakeCase <- (h =\ 0).hasField("risk_score_info")
      hasPiiRiskScoresCamelCase <- (h =\ 0).hasField("riskScoreInfo")
      piiRiskModelScores <- {
        //The following is to ensure that we treat missing data and invalid data as expected
        if (hasPiiRiskScoresSnakeCase)
          ((h =\ 0) --\ "risk_score_info").as[DecodedResultOption[PiiRiskModelScores]]
        else if (hasPiiRiskScoresCamelCase)
          ((h =\ 0) --\ "riskScoreInfo").as[DecodedResultOption[PiiRiskModelScores]]
        else
          DecodeResult.ok(DecodedResultNone(): DecodedResultOption[PiiRiskModelScores])
      }

      reasonCodes <- (h =\ 0).getOrElseNone[List[ReasonCode]]("reason_codes")
      queryPlan <- (h =\ 0).getOrElseNone[QueryPlan]("query_plan")
      httpStatus <- ((h =\ 0) --\ "http_status").as[Option[Int]]

      hasLegacyRuleCodes <- (h =\ 0 ).hasField("ruleCodes") //ruleCodes
      hasRuleCodes <-  (h =\ 0 ).hasField("rule_codes") //ruleCodes
      ruleCodes <- {
        if(hasRuleCodes)
          ((h =\ 0) --\ "rule_codes").as[DecodedResultOption[List[RuleCode]]]
        else if(hasLegacyRuleCodes)
          ((h =\ 0) --\ "ruleCodes").as[DecodedResultOption[List[RuleCode]]]
        else
          DecodeResult.ok(DecodedResultNone(): DecodedResultOption[List[RuleCode]])
      }
    } yield Debug(
      fraudModelScores = fraudModelScores.toOption,
      correlationModelScores = correlationModelScores.toOption.getOrElse(CorrelationModelScores.empty),
      piiRiskModelScores = piiRiskModelScores.toOption.getOrElse(PiiRiskModelScores.empty),
      ruleCodes.toOption,
      reasonCodes,
      httpStatus,
      queryPlan = queryPlan
    )
  }

  implicit val ruleCodeDecoder: DecodeJson[RuleCode] = DecodeJson { h =>
    for {
      ruleCode <- (h --\ "rulecode").as[String]
      score <- (h --\ "originalScore").as[String]
    } yield {
      RuleCode(
        ruleCode =ruleCode,
        score = score.toDouble
      )
    }
  }

  implicit val queryPlanDecoder: DecodeJson[QueryPlan] = DecodeJson { h =>
    for {
      vendors <- (h --\ "vendors").as[Set[Component]]
      features <- (h --\ "id_plus_features").as[DebugIdPlusFeatures]
      schedulerTimeout <- (h --\ "scheduler_timeout").as[FiniteDuration]
      vendorTimeouts <- (h --\ "vendor_timeouts").as[VendorTimeoutWrapper]
    } yield {
      QueryPlan(
        vendors,
        schedulerTimeout,
        vendorTimeouts,
        features
      )
    }
  }

  implicit val vendorTimeoutWrapperDecoder: DecodeJson[VendorTimeoutWrapper] = DecodeJson { h =>
    val vendorsOpt: Option[Set[_root_.argonaut.Json.JsonField]] = h.fieldSet
    vendorsOpt match {
      case Some(vendors) => {
        val vendorTimeouts: Set[Option[VendorTimeout]] = for {
          vendor <- vendors
        } yield (h --\ vendor).as[FiniteDuration].value match {
          case Some(duration) => {
            Some(VendorTimeout(Component.getByScoreKey(vendor), duration))
          }
          case None => None
        }
        DecodeResult.ok(VendorTimeoutWrapper(vendorTimeouts.flatten))
      }
      case None =>
        DecodeResult.ok(VendorTimeoutWrapper(Set.empty))
    }
  }

  implicit val componentDecoder: DecodeJson[Component] = DecodeJson { h =>
    for {
      component <- h.as[String]
    } yield {
      Component.getByScoreKey(component)
    }
  }

  implicit val finiteDurationDecoder: DecodeJson[FiniteDuration] = DecodeJson { h =>
    for {
      timeoutLength <- (h --\ "length").as[Long]
      unit <- (h --\ "unit").as[String]
    } yield {
      FiniteDuration.apply(timeoutLength, "ms")
    }
  }


  implicit val idplusFeaturesDecoder: DecodeJson[DebugIdPlusFeatures] = DecodeJson { h =>
    for {
      requestedFeatures <- (h --\ "requested_features").as[Set[String]]
      resolvedFeatures <- (h --\ "resolved_features").as[Set[String]]
      outputtedFeatures <- (h --\ "outputted_features").as[Set[String]]
    } yield {
      DebugIdPlusFeatures(
        requestedFeatures.flatMap(IdPlusFeatures.byName),
        resolvedFeatures.flatMap(IdPlusFeatures.byName),
        outputtedFeatures.flatMap(IdPlusFeatures.byName)
      )
    }
  }

  implicit val categoricalRuleCodeDecoder: DecodeJson[CategoricalRuleCode] = DecodeJson { h =>
    for {
      ruleCode <- (h --\ "rulecode").as[String]
      score <- (h --\ "value").as[String]
    } yield {
      CategoricalRuleCode(
        ruleCode =ruleCode,
        value = score
      )
    }
  }
}
