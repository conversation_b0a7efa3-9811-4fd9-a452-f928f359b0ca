package me.socure.transactionauditing.common.config

import com.typesafe.config.ConfigFactory
import org.scalatest.{FunSuite, Matchers}

class S3ConfigFactoryTest extends FunSuite with Matchers {

  test("load up old config values properly") {
    val config = ConfigFactory.load("test-client.conf")
    val s3Config = S3ConfigFactory.get(config)

    s3Config.region shouldBe "us-east-1"
    s3Config.thirdPartyBucket shouldBe Some("thirdparty-stats-stage")
    s3Config.largeFileBucket shouldBe "sqs-storage-local"
    s3Config.maskingErrorBucket shouldBe Some("stage-audit-errors/pii_mask_failures")
  }

  test("load up new config values properly") {
    val config = ConfigFactory.load("writer-test.conf")
    val s3Config = S3ConfigFactory.get(config, pipelineBasedConfig = true)

    s3Config.region shouldBe "us-east-1"
    s3Config.thirdPartyBucket shouldBe Some("thirdparty-stats-stage")
    s3Config.largeFileBucket shouldBe "sqs-storage-stage"
    s3Config.maskingErrorBucket shouldBe Some("stage-audit-errors/pii_mask_failures")
  }
}
