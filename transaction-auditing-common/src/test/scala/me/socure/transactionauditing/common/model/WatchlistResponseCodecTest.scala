package me.socure.transactionauditing.common.model

import argonaut.Parse
import me.socure.transactionauditing.common.model.response.v3_0.TransactionResponseCodecsV3_0._
import me.socure.transactionauditing.common.model.response.v3_0.{TransactionResponseV3_0, WatchlistMatchV3_0}
import org.scalatest.{FunSuite, Matchers}

import scala.io.Source
import scalaz.{-\/, \/-}

class WatchlistResponseCodecTest extends FunSuite with Matchers {
  val watchlistResponse3_0: String = Source.fromURL(getClass.getResource("/sample_response_watchlist_3_0.json")).mkString
  val watchlistResponse3_0WithUrl: String = Source.fromURL(getClass.getResource("/sample_response_watchlist_3_0_urls.json")).mkString

  test("parse v3 watchlist json") {
    val responseOpt = Option(watchlistResponse3_0).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[TransactionResponseV3_0](_) match {
      case \/-(debug) => Some(debug)
      case -\/(err) =>
        throw new Exception(err)
    })

    val watchlist = responseOpt.get.watchlist.get

    watchlist.reasonCodes should contain (ReasonCode("R186"))
    watchlist.matches should contain allOf (
      "EU CONSOLIDATED LIST.BDF" -> List(
        WatchlistMatchV3_0(
          score = 100,
          fields = List("name", "dob"),
          sourceUrls = List.empty,
          comments = Some("Legal Basis: 2015/2245 (OJ L318); 674/2006 (OJ L116) | Reg Date: 2015-12-05; 2006-04-29 | Additional Information: (Date of UN designation: 2006-04-21)"),
          offense = None
        )
      ),
      "OSFI CONSOLIDATED LIST.BDF" -> List(
        WatchlistMatchV3_0(
          score = 100,
          fields = List("name", "dob"),
          sourceUrls = List.empty,
          comments = Some("Other information: \nFormed Jemmah Anshorut Tauhid (JAT) in 2008.  In 2010, arrested for incitement to commit terrorism and fundraising with respect to a training camp in Aceh, Indonesia and sentenced to 15 years in 2011.\n\nUNSC (April 21, 2006); UNSC (October 14, 2015)"),
          offense = None
        )
      ),
      "EPLS.BDF" -> List(
        WatchlistMatchV3_0(
          score = 100,
          fields = List("name"),
          sourceUrls = List.empty,
          comments = Some("Cross-Reference: ABU BAKAR BA'ASYIR | ABU BAKAR BASHIR"),
          offense = None
        ),
        WatchlistMatchV3_0(
          score = 100,
          fields = List("name"),
          sourceUrls = List.empty,
          comments = Some("Cross-Reference: ABU BAKAR BAASYIR | ABU BAKAR BASHIR"),
          offense = None
        )
      ),
      "OFAC SDN.BDF" -> List(
        WatchlistMatchV3_0(
          score = 100,
          fields = List("name", "dob"),
          sourceUrls = List.empty,
          comments = Some("Program: SDGT"),
          offense = None
        )
      ),
      "UN CONSOLIDATED LIST.BDF" -> List(
        WatchlistMatchV3_0(
          score = 100,
          fields = List("name", "dob"),
          sourceUrls = List.empty,
          comments = Some("Formed Jemmah Anshorut Tauhid (JAT) (QDe.133) in 2008.  In 2010, arrested for incitement to commit terrorism and fundraising with respect to a training camp in Aceh, Indonesia and sentenced to 15 years in 2011.  Review pursuant to Security Council resolution 1822 (2008) was concluded on 8 Jun. 2010.\n\nListed on: 21 Apr. 2006 (amended on 14 Oct. 2015)"),
          offense = None
        )
      ),
      "BANK OF ENGLAND CONSOLIDATED LIST.BDF" -> List(
        WatchlistMatchV3_0(
          score = 100,
          fields = List("name", "dob"),
          sourceUrls = List.empty,
          comments = Some("Regime: The ISIL (Da'esh) and Al-Qaida organisations | Other Information: UN Ref QI.B.217.06 | Last Updated: 12/07/2015"),
          offense = None
        )
      )
    )
  }

  test("parse v3 watchlist json with url and offense") {
    val responseOpt = Option(watchlistResponse3_0WithUrl).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[TransactionResponseV3_0](_) match {
      case \/-(debug) => Some(debug)
      case -\/(err) =>
        throw new Exception(err)
    })

    val watchlist = responseOpt.get.watchlist.get

    watchlist.reasonCodes should contain (ReasonCode("R186"))

    watchlist.matches should contain (
      "Pakistan,PK-Punjab Police (Pakistan)" -> List(
        WatchlistMatchV3_0(
          score = 94,
          fields = List("name"),
          sourceUrls = List("http://www.dawn.com/news/1205389", "http://punjabpolice.gov.pk/070915-news-ii"),
          comments = Some("Source: Pakistan,PK-Punjab Police (Pakistan)\nOffense: Arrested for burglary - 2015\nCategory: Enforcement | Subcategory: Burglary\nAssociations: | Associate (Enforcement:Burglary): Rasool, Ghulam, 7600917\nLast updated: 2016-10-14\nProfile Notes: According to the PK-Punjab Police (Pakistan); September 07, 2015: In 2015, Abu Bakar alias Gunga was arrested along with Qasim alias Niku, Irfan alias Chita, Babar alias Babri, Fiaz alias Baga, Waqas alias Wiqi, Ghulam Rasool alias Sula, Ibrar alias Khidu, Javed alias Bodhi, Zeeshan alias Langri for burglary. Police recovered stolen and snatched items from their possession, including cash, cars, motorcycles, auto rickshaws, mobile phones, gold jewellery and other valuables worth PKR 22,400,000 ,with a large cache of illegal weapons."),
          offense = Some("Arrested for burglary - 2015")
        ),
        WatchlistMatchV3_0(
          score = 94,
          fields = List("name"),
          sourceUrls = List("http://nation.com.pk/lahore/01-Feb-2013/nine-member-gang-busted",
            "http://www.dawn.com/news/782783/cia-claims-nine-gangsters-arrested",
            "http://punjabpolice.gov.pk/01022013-News"),
          comments = Some( "Source: Pakistan,PK-Punjab Police (Pakistan)\nOffense: Arrested for burglary - February, 2013\nCategory: Enforcement | Subcategory: Burglary\nAssociations: | Associate (Enforcement:Burglary): Ali, Imran, 7608019 | Associate (Enforcement:Burglary): Ali, Malik, 7608065 | Associate (Enforcement:Burglary): Amjad, Malik, 7608071 | Associate (Enforcement:Burglary): Hasan, Tauqeedul, 7608009 | Associate (Enforcement:Burglary): Nazar, Mudassar, 7608027\nLast updated: 2016-10-18\nProfile Notes: According to the PK-Punjab Police (Pakistan); February 01, 2013: In February, 2013, Police arrested nine members of an inter-district Khada dacoit gang of criminals involved in a number of cases of dacoities and robberies. The arrested members of the gang were identified as Jehangir alias Khada, Tauqeedul Hasan, Kashif, Nadeem, Sarwar, Imran Ali, Shabbir, Mudassar Nazar and Abu Bakar. During interrogation, the arrested criminals confessed to more than 40 incidents of dacoities and robberies. Police recovered two stolen motorcycles, PKR 575,000 in cash and firearms from their possession."),
          offense = Some("Arrested for burglary - February, 2013")
        )
      )
    )
  }
}
