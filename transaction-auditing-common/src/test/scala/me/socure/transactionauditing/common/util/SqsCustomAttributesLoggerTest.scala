package me.socure.transactionauditing.common.util

import com.amazonaws.services.sqs.model.{MessageAttributeValue, SendMessageRequest}
import org.scalatest.{FunSuite, Matchers}

class SqsCustomAttributesLoggerTest extends FunSuite with Matchers {
  val transactionAttributeValue = "test-trx-value"
  val serviceId = "1"
  val request = new SendMessageRequest()

  val transactionAttribute = new MessageAttributeValue()
  transactionAttribute.setDataType("String")
  transactionAttribute.setStringValue(transactionAttributeValue)

  val serviceIdAttribute = new MessageAttributeValue()
  serviceIdAttribute.setDataType("String")
  serviceIdAttribute.setStringValue(serviceId)

  request
    .addMessageAttributesEntry(CustomMessageAttributes.TRANSACTION_ID.name, transactionAttribute)
    .addMessageAttributesEntry(CustomMessageAttributes.SERVICE_ID.name, serviceIdAttribute)

  test("extract message attributes") {
    val transactionAttribute = SqsCustomAttributesLogger.getTransactionAttribute(request.getMessageAttributes)
    transactionAttribute shouldBe Some(transactionAttributeValue)

    val serviceIdAttribute = SqsCustomAttributesLogger.getServiceIdAttribute(request.getMessageAttributes)
    serviceIdAttribute shouldBe Some(serviceId)
  }

  test("should not throw an exception if attributes are missing") {
    val anotherRequest = new SendMessageRequest()
    val transactionAttribute = SqsCustomAttributesLogger.getTransactionAttribute(anotherRequest.getMessageAttributes)
    transactionAttribute shouldBe None

    val serviceIdAttribute = SqsCustomAttributesLogger.getServiceIdAttribute(anotherRequest.getMessageAttributes)
    serviceIdAttribute shouldBe None
  }
}
