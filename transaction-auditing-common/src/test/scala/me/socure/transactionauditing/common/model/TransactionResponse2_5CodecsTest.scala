package me.socure.transactionauditing.common.model

import argonaut.Parse
import me.socure.model.idplus.v3_0.IdPlusFeatures
import me.socure.service.constants.IConstants
import me.socure.transactionauditing.common.domain.ModelScore
import me.socure.transactionauditing.common.model.debug._
import me.socure.transactionauditing.common.model.response.{FraudModelScore, KycCorrelationIndices, KycDecision}
import me.socure.transactionauditing.common.model.response.v2_5._
import me.socure.transactionauditing.common.model.response.v2_5.TransactionResponseCodecsV2_5._
import org.scalatest.{FunSuite, Matchers}

import scala.concurrent.duration.FiniteDuration
import scala.io.Source
import scalaz.{-\/, \/-}

class TransactionResponse2_5CodecsTest extends FunSuite with Matchers{
  val v2_5Response: String = Source.fromURL(getClass.getResource("/sample_response_2_5.json")).mkString
  val v2_5ResponseWithDebugWithCustom: String = Source.fromURL(getClass.getResource("/sample_response_2_5_custom_models.json")).mkString

  test("parse basic 2.5 response") {
    val responseOpt = Option(v2_5Response).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[TransactionResponseV2_5](_) match {
      case \/-(debug) => Some(debug)
      case -\/(err) =>
        throw new Exception(err)
    })

    responseOpt shouldBe Some(
      TransactionResponseV2_5(
        status = "Ok",
        msg = None,
        referenceId = "259264e6-2487-45f9-8a2e-45f7c04ff9f7",
        authScore = 10,
        reasonCodes = List(
          "I144",
          "I252",
          "I905",
          "I186",
          "I241",
          "I158",
          "I182",
          "I326",
          "I230",
          "I159",
          "I330",
          "I172",
          "I129",
          "I322",
          "I240",
          "I553",
          "I329",
          "I127",
          "I704",
          "I254",
          "I556",
          "I180",
          "I906",
          "I917",
          "I250",
          "I914",
          "I178",
          "I708",
          "I156",
          "I253",
          "I555",
          "I260",
          "I707",
          "I157",
          "I257",
          "I121",
          "R106"
        ).map(ReasonCode),
        confidence = 0.95,
        fraudScore = 0.0232,
        customModels = None,
        details =
            DetailResponse(
            fieldValidations =  Map(
              "address" -> "",
              "mobilenumber" -> "",
              "name" -> "",
              "email" -> ""
            ),
            blacklistResponse = BlacklistResponseV2_5(
              reason = "Spammer",
              reportedDate = "2017-04-28",
              industry = "No Industry"
            ),
            profilesFound = List(
              "https://www.facebook.com/azazeldt",
              "https://www.flickr.com/people/********@N06",
              "https://www.linkedin.com/in/roger-plotz-41609883",
              "https://angel.co/azazeldt",
              "http://vimeo.com/user10901589",
              "http://www.pinterest.com/azazeldt/",
              "https://myspace.com/azazeldt",
              "https://plus.google.com/117247490108010810232",
              "https://twitter.com/?profile_id=14230203",
              "https://twitter.com/azazeldt"
            ),
            kycResponse = KycResponse2_5(
              cvi = Some(40),
              correlationIndices = Some(KycCorrelationIndices(
                nameAddressPhoneIndex = 8L,
                nameAddressSsnIndex = 8L
              )),
              decision = Some(KycDecision(
                modelName = "SOCURE_FIX",
                modelVersion = "1.0",
                status = "REFER"
              )),
              fieldValidations = Map(
                "firstname" -> 0.99,
                "surname" -> 0.99,
                "streetaddress" -> 0.99,
                "city" -> 0.99,
                "state" -> 0.99,
                "zip" -> 0.99
              )
            ),
            correlationScores = Map(
              "name_address_email" -> "0.99",
              "name_phone" -> "",
              "name_phone_email" -> "",
              "name_address_phone_email" -> "",
              "name_email" -> "0.99",
              "name_address" -> "0.99",
              "name_address_phone" -> ""
            ),
            watchlistResponse =  None
          ),
        debug = None
      )
    )
  }

  test("test full 2.5 response with debug") {
    val responseOpt = Option(v2_5ResponseWithDebugWithCustom).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[TransactionResponseV2_5](_) match {
      case \/-(debug) => Some(debug)
      case -\/(err) =>
        throw new Exception(err)
    })

    responseOpt shouldBe Some(
      TransactionResponseV2_5(
        status = "Ok",
        msg = None,
        referenceId = "a8aa4e1f-6144-439c-a260-c960c2fb97f7",
        authScore = 4,
        reasonCodes = List(
          "I144", "I916", "I252", "I186", "I241", "I158", "I901", "I182",
          "I326", "I230", "I159", "I172", "I129", "I322", "I333", "I240",
          "I553", "I611", "I614", "I329", "I127", "I704", "I254", "I556",
          "I180", "I906", "I917", "I250", "I178", "I708", "I156", "I253",
          "R702", "I618", "I555", "I260", "I707", "I602", "I157", "I257",
          "I121", "R106"
        ).map(ReasonCode),
        confidence = 0.33,
        fraudScore = 0.0508,
        customModels = Some(
          List(
            FraudModelScore(
              name = "vesta_f1_022017_ta_model_gbm2",
              version = "1.0",
              score = 0.0012
            ),
            FraudModelScore(
              name = "xoom_oct2016_combine_sender_v1f1_model_rf2",
              version = "1.02",
              score = 0.0291
            ),
            FraudModelScore(
              name = "radiusbank_model_glm1",
              version = "1.0",
              score = 0.0508
            )
          )
        ),
        details =
          DetailResponse(
            fieldValidations =  Map(
              "address" -> "",
              "mobilenumber" -> "",
              "name" -> "",
              "email" -> ""
            ),
            blacklistResponse = BlacklistResponseV2_5(
              reason = "Spammer",
              reportedDate = "2017-04-28",
              industry = "No Industry"
            ),
            profilesFound = List(
              "https://www.facebook.com/azazeldt", "https://www.flickr.com/people/********@N06", "http://www.linkedin.com/in/roger-plotz-2729969",
              "https://angel.co/azazeldt", "https://myspace.com/azazeldt", "https://plus.google.com/117247490108010810232",
              "http://www.facebook.com/people/_/*********", "http://www.amazon.com/gp/pdp/profile/A2YV0MN55C2FG4/",
              "http://www.hi5.com/friend/p100155053--profile--html", "https://twitter.com/azazeldt", "http://www.amazon.com/wishlist/TKCEWUO7JDDI",
              "https://whitepages.plus/n/Roger_Plotz/Grantsville_UT/032be7e7374eaaa901e1e0be70043837",
              "https://www.linkedin.com/in/roger-plotz-41609883", "https://whitepages.plus/n/Roger_Plotz/San_francisco_CA/d5fff8071d9a0884182fff39d8b62784",
              "http://pinterest.com/azazeldt/", "http://vimeo.com/user10901589", "http://www.pinterest.com/azazeldt/", "https://twitter.com/?profile_id=14230203",
              "http://www.flickr.com/people/********@N06/"
            ),
            kycResponse = KycResponse2_5(
              cvi = None,
              correlationIndices = None,
              decision = None,
              fieldValidations = Map(
                "firstname" -> 0.99,
                "surname" -> 0.99,
                "streetaddress" -> 0.99,
                "mobilenumber" -> 0.99,
                "city" -> 0.99,
                "state" -> 0.99,
                "zip" -> 0.99
              )
            ),
            correlationScores = Map(
              "name_address_email" -> "0.99",
              "name_phone_email" -> "0.99",
              "name_address_phone_email" -> "0.99",
              "name_address_phone" -> "0.99"
            ),
            watchlistResponse =  None
          ),
        debug = Some(
          Debug(
            Some(
              FraudModelScores(
                primary = Some(ModelScore(
                  score = 0.*****************,
                  model = Model(
                    name = "Xoom.US.********",
                    version = "1.0",
                    identifier = "radiusbank_model_glm1"
                  )
                )),
                sigma = None,
                secondary = Set(
                  ModelScore(
                    score = 0.*****************,
                    model = Model(
                      name = "radiusbank_model_glm1",
                      version = "1.0",
                      identifier = "radiusbank_model_glm1"
                    )
                  )
                )
              )
            ),
            correlationModelScores = CorrelationModelScores(
              nameAddress = Some(
                ModelScore(
                  score = 0.****************,
                  model = Model(
                    name = "Correlation Model - Name vs Address (US)",
                    version = "5.0",
                    identifier = "name_addr_corr_gbm_final_v2"
                  )
                )
              ),
              nameEmail = Some(
                ModelScore(
                  score = 0.****************,
                  model = Model(
                    name = "Correlation Model - Name vs Email (US)",
                    version = "3.0",
                    identifier = "drf_name_email_v6"
                  )
                )
              ),
              namePhone = Some(
                ModelScore(
                  score = 0.****************,
                  model = Model(
                    name = "Correlation Model - Name vs Phone (US)",
                    version = "3.0",
                    identifier = "drf_name_phone_v3"
                  )
                )
              )
            ),
            piiRiskModelScores = PiiRiskModelScores(
              address = None,
              email = None,
              phone = None
            ),
            ruleCodes = Some(
              List(
                RuleCode(
                  ruleCode = "scoreComponents.TWANL.100009",
                  score = 1.0
                ),
                RuleCode(
                  ruleCode = "scoreComponents.PBVAL.200216",
                  score = 10.0
                ),
                RuleCode(
                  ruleCode = "scoreComponents.NSVAL.300001",
                  score = 99.0
                ),
                RuleCode(
                  ruleCode = "scoreComponents.WPVAL.100056",
                  score = 0.0
                ),
                RuleCode(
                  ruleCode = "scoreComponents.FCVAL.300002",
                  score = 1.0
                ),
                RuleCode(
                  ruleCode = "scoreComponents.LNVAL.100128",
                  score = 8.0
                )
              )
            ),
            reasonCodes = None,
            httpStatus = None,
            queryPlan = Some(
              QueryPlan(
                vendors = Set(IConstants.Component.FORM_VAL),
                schedulerTimeout = FiniteDuration(5000, "milliseconds"),
                vendorTimeouts = VendorTimeoutWrapper(
                  vendors = Set(
                    VendorTimeout(
                      vendor = IConstants.Component.FORM_VAL,
                      timeout = FiniteDuration(5000, "milliseconds")
                    )
                  )
                ),
                idPlusFeatures = DebugIdPlusFeatures(
                  requestedFeatures = Set(IdPlusFeatures.KYC),
                  resolvedFeatures = Set(IdPlusFeatures.KYC),
                  outputtedFeatures = Set(IdPlusFeatures.KYC)
                )
              )
            )
            )
          )
        )
      )
  }
}
