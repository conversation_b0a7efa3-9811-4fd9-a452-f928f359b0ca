package me.socure.transactionauditing.common.model

import argonaut.Parse
import me.socure.transactionauditing.common.model.response.v1_0.watchlist.{TransactionWatchlistResponseV1_0, WatchlistMatch1_0, WatchlistResponseV1_0}
import me.socure.transactionauditing.common.model.response.v1_0.watchlist.TransactionWatchlistResponseV1_0Codecs._
import org.scalatest.{FunSuite, Matchers}

import scala.io.Source
import scalaz.{-\/, \/-}

class TransactionWatchlistResponseV1_0CodecsTest extends FunSuite with Matchers {
  val watchlistV1: String = Source.fromURL(getClass.getResource("/sample_response_watchlist_1_0.json")).mkString

  test("should parse watchlistv1 correctly") {
    val responseOpt = Option(watchlistV1).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[TransactionWatchlistResponseV1_0](_) match {
      case \/-(watchlistResponse) => Some(watchlistResponse)
      case -\/(err) =>
        throw new Exception(err)
    })

    responseOpt shouldBe Some(
      TransactionWatchlistResponseV1_0(
        status = "Ok",
        referenceId = "31a8b232-9fc7-4d83-a497-305c8a231667",
        reasonCodes = List(
          ReasonCode("R186")
        ),
        matches = WatchlistResponseV1_0(
          Map(
            "OSFI CONSOLIDATED LIST.BDF" -> WatchlistMatch1_0(
              score = 100,
              fields = List("name", "dob"),
              sourceUrls = List.empty
            ),
            "UN CONSOLIDATED LIST.BDF" -> WatchlistMatch1_0(
              score = 100,
              fields = List("name", "dob"),
              sourceUrls = List.empty
            ),
            "OFAC SDN.BDF" -> WatchlistMatch1_0(
              score = 100,
              fields = List("name", "dob"),
              sourceUrls = List.empty
            ),
            "BANK OF ENGLAND CONSOLIDATED LIST.BDF" -> WatchlistMatch1_0(
              score = 100,
              fields = List("name", "dob"),
              sourceUrls = List.empty
            ),
            "EU CONSOLIDATED LIST.BDF" -> WatchlistMatch1_0(
              score = 100,
              fields = List("name", "dob"),
              sourceUrls = List.empty
            )
          )
        ),
        msg = None
      )
    )
  }

}
