package me.socure.transactionauditing.common.json

import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.Formats
import org.json4s.jackson.Serialization
import org.scalatest.{FreeSpec, Matchers}

class TrxJsonFormatsTest extends FreeSpec with Matchers {

  private implicit val formats: Formats = TrxJsonFormats.value
  private case class DateTimeHolder(value: DateTime)

  "TrxJsonFormats" - {

    "should deserialize expected json format" in {
      val dateTime = DateTime.now(DateTimeZone.UTC)
      val s = TrxJsonFormats.fmt.print(dateTime)
      val jsonStr = s"""{"value" : "$s"}"""
      val result = Serialization.read[DateTimeHolder](jsonStr)
      result.value shouldBe dateTime
    }

    "should fallback to default date deserialization" in {
      val dateTime = DateTime.now(DateTimeZone.UTC)
      val s = formats.dateFormat.format(dateTime.toDate)
      val jsonStr = s"""{"value" : "$s"}"""
      val result = Serialization.read[DateTimeHolder](jsonStr).value
      result.withZone(DateTimeZone.UTC) shouldBe dateTime.withMillisOfSecond(0)
    }
  }
}
