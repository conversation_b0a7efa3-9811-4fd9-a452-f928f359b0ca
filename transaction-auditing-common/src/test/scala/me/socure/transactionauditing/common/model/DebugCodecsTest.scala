package me.socure.transactionauditing.common.model

import argonaut.Parse
import me.socure.model.idplus.v3_0.IdPlusFeatures
import me.socure.service.constants.IConstants
import me.socure.transactionauditing.common.domain.ModelScore
import me.socure.transactionauditing.common.model.debug._
import me.socure.transactionauditing.common.model.debug.codecs.DebugCodecs._
import org.scalatest.{FunSuite, Matchers}

import scala.concurrent.duration.FiniteDuration
import scala.io.Source
import scalaz.{-\/, \/-}

class DebugCodecsTest extends FunSuite with Matchers {
  val debugJsonLegacy: String = Source.fromURL(getClass.getResource("/sample_debug_2_5.json")).mkString
  val debugJson: String = Source.fromURL(getClass.getResource("/sample_debug.json")).mkString
  val debugJsonNoPrimaryFraud: String = Source.fromURL(getClass.getResource("/sample_debug_no_primary_fraud.json")).mkString


  test("parse legacy json") {
    val debugOpt = Option(debugJsonLegacy).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[Debug](_) match {
      case \/-(debug) => Some(debug)
      case -\/(err) =>
        throw new Exception(err)
    })

    val debug = debugOpt.get
    debug.fraudModelScores shouldBe Some(
      FraudModelScores(
        primary = Some(ModelScore(
          model = Model(
            name = "gyft_model_rf2",
            version = "1.0",
            identifier = "gyft_model_rf2"
          ),
          score = 0.024693052768707258
        )),
        sigma = None,
        secondary = Set(
          ModelScore(
            model = Model(
              name = "Generic.US.Turo",
              version = "1.0",
              identifier = "Generic.US.Turo"
            ),
            score = 0.0571096080541611
          )
        )
      )
    )

    debug.correlationModelScores shouldBe  CorrelationModelScores(
      nameAddress = Some(ModelScore(
        model = Model(
          name = "Correlation Model - Name vs Address (US)",
          version = "1",
          identifier = "default name_address"
        ),
        score = 0.01
      )),
      nameEmail = Some(ModelScore(
        model = Model(
          name = "Correlation Model - Name vs Email (US)",
          version = "2",
          identifier = "default name_email"
        ),
        score = 0.02
      )),
      namePhone = Some(ModelScore(
        model = Model(
          name = "Correlation Model - Name vs Phone (US)",
          version = "3",
          identifier = "default name_phone"
        ),
        score = 0.03
      ))
    )

    debug.piiRiskModelScores shouldBe PiiRiskModelScores(
      address = Some(ModelScore(
        model = Model(
          name = "Address Risk Score Model",
          version = "1",
          identifier = "default address"
        ),
        score = 0.01
      )),
      email = Some(ModelScore(
        model = Model(
          name = "Email Risk Score Model",
          version = "2",
          identifier = "default email"
        ),
        score = 0.02
      )),
      phone = Some(ModelScore(
        model = Model(
          name = "Phone Risk Score Model",
          version = "3",
          identifier = "default phone"
        ),
        score = 0.03
      ))
    )
    debug.reasonCodes shouldBe None
    debug.httpStatus shouldBe None
    debug.queryPlan shouldBe None
  }

  test("parse 3.0 debug node") {
    val debugOpt = Option(debugJson).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[Debug](_) match {
      case \/-(debug) => Some(debug)
      case -\/(err) =>
        throw new Exception(err)
    })

    val debug = debugOpt.get
    debug.fraudModelScores shouldBe Some(
      FraudModelScores(
        primary = Some(ModelScore(
          model = Model(
            name = "Fraud Generic Model (US)",
            version = "3.0",
            identifier = "gen_us_30_20171129_adjust_gbm_v1"
          ),
          score = 0.0653796761095822
        )),
        sigma = Some(ModelScore(
          model = Model(
            name = "Fraud Sigma Model (US)",
            version = "5.0",
            identifier = "gen_us_30_20171129_adjust_gbm_v1_sigma"
          ),
          score = 0.1653796761095822
        )),
        secondary = Set.empty
      )
    )

    debug.correlationModelScores shouldBe  CorrelationModelScores(
      nameAddress = Some(ModelScore(
        model = Model(
          name = "Correlation Model - Name vs Address (US)",
          version = "6.0",
          identifier = "Name_Address_w9_HD_IF_corr_gbm_v2"
        ),
        score = 0.9914661996876485
      )),
      nameEmail = Some(ModelScore(
        model = Model(
          name = "Correlation Model - Name vs Email (US)",
          version = "4.0",
          identifier = "Combined_Trainset_glm_model_vI_HDWP_N_4"
        ),
        score = 0.9999792631276668
      )),
      namePhone = Some(ModelScore(
        model = Model(
          name = "Correlation Model - Name vs Phone (US)",
          version = "4.0",
          identifier = "Name_Phone_prior80_WP_NS_corr_drf_v2"
        ),
        score = 0.9462615894444752
      ))
    )

    debug.piiRiskModelScores shouldBe PiiRiskModelScores(
      address = Some(ModelScore(
        model = Model(
          name = "Address Risk Model (US)",
          version = "3.0",
          identifier = "addrrisk_oct2017_glm_model_vX"
        ),
        score = 0.11518155603028522
      )),
      email = Some(ModelScore(
        model = Model(
          name = "Email Risk Model (US)",
          version = "4.0",
          identifier = "emailrisk_oct2017_gbm_model_v_23"
        ),
        score = 0.008523373294829962
      )),
      phone = Some(ModelScore(
        model = Model(
          name = "Phone Risk Model (US)",
          version = "2.0",
          identifier = "phone_risk_model_glm_v2"
        ),
        score = 0.9999999689936944
      ))
    )
    debug.reasonCodes shouldBe Some(
      List(
        ReasonCode("I556"),
        ReasonCode("I618"),
        ReasonCode("I708"),
        ReasonCode("I520"),
        ReasonCode("I720"),
        ReasonCode("R620"),
        ReasonCode("R106"),
        ReasonCode("I127"),
        ReasonCode("I707"),
        ReasonCode("I553"),
        ReasonCode("I609"),
        ReasonCode("I555"),
        ReasonCode("I602"),
        ReasonCode("R606"),
        ReasonCode("I121"),
        ReasonCode("I611"),
        ReasonCode("I704")
      )
    )
    debug.httpStatus shouldBe Some(200)
    debug.queryPlan.get.vendors should contain allOf (
      IConstants.Component.COVAL,
      IConstants.Component.IFVAL,
      IConstants.Component.BLVAL,
      IConstants.Component.HDVAL,
      IConstants.Component.FORM_VAL,
      IConstants.Component.WPVAL,
      IConstants.Component.GLOBAL,
      IConstants.Component.FVANL,
      IConstants.Component.RKVAL,
      IConstants.Component.NSVAL,
      IConstants.Component.FULL_CONTACT_VAL
    )
    debug.queryPlan.get.schedulerTimeout shouldBe FiniteDuration.apply(5000, "ms")
    debug.queryPlan.get.vendorTimeouts.vendors should contain allOf (
      VendorTimeout(IConstants.Component.WPVAL, FiniteDuration.apply(5000, "ms")),
      VendorTimeout(IConstants.Component.FULL_CONTACT_VAL, FiniteDuration.apply(5000, "ms")),
      VendorTimeout(IConstants.Component.FORM_VAL, FiniteDuration.apply(5000, "ms")),
      VendorTimeout(IConstants.Component.IFVAL, FiniteDuration.apply(4000, "ms")),
      VendorTimeout(IConstants.Component.NSVAL, FiniteDuration.apply(5000, "ms")),
      VendorTimeout(IConstants.Component.HDVAL, FiniteDuration.apply(4000, "ms")),
      VendorTimeout(IConstants.Component.FVANL, FiniteDuration.apply(5000, "ms")),
      VendorTimeout(IConstants.Component.BLVAL, FiniteDuration.apply(4000, "ms"))
    )
    val idPlusFeatures = debug.queryPlan.get.idPlusFeatures
    idPlusFeatures.requestedFeatures should contain allOf (
      IdPlusFeatures.FraudScore,
      IdPlusFeatures.AllCorrelations
    )
    idPlusFeatures.resolvedFeatures should contain allOf (
      IdPlusFeatures.FraudScore,
      IdPlusFeatures.Social,
      IdPlusFeatures.EmailRiskScore,
      IdPlusFeatures.AllCorrelations,
      IdPlusFeatures.NameEmailCorrelation,
      IdPlusFeatures.NamePhoneCorrelation,
      IdPlusFeatures.NameAddressCorrelation,
      IdPlusFeatures.AddressRiskScore,
      IdPlusFeatures.Blacklist,
      IdPlusFeatures.PhoneRiskScore
    )
    idPlusFeatures.outputtedFeatures should contain allOf (
      IdPlusFeatures.FraudScore,
      IdPlusFeatures.Social,
      IdPlusFeatures.EmailRiskScore,
      IdPlusFeatures.AllCorrelations,
      IdPlusFeatures.NameEmailCorrelation,
      IdPlusFeatures.NamePhoneCorrelation,
      IdPlusFeatures.NameAddressCorrelation,
      IdPlusFeatures.AddressRiskScore,
      IdPlusFeatures.Blacklist,
      IdPlusFeatures.PhoneRiskScore
    )
  }

  test("parse 3.0 debug node without primary fraud model") {
    val debugOpt = Option(debugJsonNoPrimaryFraud).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[Debug](_) match {
      case \/-(debug) => Some(debug)
      case -\/(err) =>
        throw new Exception(err)
    })

    val debug = debugOpt.get
    debug.fraudModelScores shouldBe Some(
      FraudModelScores(
        primary = None,
        sigma = None,
        secondary = Set.empty
      )
    )

    debug.correlationModelScores shouldBe  CorrelationModelScores(
      nameAddress = Some(ModelScore(
        model = Model(
          name = "Correlation Model - Name vs Address (US)",
          version = "6.0",
          identifier = "Name_Address_w9_HD_IF_corr_gbm_v2"
        ),
        score = 0.9914661996876485
      )),
      nameEmail = Some(ModelScore(
        model = Model(
          name = "Correlation Model - Name vs Email (US)",
          version = "4.0",
          identifier = "Combined_Trainset_glm_model_vI_HDWP_N_4"
        ),
        score = 0.9999792631276668
      )),
      namePhone = Some(ModelScore(
        model = Model(
          name = "Correlation Model - Name vs Phone (US)",
          version = "4.0",
          identifier = "Name_Phone_prior80_WP_NS_corr_drf_v2"
        ),
        score = 0.9462615894444752
      ))
    )

    debug.piiRiskModelScores shouldBe PiiRiskModelScores(
      address = Some(ModelScore(
        model = Model(
          name = "Address Risk Model (US)",
          version = "3.0",
          identifier = "addrrisk_oct2017_glm_model_vX"
        ),
        score = 0.11518155603028522
      )),
      email = Some(ModelScore(
        model = Model(
          name = "Email Risk Model (US)",
          version = "4.0",
          identifier = "emailrisk_oct2017_gbm_model_v_23"
        ),
        score = 0.008523373294829962
      )),
      phone = Some(ModelScore(
        model = Model(
          name = "Phone Risk Model (US)",
          version = "2.0",
          identifier = "phone_risk_model_glm_v2"
        ),
        score = 0.9999999689936944
      ))
    )
    debug.reasonCodes shouldBe Some(
      List(
        ReasonCode("I556"),
        ReasonCode("I618"),
        ReasonCode("I708"),
        ReasonCode("I520"),
        ReasonCode("I720"),
        ReasonCode("R620"),
        ReasonCode("R106"),
        ReasonCode("I127"),
        ReasonCode("I707"),
        ReasonCode("I553"),
        ReasonCode("I609"),
        ReasonCode("I555"),
        ReasonCode("I602"),
        ReasonCode("R606"),
        ReasonCode("I121"),
        ReasonCode("I611"),
        ReasonCode("I704")
      )
    )
    debug.httpStatus shouldBe Some(200)
    debug.queryPlan.get.vendors should contain allOf (
      IConstants.Component.COVAL,
      IConstants.Component.IFVAL,
      IConstants.Component.BLVAL,
      IConstants.Component.HDVAL,
      IConstants.Component.FORM_VAL,
      IConstants.Component.WPVAL,
      IConstants.Component.GLOBAL,
      IConstants.Component.FVANL,
      IConstants.Component.RKVAL,
      IConstants.Component.NSVAL,
      IConstants.Component.FULL_CONTACT_VAL
    )
    debug.queryPlan.get.schedulerTimeout shouldBe FiniteDuration.apply(5000, "ms")
    debug.queryPlan.get.vendorTimeouts.vendors should contain allOf (
      VendorTimeout(IConstants.Component.WPVAL, FiniteDuration.apply(5000, "ms")),
      VendorTimeout(IConstants.Component.FULL_CONTACT_VAL, FiniteDuration.apply(5000, "ms")),
      VendorTimeout(IConstants.Component.FORM_VAL, FiniteDuration.apply(5000, "ms")),
      VendorTimeout(IConstants.Component.IFVAL, FiniteDuration.apply(4000, "ms")),
      VendorTimeout(IConstants.Component.NSVAL, FiniteDuration.apply(5000, "ms")),
      VendorTimeout(IConstants.Component.HDVAL, FiniteDuration.apply(4000, "ms")),
      VendorTimeout(IConstants.Component.FVANL, FiniteDuration.apply(5000, "ms")),
      VendorTimeout(IConstants.Component.BLVAL, FiniteDuration.apply(4000, "ms"))
    )
    val idPlusFeatures = debug.queryPlan.get.idPlusFeatures
    idPlusFeatures.requestedFeatures should contain allOf (
      IdPlusFeatures.FraudScore,
      IdPlusFeatures.AllCorrelations
    )
    idPlusFeatures.resolvedFeatures should contain allOf (
      IdPlusFeatures.FraudScore,
      IdPlusFeatures.Social,
      IdPlusFeatures.EmailRiskScore,
      IdPlusFeatures.AllCorrelations,
      IdPlusFeatures.NameEmailCorrelation,
      IdPlusFeatures.NamePhoneCorrelation,
      IdPlusFeatures.NameAddressCorrelation,
      IdPlusFeatures.AddressRiskScore,
      IdPlusFeatures.Blacklist,
      IdPlusFeatures.PhoneRiskScore
    )
    idPlusFeatures.outputtedFeatures should contain allOf (
      IdPlusFeatures.FraudScore,
      IdPlusFeatures.Social,
      IdPlusFeatures.EmailRiskScore,
      IdPlusFeatures.AllCorrelations,
      IdPlusFeatures.NameEmailCorrelation,
      IdPlusFeatures.NamePhoneCorrelation,
      IdPlusFeatures.NameAddressCorrelation,
      IdPlusFeatures.AddressRiskScore,
      IdPlusFeatures.Blacklist,
      IdPlusFeatures.PhoneRiskScore
    )
  }
}
