package me.socure.transactionauditing.common.model

import argonaut.Parse
import me.socure.transactionauditing.common.model.response.v3_0.TransactionResponseCodecsV3_0._
import me.socure.transactionauditing.common.model.response.v3_0.TransactionResponseV3_0
import me.socure.transactionauditing.common.model.response.{KycCorrelationIndices, KycDecision}
import org.scalatest.{FunSuite, Matchers}

import scala.io.Source
import scalaz.{-\/, \/-}

class KycResponse30CodecTest extends FunSuite with Matchers {
  val kycResponse3_0: String = Source.fromURL(getClass.getResource("/sample_response_kyc_3_0.json")).mkString

  test("parse kyc response correctly") {
    val responseOpt = Option(kycResponse3_0).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[TransactionResponseV3_0](_) match {
      case \/-(debug) => Some(debug)
      case -\/(err) =>
        throw new Exception(err)
    })

    val kycResponse = responseOpt.flatMap(_.kyc).get

    kycResponse.cvi.get shouldBe 40
    kycResponse.reasonCodes should contain allOf(
      ReasonCode("I914"),
      ReasonCode("R905"),
      ReasonCode("I917"),
      ReasonCode("R915"),
      ReasonCode("I905")
    )
    kycResponse.correlationIndices.get shouldBe KycCorrelationIndices(
      nameAddressPhoneIndex = 8L,
      nameAddressSsnIndex = 8L
    )
    kycResponse.decision.get shouldBe KycDecision(
      modelName = "SOCURE_FIX",
      modelVersion = "1.0",
      status = "REFER"
    )

    kycResponse.fieldValidations should contain allOf(
      "firstName" -> 0.99,
      "surName" -> 0.99,
      "streetAddress" -> 0.99,
      "city" -> 0.99,
      "state" -> 0.99,
      "zip" -> 0.99,
      "mobileNumber" -> 0.01,
      "dob" -> 0.99
    )
  }
}
