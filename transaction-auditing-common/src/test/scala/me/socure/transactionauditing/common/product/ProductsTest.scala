package me.socure.transactionauditing.common.product

import org.scalatest.{FunSuite, Matchers}

class ProductsTest extends FunSuite with Matchers {

  test("EmailAuthScore 3.0 should return correct url for 3.0") {
    Products.EmailAuthScore.V3_0.toString() shouldBe "/api/3.0/EmailAuthScore"
  }

  test("EmailAuthScore 2.5 should return correct url for 2.5") {
    Products.EmailAuthScore.V2_5.toString() shouldBe "/api/2.5/EmailAuthScore"
  }
}