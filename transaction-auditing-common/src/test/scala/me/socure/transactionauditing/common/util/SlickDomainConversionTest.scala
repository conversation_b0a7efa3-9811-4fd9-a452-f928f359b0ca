package me.socure.transactionauditing.common.util

import me.socure.common.transaction.id.TrxId
import me.socure.transactionauditing.common.domain.fixture.APITransactionValueGenerator
import me.socure.transactionauditing.common.transaction.TransactionColumns
import org.scalatest.{FunSuite, Matchers}

import scala.util.Random

/**
  * Created by <PERSON><PERSON><PERSON><PERSON> on 14/03/2017.
  */
class SlickDomainConversionTest extends FunSuite with Matchers {

  val request = "/api/2.5/EmailAuthScore?socurekey=some_key&email=rogerplotz667%40gmail.com&firstname=roger&surname=plotz&companyname=Socure&physicaladdress=521%20W%20141ST%20ST%20APT%204&zip=10031&city=New%20York&state=NY&country=US&mobilenumber=%2B14153689377&nationalid=*********&dob=123456&details=true&driverlicense=99k&ipaddress=127.0.0.1"
  val params = """{"country":"ID","ipaddress":"127.0.0.1","firstname":"<PERSON><PERSON><PERSON><PERSON>","mobilenumber":null,"impersonatorapikey":null,"city":"Jakarta","isblacklist":false,"userid":null,"nationalid":null,"physicaladdress2":null,"surname":"BAASYIR","nocache":null,"details":true,"state":null,"runid":null,"customeruserid":null,"email":"<EMAIL>","zip":null,"debug":true,"datascience":false,"physicaladdress":"521+w+141st+Street+apt4","driverlicensestate":"sdfsdfsd","kyc":false,"forcerefresh":true,"showcustommodelsmapped":false,"companyname":null,"dob":"********","socurekey":"somekey","geocode":null,"watchlist":"2","driverlicense":null}"""
  val params2 = """{"nationalid":"*********","city":"MERRIT","stepupReason":null,"payments":{"recipientCountry":null,"paymentType":null,"disbursementType":null,"account":{"accountNumber":"***********","routingNumber":"*********","inquiries":["STATUS","OWNERSHIP"],"validInquiries":true}},"previousReferenceId":null,"watchlistfilters":null,"runid":null,"driverlicensestate":null,"mobilenumber":null,"zip":"49667","email":null,"state":"MI","socurekey":"somekey","companyname":null,"dob":"******","firstname":"Wilson","physicaladdress2":null,"physicaladdress":"206 GOODWIN ST","consentTimestamp":null,"orderamount":null,"ipaddress":null,"vendors":null,"shippingDetails":null,"ein":"*********","prevordercount":0.0,"nocache":0.0,"surname":"paul","documentuuid":null,"country":"us","customeruserid":null,"userid":null,"orderchannel":null,"debug":false,"driverlicense":null,"datascience":false,"geocode":null,"modules":["accountintelligence"],"fullname":null,"forcerefresh":null,"accountcreationdate":null,"impersonatorapikey":null,"businessName":null,"businessPhone":null,"submissiondate":null,"details":false,"userConsent":true,"deviceSessionId":null,"lastorderdate":null,"isConsenting":null,"merchantDetails":null,"prevUnpaidOrderCount":null,"device":null,"userName":null,"addresses":null}"""
  implicit val testTrxId = new TrxId("test-id")

  test("should obfuscate PII information in request URI") {
    SlickDomainConversion.obfuscatePII(request) should include ("ipaddress=******")
  }

  test("should not obfuscate PII information in request URI") {
    SlickDomainConversion.obfuscatePII(request) should not include ("driverlicensestate=******")
  }

  test("should obfuscate PII infor in parameters") {
    SlickDomainConversion.obfuscateParameters(params) should include (""""ipaddress":"******"""")
  }

  test("should do nothing PII info not in parameters") {
    SlickDomainConversion.obfuscateParameters(params) should include (""""driverlicense":null""")
  }

  test("should obfuscate newly added PII fields in parameters") {
    val obfuscatedParams = SlickDomainConversion.obfuscateParameters(params2)
    obfuscatedParams should include (""""accountNumber":"******"""")
    obfuscatedParams should include (""""routingNumber":"******"""")
    obfuscatedParams should include (""""ein":"******"""")
  }


  test("should return Parameter error when status is 400") {
    SlickDomainConversion.getStatusFromDebug(Some("""[{"http_status":400}]""")) shouldBe(Some("Parameter Error"))
  }

  test("should return Parameter error when status is 403") {
    SlickDomainConversion.getStatusFromDebug(Some("""[{"http_status":403}]""")) shouldBe(Some("Parameter Error"))
  }

  test("should return Internal error when status is 500") {
    SlickDomainConversion.getStatusFromDebug(Some("""[{"http_status":500}]""")) shouldBe(Some("Internal Error"))
  }

  test("should return Internal error when status is 501") {
    SlickDomainConversion.getStatusFromDebug(Some("""[{"http_status":501}]""")) shouldBe(Some("Internal Error"))
  }

  test("should return Successful when status is 200") {
    SlickDomainConversion.getStatusFromDebug(Some("""[{"http_status":200}]""")) shouldBe(Some("Successful"))
  }

  test("should return nothing when no debug block") {
    SlickDomainConversion.getStatusFromDebug(None) shouldBe(None)
  }

  test("should not reuturn API Key and AccountID if not asked for") {
    val trx = SlickDomainConversion
      .toSlickTransaction(APITransactionValueGenerator.aAPITransaction())
      .copy(
        accountId = Some(2L),
        apiKey = Some("tes-acc")
      )

    val expectedTrx = trx.copy(
      accountId = None,
      apiKey = None
    )

    val columns = List.fill(5)(Random.shuffle(TransactionColumns.values).head).toSet -- Set(TransactionColumns.ACCOUNT_ID, TransactionColumns.API_KEY)

    val actualTrx = SlickDomainConversion.removeAccountIdAndApiKeyIfNecessary(tx = trx, columns = columns)

    actualTrx shouldBe expectedTrx
  }
}
