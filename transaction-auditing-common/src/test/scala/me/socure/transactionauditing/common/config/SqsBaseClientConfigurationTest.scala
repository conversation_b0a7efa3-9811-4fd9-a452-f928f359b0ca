package me.socure.transactionauditing.common.config

import com.typesafe.config.{Config, ConfigFactory}
import me.socure.transactionauditing.common.util.SqsQueueEnums
import org.scalatest.{FunSuite, Matchers}

class SqsBaseClientConfigurationTest extends FunSuite with Matchers {

  val config: Config = ConfigFactory.load("test-client.conf")

  test("load the transaction config settings") {
    val settings = new SqsBaseClientConfiguration(config, SqsQueueEnums.TRANSACTION)
    settings.endpoints.map(_.region) should contain  ("us-east-1")
    settings.endpoints.map(_.region) should contain ("us-west-1")
    settings.endpoints.map(_.region) should contain  ("us-east-2")
    settings.endpoints.head.queue.queueName shouldBe "transaction-auditing-stage"
    settings.maxRetries shouldBe 10
    settings.maxBackoff shouldBe 32
    settings.minBackoff shouldBe 2
  }

  test("load the third-party config settings") {
    val settings = new SqsBaseClientConfiguration(config, SqsQueueEnums.THIRD_PARTY)
    settings.endpoints.map(_.region) should contain  ("us-east-1")
    settings.endpoints.map(_.region) should contain ("us-west-1")
    settings.endpoints.map(_.region) should contain  ("us-east-2")
    settings.endpoints.head.queue.queueName shouldBe "third-party-transaction-auditing-stage"
    settings.endpoints.head.queue.queueOwnerAWSAccountId shouldBe Some("************")
    settings.endpoints.last.queue.queueOwnerAWSAccountId shouldBe None
    settings.maxRetries shouldBe 10
    settings.maxBackoff shouldBe 32
    settings.minBackoff shouldBe 2
  }



}
