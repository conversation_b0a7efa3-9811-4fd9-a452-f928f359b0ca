package me.socure.transactionauditing.common.config

import com.amazon.sqs.javamessaging.AmazonSQSExtendedClient
import com.amazonaws.auth.{AWSCredentialsProvider, AWSStaticCredentialsProvider, BasicAWSCredentials}
import com.amazonaws.services.s3.AmazonS3Client
import com.amazonaws.services.sqs._
import com.amazonaws.services.sqs.model.GetQueueUrlResult
import com.typesafe.config.ConfigFactory
import me.socure.s3.ExtendedSQSClientRegionFactory
import me.socure.transactionauditing.common.util.SqsQueueEnums
import org.mockito.Mockito
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuite, Matchers}

class ClientSqsEndpointTest extends FunSuite with Matchers with MockitoSugar with BeforeAndAfter {

  private val config = ConfigFactory.load("test-client.conf")
  private val baseSqsClient = mock[AmazonSQS]
  private val sqsClient = new AmazonSQSExtendedClient(baseSqsClient)
  private val awsConfiguration = new SqsBaseClientConfiguration(config, SqsQueueEnums.TRANSACTION)
  private val transactionQueue = awsConfiguration.endpoints.head.queue
  private val queueUrl = "trx_queue_url"

  before {
    Mockito.reset(baseSqsClient)
  }

  test("successfully load up basic source config information") {
    awsConfiguration.endpoints.head.region shouldBe "us-east-1"
    awsConfiguration.endpoints.tail.head.region shouldBe "us-west-1"
    awsConfiguration.endpoints.tail.tail.head.region shouldBe "us-east-2"
  }

  test("Test SqsEndpoint creation") {
    withTrxQueueMock {
      val endpoint = new ClientSqsEndpoint(sqsClient, awsConfiguration.endpoints.head, SqsQueueEnums.TRANSACTION)

      endpoint.queue.url shouldBe queueUrl
      endpoint.queueSourceSettings.maxBatchSize shouldBe 10
      endpoint.queueSourceSettings.maxBufferSize shouldBe 60
      endpoint.queueSourceSettings.waitTimeSeconds shouldBe 20
    }
  }

  test("successfully load up basic sink information") {
    withTrxQueueMock {
      val endpoint = new ClientSqsEndpoint(sqsClient, awsConfiguration.endpoints.head, SqsQueueEnums.TRANSACTION)

      endpoint.queue.url shouldBe queueUrl
      endpoint.queueSinkSettings.maxInFlight shouldBe 15
    }
  }


  test("test getting client information") {
    val sqsClient = createClient("test-endpoint.com", new AWSStaticCredentialsProvider(
      new BasicAWSCredentials("test key", "test secret")
    ))
    val endpoint = mock[ClientSqsEndpoint]
    Mockito.when(endpoint.client).thenReturn(sqsClient)

    endpoint.client shouldBe sqsClient
  }

  private def withTrxQueueMock[T](action: => T): T = {
    val queueUrlResult = mock[GetQueueUrlResult]
    Mockito.when(queueUrlResult.getQueueUrl).thenReturn(queueUrl)
    Mockito.when(baseSqsClient.getQueueUrl(transactionQueue.queueName)).thenReturn(queueUrlResult)
    try {
      action
    } finally {
      Mockito.verify(queueUrlResult).getQueueUrl
      Mockito.verify(baseSqsClient).getQueueUrl(transactionQueue.queueName)
    }
  }

  private def createClient(sqsEndpoint: String, credentialsProvider: AWSCredentialsProvider): AmazonSQSExtendedClient = {
    val sqsBuilder = AmazonSQSClientBuilder.standard()
    ExtendedSQSClientRegionFactory.get(mock[AmazonS3Client], "test-bucket", sqsBuilder, "us-east-1")
  }

}
