package me.socure.transactionauditing.common.transaction

import me.socure.transactionauditing.common.transaction.TransactionColumns._
import org.scalatest.{FunSuite, Matchers}


class TransactionColumnsTest extends FunSuite with Matchers {

  test("check enum values") {
    TRANSACTION_DATE.name shouldBe "transactionDate"
    AUTH_SCORE.name shouldBe "authScore"
    API_REQUEST.name shouldBe "apiRequest"
    API_RESPONSE.name shouldBe "apiResponse"
    PROCESSING_TIME.name shouldBe "processingTime"
    CUSTOMER_USER_ID.name shouldBe "customerUserId"
    KYC.name shouldBe "kyc"
    ERROR.name shouldBe "error"
    PARAMETERS.name shouldBe "parameters"
    CONFIDENCE.name shouldBe "confidence"
    ENVIRONMENT_ID.name shouldBe "environmentId"
    DETAILS.name shouldBe "details"
    DEBUG.name shouldBe "debug"
    UUID.name shouldBe "uuid"
  }
}