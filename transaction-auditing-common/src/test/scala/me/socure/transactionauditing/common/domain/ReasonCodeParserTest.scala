package me.socure.transactionauditing.common.domain

import org.scalatest.{FunSuite, Matchers}

class ReasonCodeParserTest extends FunSuite with Matchers {

  test("parse reasoncodes") {
    val rcList = Some("[\"I144\",\"I252\",\"I905\",\"I186\",\"I241\",\"I158\",\"I182\",\"I326\",\"I230\",\"R908\",\"I159\",\"I172\",\"I129\",\"I322\",\"I333\",\"I240\",\"I553\",\"R904\",\"I329\",\"I127\",\"I254\",\"I556\",\"I180\",\"I250\",\"I178\",\"I156\",\"I903\",\"I253\",\"I555\",\"I260\",\"I157\",\"I257\",\"I121\",\"R106\"]")
    val parsed = ReasonCodeParser.parse(rcList)
    parsed.size shouldBe 34
    parsed.contains("I158") shouldBe true
    parsed.contains("I144") shouldBe true
    parsed.contains("R106") shouldBe true
  }

  test("parse no reasoncodes") {
    val parsed = ReasonCodeParser.parse(None)
    parsed.size shouldBe 0
  }

  test("parse empty reasoncodes") {
    val rcList = Some("[]")
    val parsed = ReasonCodeParser.parse(rcList)
    parsed.size shouldBe 0
  }

}
