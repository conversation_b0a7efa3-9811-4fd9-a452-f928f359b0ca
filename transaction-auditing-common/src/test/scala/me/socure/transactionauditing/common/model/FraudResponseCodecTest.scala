package me.socure.transactionauditing.common.model

import argonaut.Parse
import me.socure.transactionauditing.common.model.response.v3_0.TransactionResponseCodecsV3_0._
import me.socure.transactionauditing.common.model.response.v3_0.TransactionResponseV3_0
import me.socure.transactionauditing.common.model.response.{CorrelationResponse, FraudModelScore}
import org.scalatest.{FunSuite, Matchers}

import scala.io.Source
import scalaz.{-\/, \/-}

class FraudResponseCodecTest extends FunSuite with Matchers {
  val fraudResponse3_0: String = Source.fromURL(getClass.getResource("/sample_response_fraud_3_0.json")).mkString

  test("should parse valid fraud module results") {
    val responseOpt = Option(fraudResponse3_0).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[TransactionResponseV3_0](_) match {
      case \/-(debug) => Some(debug)
      case -\/(err) =>
        throw new Exception(err)
    })

    val fraudResponseReasonCodes = responseOpt.flatMap(_.fraud).map(_.reasonCodes)
    val fraudResponseScores = responseOpt.flatMap(_.fraud).map(_.scores)

    fraudResponseReasonCodes.get should contain allOf(
      ReasonCode("I704"),
      ReasonCode("I708"),
      ReasonCode("I618"),
      ReasonCode("I609"),
      ReasonCode("I707"),
      ReasonCode("R551"),
      ReasonCode("R606"),
      ReasonCode("I611"),
      ReasonCode("I602")
    )
    fraudResponseScores shouldBe Some(
      List(
        FraudModelScore(
          name = "generic",
          version = "3.0",
          score = 0.0697
        ),
        FraudModelScore(
          name = "My TestModel-1",
          version = "11.1",
          score = 0.1665
        )
      )
    )
  }

  test("should parse all the correlations that come along with fraud") {
    val responseOpt = Option(fraudResponse3_0).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[TransactionResponseV3_0](_) match {
      case \/-(debug) => Some(debug)
      case -\/(err) =>
        throw new Exception(err)
    })

    responseOpt.flatMap(_.nameAddressCorrelation) shouldBe Some(CorrelationResponse(
      reasonCodes = Set(ReasonCode("I708")),
      score = 0.9915
    ))

    responseOpt.flatMap(_.nameEmailCorrelation) shouldBe Some(CorrelationResponse(
      reasonCodes = Set(ReasonCode("R551")),
      score = 0.3075
    ))

    responseOpt.flatMap(_.namePhoneCorrelation) shouldBe Some(CorrelationResponse(
      reasonCodes = Set(ReasonCode("I618")),
      score = 0.9463
    ))
  }

  test("should parse all the risk scores that come with the correlation and fraud scores") {
    val responseOpt = Option(fraudResponse3_0).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[TransactionResponseV3_0](_) match {
      case \/-(debug) => Some(debug)
      case -\/(err) =>
        throw new Exception(err)
    })

    val addressRisk = responseOpt.flatMap(_.addressRisk)
    addressRisk.get.score shouldBe 0.1152
    addressRisk.get.reasonCodes should contain allOf(ReasonCode("I704"),
      ReasonCode("I708"),
      ReasonCode("I720"),
      ReasonCode("I707")
    )

    val emailRisk = responseOpt.flatMap(_.emailRisk)
    emailRisk.get.score shouldBe 0.9945
    emailRisk.get.reasonCodes should contain allOf(
      ReasonCode("R551"),
      ReasonCode("R520")
    )

    val phoneRisk = responseOpt.flatMap(_.phoneRisk)
    phoneRisk.get.score shouldBe 0.9953
    phoneRisk.get.reasonCodes should contain allOf(
      ReasonCode("R620"),
      ReasonCode("R606"),
      ReasonCode("I611"),
      ReasonCode("I618"),
      ReasonCode("I602"),
      ReasonCode("I609")
    )
  }
}
