package me.socure.transactionauditing.common.model

import org.scalatest.{FunSuite, Matchers}

import scala.io.Source

class ResponseCodecsTest extends FunSuite with Matchers {
  val v3_0Response: String = Source.fromURL(getClass.getResource("/sample_response_3_0_full.json")).mkString
  val v2_5Response: String = Source.fromURL(getClass.getResource("/sample_response_2_5.json")).mkString

  test("test something") {
    1 + 1 shouldBe 2
  }
}
