package me.socure.transactionauditing.common.model

import argonaut.Parse
import me.socure.transactionauditing.common.model.response.FraudModelScore
import me.socure.transactionauditing.common.model.response.v3_0.TransactionResponseCodecsV3_0._
import me.socure.transactionauditing.common.model.response.v3_0.TransactionResponseV3_0
import org.scalatest.{FunSuite, Matchers}

import scala.io.Source
import scalaz.{-\/, \/-}

class AuthResponseCodecTest extends FunSuite with Matchers {
  val authResponse3_0: String = Source.fromURL(getClass.getResource("/sample_response_authscore_3_0.json")).mkString

  test("should parse authscore correctly") {
    val responseOpt = Option(authResponse3_0).map(_.trim).filter(_.nonEmpty).flatMap(Parse.decodeEither[TransactionResponseV3_0](_) match {
      case \/-(debug) => Some(debug)
      case -\/(err) =>
        throw new Exception(err)
    })

    val authScoreResponse = responseOpt.flatMap(_.authenticity).get

    authScoreResponse.score shouldBe 7
    authScoreResponse.reasonCodes should contain allOf (
      ReasonCode("I708"),
      ReasonCode("I618"),
      ReasonCode("R551")
    )

    val fraudResponseReasonCodes = responseOpt.flatMap(_.fraud).map(_.reasonCodes)
    val fraudResponseScores = responseOpt.flatMap(_.fraud).map(_.scores)

    fraudResponseReasonCodes.get should contain allOf(
      ReasonCode("I704"),
      ReasonCode("I708"),
      ReasonCode("I618"),
      ReasonCode("I609"),
      ReasonCode("I707"),
      ReasonCode("R551"),
      ReasonCode("R606"),
      ReasonCode("I611"),
      ReasonCode("I602")
    )
    fraudResponseScores shouldBe Some(
      List(
        FraudModelScore(
          name = "generic",
          version = "3.0",
          score = 0.0697
        ),
        FraudModelScore(
          name = "My TestModel-1",
          version = "11.1",
          score = 0.1665
        )
      )
    )
  }
}
