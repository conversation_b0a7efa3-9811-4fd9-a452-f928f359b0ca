package me.socure.transactionauditing.common.model

import me.socure.transactionauditing.common.domain.ApiTransaction
import me.socure.transactionauditing.common.domain.fixture.APITransactionValueGenerator
import me.socure.transactionauditing.common.model.response.v1_0.watchlist.TransactionWatchlistResponseV1_0
import me.socure.transactionauditing.common.model.response.v2_5.TransactionResponseV2_5
import me.socure.transactionauditing.common.model.response.v3_0.{AlertListMatch, AlertListResponse, DecisionResponse, DocumentVerification, TransactionResponseV3_0}
import org.scalatest.{FunSuite, Matchers}

import scala.io.Source

class TransactionResponseParserTest extends FunSuite with Matchers {
  val v2_5Response: String = Source.fromURL(getClass.getResource("/sample_response_2_5.json")).mkString
  val v3_0Response: String = Source.fromURL(getClass.getResource("/sample_response_3_0_full.json")).mkString
  val v3_0ResponseWithAlertList: String = Source.fromURL(getClass.getResource("/sample_response_3_0_alertlist.json")).mkString
  val v1_0Watchlist: String = Source.fromURL(getClass.getResource("/sample_response_watchlist_1_0.json")).mkString

  test("test 2.5 response") {
    val transaction: ApiTransaction = APITransactionValueGenerator.aAPITransaction(
      apiName = Some("2.5"),
      transactionId = "259264e6-2487-45f9-8a2e-45f7c04ff9f7",
      response = Some(v2_5Response))
    val response = TransactionResponseParser.parse(transaction)

    response match {
      case Some(e: TransactionResponseV2_5) => e.referenceId shouldBe transaction.transactionId
    }
  }

  test("test 3.0 response") {
    val transaction: ApiTransaction = APITransactionValueGenerator.aAPITransaction(
      apiName = Some("3.0"),
      transactionId = "01d1bcbc-b0e2-4772-a050-c212d57ddd17",
      response = Some(v3_0Response))
    val response = TransactionResponseParser.parse(transaction)

    val expectedDecision = DecisionResponse(
      modelName = Some("Best Practice"),
      modelVersion = Some("1.0"),
      value = Some("accept"),
      response = None
    )

    val expectedDocumentVerification = DocumentVerification(
      reasonCodes = Set(
        "R824",
        "R827",
        "I803",
        "R823",
        "R825",
        "I805"
      ).map(ReasonCode),
      documentType = "Drivers License",
      documentVersion = None,
      country = "US",
      state = Some("NY"),
      decisionName = "lenient",
      decisionValue = "review",
      firstName = None,
      surName = Some("DOCUMENT"),
      fullName = Some("LICENSE DOCUMENT"),
      address = Some("2345 ANYPLACE AVE ANYTOWN,NY 12345"),
      documentNumber = Some("012345678"),
      dob = Some("1985-06-09"),
      expirationDate = Some("2016-10-01"),
      issueDate = Some("2008-09-30")
    )

    response match {
      case Some(e: TransactionResponseV3_0) => {
        e.referenceId shouldBe transaction.transactionId
        e.decision shouldBe Some(expectedDecision)
        e.documentVerification shouldBe Some(expectedDocumentVerification)
      }
    }
  }

  test("test 1.0 watchlist response") {
    val transaction: ApiTransaction = APITransactionValueGenerator.aAPITransaction(
      apiName = Some("1/Watchlist"),
      transactionId = "31a8b232-9fc7-4d83-a497-305c8a231667",
      response = Some(v1_0Watchlist),
      reasonCodes = Some("R186")
    )
    val response = TransactionResponseParser.parse(transaction)

    response match {
      case Some(e: TransactionWatchlistResponseV1_0) =>
        e.referenceId shouldBe transaction.transactionId
        e.reasonCodes shouldBe List(ReasonCode("R186"))
    }
  }

  test("test 3.0 response with AlertList") {
    val transaction: ApiTransaction = APITransactionValueGenerator.aAPITransaction(
      apiName = Some("3.0"),
      transactionId = "01d1bcbc-b0e2-4772-a050-c212d57ddd17",
      response = Some(v3_0ResponseWithAlertList))
    val response = TransactionResponseParser.parse(transaction)

    response match {
      case Some(e: TransactionResponseV3_0) =>
        e.referenceId shouldBe transaction.transactionId
        val alertListResponse = AlertListResponse(
          reasonCodes = Set("R113", "R111").map(ReasonCode),
          matches = Set(
            AlertListMatch(
              element = "ssn",
              datasetName = "bancorp",
              reason = Some("Chargeback Fraud"),
              industryName = Some("E-Commerce"),
              lastReportedDate = Some("2018-06-01"),
              reportedCount = Some(2)
            ),
            AlertListMatch(
              element = "ssn",
              datasetName = "consortium",
              reason = Some("Chargeback Fraud"),
              industryName = None,
              lastReportedDate = Some("2018-06-01"),
              reportedCount = None
            ),
            AlertListMatch(
              element = "mobilenumber",
              datasetName = "consortium",
              reason = Some("Chargeback Fraud"),
              industryName = Some("E-Commerce"),
              lastReportedDate = Some("1999-05-09"),
              reportedCount = None
            ),
            AlertListMatch(
              element = "email",
              datasetName = "consortium",
              reason = Some("Chargeback Fraud"),
              industryName = Some("E-Commerce"),
              lastReportedDate = Some("1999-05-09"),
              reportedCount = None
            ),
            AlertListMatch(
              element = "ipaddress",
              datasetName = "consortium",
              reason = None,
              industryName = None,
              lastReportedDate = None,
              reportedCount = None
            )
          )
        )

        e.alertList shouldBe defined
        e.alertList.get shouldBe alertListResponse
    }
  }
}
