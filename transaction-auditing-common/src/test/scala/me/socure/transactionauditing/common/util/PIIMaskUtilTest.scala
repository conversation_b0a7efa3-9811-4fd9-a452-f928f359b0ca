package me.socure.transactionauditing.common.util

import org.scalatest.{FunSuite, Matchers}

/**
 * Created by <PERSON><PERSON><PERSON> on Sep 14, 2022
 */
class PIIMaskUtilTest extends FunSuite with Matchers {

  private val kycPlusResponse = "{\"referenceId\":\"ecbf0a3f-f1dd-47a6-9861-d03a43aa6630\",\"kycPlus\":{\"reasonCodes\":[\"I920\",\"R948\",\"R934\",\"R902\",\"R916\",\"R930\"],\"fieldValidations\":{\"firstName\":0.01,\"surName\":0.99,\"streetAddress\":0.99,\"city\":0.01,\"state\":0.99,\"zip\":0.01,\"mobileNumber\":0.99,\"dob\":0.99,\"ssn\":0.99},\"bestMatchedEntity\":{\"firstName\":\"clarissa\",\"middleName\":\"n\",\"surName\":\"brooks\",\"suffix\":\"\",\"ssn\":\"538136973\",\"dob\":\"1963-08-25\",\"mobileNumber\":\"5098345889\",\"normalizedAddress\":{\"streetAddress\":\"\",\"city\":\"\",\"state\":\"\",\"zip\":\"\"},\"associatedPhoneNumbers\":[{\"phoneNumber\":\"5098345889\"}],\"associatedAddresses\":[{\"streetAddress\":\"3138 sun valley road\",\"city\":\"yakima\",\"state\":\"wa\",\"zip\":\"98902\"}]}}}"
  private val kycPlusResponseMasked = "{\"referenceId\":\"ecbf0a3f-f1dd-47a6-9861-d03a43aa6630\",\"kycPlus\":{\"reasonCodes\":[\"I920\",\"R948\",\"R934\",\"R902\",\"R916\",\"R930\"],\"fieldValidations\":{\"firstName\":0.01,\"surName\":0.99,\"streetAddress\":0.99,\"city\":0.01,\"state\":0.99,\"zip\":0.01,\"mobileNumber\":0.99,\"dob\":0.99,\"ssn\":0.99},\"bestMatchedEntity\":{\"firstName\":\"clarissa\",\"middleName\":\"n\",\"surName\":\"brooks\",\"suffix\":\"\",\"ssn\":\"******\",\"dob\":\"******\",\"mobileNumber\":\"5098345889\",\"normalizedAddress\":{\"streetAddress\":\"\",\"city\":\"\",\"state\":\"\",\"zip\":\"\"},\"associatedPhoneNumbers\":[{\"phoneNumber\":\"5098345889\"}],\"associatedAddresses\":[{\"streetAddress\":\"3138 sun valley road\",\"city\":\"yakima\",\"state\":\"wa\",\"zip\":\"98902\"}]}}}"

  private val kycResponse = "{\"referenceId\":\"216c3884-aec2-41f6-8e98-d2ecedb36039\",\"kyc\":{\"reasonCodes\":[\"I920\",\"R933\",\"I906\",\"R919\",\"R948\",\"R934\",\"R902\",\"R961\",\"R901\"],\"fieldValidations\":{\"firstName\":0.01,\"surName\":0.01,\"streetAddress\":0.01,\"city\":0.01,\"state\":0.01,\"zip\":0.01,\"ssn\":0.01}}}"

  test("should mask pii field in bestMatchedEntity") {
    PIIMaskUtil.maskPiiInResponse(kycPlusResponse) shouldBe kycPlusResponseMasked
  }

  test("should not mask pii field if not present") {
    PIIMaskUtil.maskPiiInResponse(kycResponse) shouldBe kycResponse
  }
}
