[{"rule_codes": [{"rulecode": "scoreComponents.LIVAL.200131", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "LIVAL.200131"}, {"rulecode": "scoreComponents.LIVAL.200126", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "LIVAL.200126"}, {"rulecode": "scoreComponents.COVAL.400013", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "COVAL.400013"}, {"rulecode": "scoreComponents.COVAL.400016", "score": "0.0", "confidence": "0.0", "originalScore": "0.9915", "ruleCodeShort": "COVAL.400016"}, {"rulecode": "scoreComponents.COVAL.400010", "score": "0.0", "confidence": "0.0", "originalScore": "0.9463", "ruleCodeShort": "COVAL.400010"}, {"rulecode": "scoreComponents.COVAL.400014", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "COVAL.400014"}, {"rulecode": "scoreComponents.COVAL.400021", "score": "0.0", "confidence": "0.0", "originalScore": "0.9792", "ruleCodeShort": "COVAL.400021"}, {"rulecode": "scoreComponents.COVAL.400011", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "COVAL.400011"}, {"rulecode": "scoreComponents.COVAL.400022", "score": "0.0", "confidence": "0.0", "originalScore": "0.9731", "ruleCodeShort": "COVAL.400022"}, {"rulecode": "scoreComponents.COVAL.400019", "score": "0.0", "confidence": "0.0", "originalScore": "0.9957", "ruleCodeShort": "COVAL.400019"}, {"rulecode": "scoreComponents.COVAL.400020", "score": "0.0", "confidence": "0.0", "originalScore": "0.9689", "ruleCodeShort": "COVAL.400020"}, {"rulecode": "scoreComponents.COVAL.400017", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "COVAL.400017"}, {"rulecode": "scoreComponents.HDVAL.100001", "score": "0.0", "confidence": "0.0", "originalScore": "100.0", "ruleCodeShort": "HDVAL.100001"}, {"rulecode": "scoreComponents.HDVAL.100028", "score": "0.0", "confidence": "0.0", "originalScore": "3.0", "ruleCodeShort": "HDVAL.100028"}, {"rulecode": "scoreComponents.HDVAL.100003", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "HDVAL.100003"}, {"rulecode": "scoreComponents.HDVAL.100004", "score": "0.0", "confidence": "0.0", "originalScore": "61.0", "ruleCodeShort": "HDVAL.100004"}, {"rulecode": "scoreComponents.HDVAL.100027", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "HDVAL.100027"}, {"rulecode": "scoreComponents.IFVAL.100005", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100005"}, {"rulecode": "scoreComponents.IFVAL.100006", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100006"}, {"rulecode": "scoreComponents.IFVAL.200003", "score": "0.0", "confidence": "0.0", "originalScore": "100.0", "ruleCodeShort": "IFVAL.200003"}, {"rulecode": "scoreComponents.IFVAL.300031", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.300031"}, {"rulecode": "scoreComponents.IFVAL.200006", "score": "0.0", "confidence": "0.0", "originalScore": "10.0", "ruleCodeShort": "IFVAL.200006"}, {"rulecode": "scoreComponents.IFVAL.200014", "score": "0.0", "confidence": "0.0", "originalScore": "100.0", "ruleCodeShort": "IFVAL.200014"}, {"rulecode": "scoreComponents.IFVAL.100001", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100001"}, {"rulecode": "scoreComponents.IFVAL.200009", "score": "0.0", "confidence": "0.0", "originalScore": "100.0", "ruleCodeShort": "IFVAL.200009"}, {"rulecode": "scoreComponents.IFVAL.100039", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100039"}, {"rulecode": "scoreComponents.IFVAL.200002", "score": "0.0", "confidence": "0.0", "originalScore": "10.0", "ruleCodeShort": "IFVAL.200002"}, {"rulecode": "scoreComponents.IFVAL.300064", "score": "0.0", "confidence": "0.0", "originalScore": "4136.2841", "ruleCodeShort": "IFVAL.300064"}, {"rulecode": "scoreComponents.IFVAL.100002", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100002"}, {"rulecode": "scoreComponents.IFVAL.400002", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.400002"}, {"rulecode": "scoreComponents.IFVAL.100040", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100040"}, {"rulecode": "scoreComponents.IFVAL.200013", "score": "0.0", "confidence": "0.0", "originalScore": "3.7", "ruleCodeShort": "IFVAL.200013"}, {"rulecode": "scoreComponents.IFVAL.400003", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.400003"}, {"rulecode": "scoreComponents.IFVAL.300054", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.300054"}, {"rulecode": "scoreComponents.IFVAL.300021", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.300021"}, {"rulecode": "scoreComponents.IFVAL.300011", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.300011"}, {"rulecode": "scoreComponents.IFVAL.300024", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.300024"}, {"rulecode": "scoreComponents.IFVAL.300051", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.300051"}, {"rulecode": "scoreComponents.IFVAL.200001", "score": "0.0", "confidence": "0.0", "originalScore": "92.0", "ruleCodeShort": "IFVAL.200001"}, {"rulecode": "scoreComponents.IFVAL.300061", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.300061"}, {"rulecode": "scoreComponents.IFVAL.300014", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.300014"}, {"rulecode": "scoreComponents.IFVAL.200012", "score": "0.0", "confidence": "0.0", "originalScore": "70.0", "ruleCodeShort": "IFVAL.200012"}, {"rulecode": "scoreComponents.IFVAL.300072", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.300072"}, {"rulecode": "scoreComponents.IFVAL.200007", "score": "0.0", "confidence": "0.0", "originalScore": "10.0", "ruleCodeShort": "IFVAL.200007"}, {"rulecode": "scoreComponents.IFVAL.200015", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.200015"}, {"rulecode": "scoreComponents.IFVAL.400004", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.400004"}, {"rulecode": "scoreComponents.IFVAL.100042", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100042"}, {"rulecode": "scoreComponents.IFVAL.100003", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100003"}, {"rulecode": "scoreComponents.IFVAL.100037", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.100037"}, {"rulecode": "scoreComponents.IFVAL.300071", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.300071"}, {"rulecode": "scoreComponents.IFVAL.300041", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.300041"}, {"rulecode": "scoreComponents.IFVAL.200011", "score": "0.0", "confidence": "0.0", "originalScore": "100.0", "ruleCodeShort": "IFVAL.200011"}, {"rulecode": "scoreComponents.IFVAL.300044", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.300044"}, {"rulecode": "scoreComponents.IFVAL.100043", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100043"}, {"rulecode": "scoreComponents.IFVAL.200005", "score": "0.0", "confidence": "0.0", "originalScore": "80.0", "ruleCodeShort": "IFVAL.200005"}, {"rulecode": "scoreComponents.IFVAL.100004", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100004"}, {"rulecode": "scoreComponents.IFVAL.200010", "score": "0.0", "confidence": "0.0", "originalScore": "100.0", "ruleCodeShort": "IFVAL.200010"}, {"rulecode": "scoreComponents.IFVAL.200008", "score": "0.0", "confidence": "0.0", "originalScore": "2.8", "ruleCodeShort": "IFVAL.200008"}, {"rulecode": "scoreComponents.IFVAL.300004", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.300004"}, {"rulecode": "scoreComponents.IFVAL.100038", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100038"}, {"rulecode": "scoreComponents.IFVAL.200004", "score": "0.0", "confidence": "0.0", "originalScore": "100.0", "ruleCodeShort": "IFVAL.200004"}, {"rulecode": "scoreComponents.IFVAL.100041", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100041"}, {"rulecode": "scoreComponents.IFVAL.300034", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.300034"}, {"rulecode": "scoreComponents.IFVAL.300001", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.300001"}, {"rulecode": "scoreComponents.IFVAL.400001", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.400001"}, {"rulecode": "scoreComponents.WPVAL.300059", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.300059"}, {"rulecode": "scoreComponents.WPVAL.100013", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100013"}, {"rulecode": "scoreComponents.WPVAL.300048", "score": "0.0", "confidence": "0.0", "originalScore": "8188.0", "ruleCodeShort": "WPVAL.300048"}, {"rulecode": "scoreComponents.WPVAL.700047", "score": "0.0", "confidence": "0.0", "originalScore": "1606.0", "ruleCodeShort": "WPVAL.700047"}, {"rulecode": "scoreComponents.WPVAL.100035", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100035"}, {"rulecode": "scoreComponents.WPVAL.100050", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100050"}, {"rulecode": "scoreComponents.WPVAL.100039", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100039"}, {"rulecode": "scoreComponents.WPVAL.700053", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.700053"}, {"rulecode": "scoreComponents.WPVAL.100034", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100034"}, {"rulecode": "scoreComponents.WPVAL.100002", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100002"}, {"rulecode": "scoreComponents.WPVAL.100008", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100008"}, {"rulecode": "scoreComponents.WPVAL.100051", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100051"}, {"rulecode": "scoreComponents.WPVAL.100057", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100057"}, {"rulecode": "scoreComponents.WPVAL.100056", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100056"}, {"rulecode": "scoreComponents.WPVAL.300061", "score": "0.0", "confidence": "0.0", "originalScore": "0.8333", "ruleCodeShort": "WPVAL.300061"}, {"rulecode": "scoreComponents.WPVAL.700028", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.700028"}, {"rulecode": "scoreComponents.WPVAL.300066", "score": "0.0", "confidence": "0.0", "originalScore": "8188.0", "ruleCodeShort": "WPVAL.300066"}, {"rulecode": "scoreComponents.WPVAL.300046", "score": "0.0", "confidence": "0.0", "originalScore": "1606.0", "ruleCodeShort": "WPVAL.300046"}, {"rulecode": "scoreComponents.WPVAL.300065", "score": "0.0", "confidence": "0.0", "originalScore": "1606.0", "ruleCodeShort": "WPVAL.300065"}, {"rulecode": "scoreComponents.WPVAL.100031", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100031"}, {"rulecode": "scoreComponents.WPVAL.100026", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100026"}, {"rulecode": "scoreComponents.WPVAL.700025", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.700025"}, {"rulecode": "scoreComponents.WPVAL.100052", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100052"}, {"rulecode": "scoreComponents.WPVAL.100003", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100003"}, {"rulecode": "scoreComponents.WPVAL.100020", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100020"}, {"rulecode": "scoreComponents.WPVAL.100063", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100063"}, {"rulecode": "scoreComponents.WPVAL.100048", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100048"}, {"rulecode": "scoreComponents.WPVAL.300052", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.300052"}, {"rulecode": "scoreComponents.WPVAL.300060", "score": "0.0", "confidence": "0.0", "originalScore": "0.8333", "ruleCodeShort": "WPVAL.300060"}, {"rulecode": "scoreComponents.WPVAL.100025", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100025"}, {"rulecode": "scoreComponents.WPVAL.100021", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100021"}, {"rulecode": "scoreComponents.WPVAL.100060", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100060"}, {"rulecode": "scoreComponents.WPVAL.100004", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100004"}, {"rulecode": "scoreComponents.WPVAL.100027", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100027"}, {"rulecode": "scoreComponents.WPVAL.100049", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100049"}, {"rulecode": "scoreComponents.WPVAL.100053", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100053"}, {"rulecode": "scoreComponents.WPVAL.100007", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100007"}, {"rulecode": "scoreComponents.WPVAL.100030", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100030"}, {"rulecode": "scoreComponents.FVANL.400003", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FVANL.400003"}, {"rulecode": "scoreComponents.FVANL.400001", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FVANL.400001"}, {"rulecode": "scoreComponents.FBVAL.200131", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FBVAL.200131"}, {"rulecode": "scoreComponents.RKVAL.400002", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "RKVAL.400002"}, {"rulecode": "scoreComponents.RKVAL.400003", "score": "0.0", "confidence": "0.0", "originalScore": "0.1152", "ruleCodeShort": "RKVAL.400003"}, {"rulecode": "scoreComponents.RKVAL.400004", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "RKVAL.400004"}, {"rulecode": "scoreComponents.RKVAL.400008", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "RKVAL.400008"}, {"rulecode": "scoreComponents.RKVAL.400007", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "RKVAL.400007"}, {"rulecode": "scoreComponents.RKVAL.400001", "score": "0.0", "confidence": "0.0", "originalScore": "0.0085", "ruleCodeShort": "RKVAL.400001"}, {"rulecode": "scoreComponents.TWVAL.200131", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "TWVAL.200131"}, {"rulecode": "scoreComponents.TWVAL.200126", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "TWVAL.200126"}, {"rulecode": "scoreComponents.BLVAL.100001", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "BLVAL.100001"}, {"rulecode": "scoreComponents.NSVAL.300020", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.300020"}, {"rulecode": "scoreComponents.NSVAL.300009", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.300009"}, {"rulecode": "scoreComponents.NSVAL.100024", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100024"}, {"rulecode": "scoreComponents.NSVAL.100072", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100072"}, {"rulecode": "scoreComponents.NSVAL.300018", "score": "0.0", "confidence": "0.0", "originalScore": "85.0", "ruleCodeShort": "NSVAL.300018"}, {"rulecode": "scoreComponents.NSVAL.300026", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.300026"}, {"rulecode": "scoreComponents.NSVAL.100066", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100066"}, {"rulecode": "scoreComponents.NSVAL.100069", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100069"}, {"rulecode": "scoreComponents.NSVAL.100120", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100120"}, {"rulecode": "scoreComponents.NSVAL.100028", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100028"}, {"rulecode": "scoreComponents.NSVAL.100017", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100017"}, {"rulecode": "scoreComponents.NSVAL.100058", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100058"}, {"rulecode": "scoreComponents.NSVAL.100023", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100023"}, {"rulecode": "scoreComponents.NSVAL.100129", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100129"}, {"rulecode": "scoreComponents.NSVAL.300021", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.300021"}, {"rulecode": "scoreComponents.NSVAL.300032", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.300032"}, {"rulecode": "scoreComponents.NSVAL.100073", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100073"}, {"rulecode": "scoreComponents.NSVAL.300035", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.300035"}, {"rulecode": "scoreComponents.NSVAL.100125", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100125"}, {"rulecode": "scoreComponents.NSVAL.100068", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100068"}, {"rulecode": "scoreComponents.NSVAL.100132", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100132"}, {"rulecode": "scoreComponents.NSVAL.100121", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100121"}, {"rulecode": "scoreComponents.NSVAL.300019", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.300019"}, {"rulecode": "scoreComponents.NSVAL.100067", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100067"}, {"rulecode": "scoreComponents.NSVAL.100124", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100124"}, {"rulecode": "scoreComponents.NSVAL.100052", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100052"}, {"rulecode": "scoreComponents.NSVAL.100020", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100020"}, {"rulecode": "scoreComponents.NSVAL.100071", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100071"}, {"rulecode": "scoreComponents.NSVAL.300002", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.300002"}, {"rulecode": "scoreComponents.NSVAL.300033", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.300033"}, {"rulecode": "scoreComponents.NSVAL.300010", "score": "-0.0", "confidence": "0.0", "originalScore": "-1.0", "ruleCodeShort": "NSVAL.300010"}, {"rulecode": "scoreComponents.NSVAL.100133", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100133"}, {"rulecode": "scoreComponents.NSVAL.100027", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100027"}, {"rulecode": "scoreComponents.NSVAL.300017", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.300017"}, {"rulecode": "scoreComponents.NSVAL.100029", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100029"}, {"rulecode": "scoreComponents.NSVAL.100038", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100038"}, {"rulecode": "scoreComponents.NSVAL.300012", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.300012"}, {"rulecode": "scoreComponents.NSVAL.100053", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100053"}, {"rulecode": "scoreComponents.NSVAL.100015", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100015"}, {"rulecode": "scoreComponents.NSVAL.300001", "score": "0.0", "confidence": "0.0", "originalScore": "26.0", "ruleCodeShort": "NSVAL.300001"}, {"rulecode": "scoreComponents.NSVAL.100123", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100123"}, {"rulecode": "scoreComponents.NSVAL.100122", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100122"}, {"rulecode": "scoreComponents.FCVAL.300009", "score": "0.0", "confidence": "0.0", "originalScore": "5.0", "ruleCodeShort": "FCVAL.300009"}, {"rulecode": "scoreComponents.FCVAL.100611", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FCVAL.100611"}, {"rulecode": "scoreComponents.FCVAL.300011", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FCVAL.300011"}, {"rulecode": "scoreComponents.FCVAL.200128", "score": "1.01", "confidence": "0.1", "originalScore": "5.0", "ruleCodeShort": "FCVAL.200128"}, {"rulecode": "scoreComponents.FCVAL.300006", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FCVAL.300006"}, {"rulecode": "scoreComponents.FCVAL.300014", "score": "0.0", "confidence": "0.0", "originalScore": "9.0", "ruleCodeShort": "FCVAL.300014"}, {"rulecode": "scoreComponents.FCVAL.300003", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FCVAL.300003"}, {"rulecode": "scoreComponents.FCVAL.200277", "score": "0.0", "confidence": "0.0", "originalScore": "0.925", "ruleCodeShort": "FCVAL.200277"}, {"rulecode": "scoreComponents.FCVAL.300007", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FCVAL.300007"}, {"rulecode": "scoreComponents.FCVAL.300013", "score": "0.0", "confidence": "0.0", "originalScore": "2.0", "ruleCodeShort": "FCVAL.300013"}, {"rulecode": "scoreComponents.FCVAL.200127", "score": "0.0", "confidence": "0.0", "originalScore": "4.0", "ruleCodeShort": "FCVAL.200127"}, {"rulecode": "scoreComponents.FCVAL.300002", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FCVAL.300002"}, {"rulecode": "scoreComponents.FCVAL.300010", "score": "0.0", "confidence": "0.0", "originalScore": "2.0", "ruleCodeShort": "FCVAL.300010"}, {"rulecode": "scoreComponents.FCVAL.300008", "score": "0.0", "confidence": "0.0", "originalScore": "7.0", "ruleCodeShort": "FCVAL.300008"}, {"rulecode": "scoreComponents.FCVAL.300001", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FCVAL.300001"}, {"rulecode": "scoreComponents.GLOBAL.300087", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300087"}, {"rulecode": "scoreComponents.GLOBAL.300097", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300097"}, {"rulecode": "scoreComponents.GLOBAL.300090", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300090"}, {"rulecode": "scoreComponents.GLOBAL.300079", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300079"}, {"rulecode": "scoreComponents.GLOBAL.300062", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300062"}, {"rulecode": "scoreComponents.GLOBAL.300123", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300123"}, {"rulecode": "scoreComponents.GLOBAL.100143", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "GLOBAL.100143"}, {"rulecode": "scoreComponents.GLOBAL.300099", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300099"}, {"rulecode": "scoreComponents.GLOBAL.300088", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300088"}, {"rulecode": "scoreComponents.GLOBAL.300040", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300040"}, {"rulecode": "scoreComponents.GLOBAL.300072", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300072"}, {"rulecode": "scoreComponents.GLOBAL.300003", "score": "0.0", "confidence": "0.0", "originalScore": "5.0", "ruleCodeShort": "GLOBAL.300003"}, {"rulecode": "scoreComponents.GLOBAL.300106", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300106"}, {"rulecode": "scoreComponents.GLOBAL.300046", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300046"}, {"rulecode": "scoreComponents.GLOBAL.300122", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300122"}, {"rulecode": "scoreComponents.GLOBAL.300069", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "GLOBAL.300069"}, {"rulecode": "scoreComponents.GLOBAL.300060", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300060"}, {"rulecode": "scoreComponents.GLOBAL.300041", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300041"}, {"rulecode": "scoreComponents.GLOBAL.300082", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300082"}, {"rulecode": "scoreComponents.GLOBAL.300047", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300047"}, {"rulecode": "scoreComponents.GLOBAL.300067", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300067"}, {"rulecode": "scoreComponents.GLOBAL.300044", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300044"}, {"rulecode": "scoreComponents.GLOBAL.300080", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300080"}, {"rulecode": "scoreComponents.GLOBAL.300004", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300004"}, {"rulecode": "scoreComponents.GLOBAL.300045", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300045"}, {"rulecode": "scoreComponents.PNVAL.200131", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PNVAL.200131"}, {"rulecode": "scoreComponents.GPVAL.200131", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GPVAL.200131"}, {"rulecode": "scoreComponents.FMVAL.200150", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FMVAL.200150"}, {"rulecode": "scoreComponents.FMVAL.300018", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.300018"}, {"rulecode": "scoreComponents.FMVAL.100611", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.100611"}, {"rulecode": "scoreComponents.FMVAL.300021", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.300021"}, {"rulecode": "scoreComponents.FMVAL.300003", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.300003"}, {"rulecode": "scoreComponents.FMVAL.300005", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.300005"}, {"rulecode": "scoreComponents.FMVAL.300666", "score": "0.0", "confidence": "0.0", "originalScore": "42.0", "ruleCodeShort": "FMVAL.300666"}, {"rulecode": "scoreComponents.FMVAL.100140", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FMVAL.100140"}, {"rulecode": "scoreComponents.FMVAL.300002", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.300002"}, {"rulecode": "scoreComponents.FMVAL.300022", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.300022"}, {"rulecode": "scoreComponents.FMVAL.300017", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.300017"}, {"rulecode": "scoreComponents.FMVAL.300004", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FMVAL.300004"}, {"rulecode": "entityResolution.FMvWP.email", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMvWP.email"}, {"rulecode": "entityResolution.FMvWP.mobilenumber", "score": "0.0", "confidence": "0.0", "originalScore": "0.33", "ruleCodeShort": "FMvWP.mobilenumber"}, {"rulecode": "entityResolution.FMvNS.name", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMvNS.name"}, {"rulecode": "entityResolution.FMvNS.mobilenumber", "score": "0.0", "confidence": "0.0", "originalScore": "0.33", "ruleCodeShort": "FMvNS.mobilenumber"}, {"rulecode": "entityResolution.FMvNS.address", "score": "0.0", "confidence": "0.0", "originalScore": "0.7333", "ruleCodeShort": "FMvNS.address"}, {"rulecode": "entityResolution.FMvNS.email", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMvNS.email"}, {"rulecode": "entityResolution.FCvFM.name", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FCvFM.name"}, {"rulecode": "entityResolution.FCvFM.geocode", "score": "-1.51", "confidence": "0.075", "originalScore": "-1.0", "ruleCodeShort": "FCvFM.geocode"}, {"rulecode": "entityResolution.FCvFM.email", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FCvFM.email"}, {"rulecode": "entityResolution.FCvWP.email", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FCvWP.email"}, {"rulecode": "entityResolution.FCvNS.name", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FCvNS.name"}, {"rulecode": "entityResolution.FCvNS.email", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FCvNS.email"}, {"rulecode": "entityResolution.WPvNS.email", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPvNS.email"}, {"rulecode": "entityResolution.WPvNS.mobilenumber", "score": "0.0", "confidence": "0.0", "originalScore": "0.99", "ruleCodeShort": "WPvNS.mobilenumber"}], "raw_score": 4.5, "version_applied": "3", "fraud_score_info": {"primary": {"score": 0.0653796761095822, "model": {"id": -1, "name": "<PERSON><PERSON> (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/gen_us_30_20171129_adjust_gbm_v1", "identifier": "gen_us_30_20171129_adjust_gbm_v1", "version": "3.0", "created_date": 1515699348058, "last_updated_date": 1515699348058}, "params": null, "response": {"data": {"class0": 0.9346203238904178, "class1": 0.0653796761095822, "predictorType": "binomial"}, "status": "ok"}}, "sigma": {"score": 0.1653796761095822, "model": {"id": -1, "name": "<PERSON><PERSON> (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/gen_us_30_20171129_adjust_gbm_v1_sigma", "identifier": "gen_us_30_20171129_adjust_gbm_v1_sigma", "version": "5.0", "created_date": 1515699348058, "last_updated_date": 1515699348058}, "params": null, "response": {"data": {"class0": 0.9346203238904178, "class1": 0.1653796761095822, "predictorType": "binomial"}, "status": "ok"}}, "custom": {}}, "correlation_score_info": {"name_address": {"score": 0.9914661996876485, "model": {"id": -1, "name": "Correlation Model - Name vs Address (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/Name_Address_w9_HD_IF_corr_gbm_v2", "identifier": "Name_Address_w9_HD_IF_corr_gbm_v2", "version": "6.0", "created_date": 1515699348058, "last_updated_date": 1515699348058}, "params": {"data": {"IFVAL.200012": 70, "HDVAL.100022": null, "IFVAL.100002": 1, "HDVAL.100052": "Exact", "IFVAL.200004": 100, "IFVAL.100001": 1, "HDVAL.100024": "Exact", "HDVAL.100053": "Exact"}}, "response": {"data": {"class0": 0.008533800312351514, "class1": 0.9914661996876485, "predictorType": "binomial"}, "status": "ok"}}, "name_email": {"score": 0.9999792631276668, "model": {"id": -1, "name": "Correlation Model - Name vs Email (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/Combined_Trainset_glm_model_vI_HDWP_N_4", "identifier": "Combined_Trainset_glm_model_vI_HDWP_N_4", "version": "4.0", "created_date": 1515699348061, "last_updated_date": 1515699348061}, "params": {"data": {"WPVAL.300040": "true", "WPVAL.300039": "", "HDVAL.100052": "Exact", "HDVAL.100041": "None", "FCvFM.name": 1, "HDVAL.100053": "Exact", "WPVAL.300044": "Match", "FCVAL.100117": null}}, "response": {"data": {"class0": 2.073687233317134e-05, "class1": 0.9999792631276668, "predictorType": "binomial"}, "status": "ok"}}, "name_phone": {"score": 0.9462615894444752, "model": {"id": -1, "name": "Correlation Model - Name vs Phone (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/Name_Phone_prior80_WP_NS_corr_drf_v2", "identifier": "Name_Phone_prior80_WP_NS_corr_drf_v2", "version": "4.0", "created_date": 1515699348061, "last_updated_date": 1515699348061}, "params": {"data": {"WPVAL.300005": "Match", "NSVAL.300010": -1, "WPVAL.300056": "TRUE", "NSVAL.300017": 0, "NSVAL.300009": 1}}, "response": {"data": {"class0": 0.05373841055552475, "class1": 0.9462615894444752, "predictorType": "binomial"}, "status": "ok"}}}, "risk_score_info": {"address": {"score": 0.11518155603028522, "model": {"id": -1, "name": "Address Risk Model (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/addrrisk_oct2017_glm_model_vX", "identifier": "addrrisk_oct2017_glm_model_vX", "version": "3.0", "created_date": 1515699348063, "last_updated_date": 1515699348063}, "params": {"data": {"IFVAL.100003": 1, "IFVAL.100005": 1, "IFVAL.200012": 70, "IFVAL.100002": 1, "HDVAL.100052": "Exact", "IFVAL.200004": 100, "IFVAL.100001": 1, "HDVAL.100024": "Exact", "HDVAL.100028": 3, "HDVAL.100053": "Exact", "HDVAL.100027": 1, "IFVAL.100004": 1, "IFVAL.100006": 1}}, "response": {"data": {"class0": 0.8848184439697148, "class1": 0.11518155603028522, "predictorType": "binomial"}, "status": "ok"}}, "email": {"score": 0.008523373294829962, "model": {"id": -1, "name": "Email Risk Model (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/emailrisk_oct2017_gbm_model_v_23", "identifier": "emailrisk_oct2017_gbm_model_v_23", "version": "4.0", "created_date": 1515699348063, "last_updated_date": 1515699348063}, "params": {"data": {"WPVAL.300042": "false", "NSVAL.300019": 1, "NSVAL.300032": 1, "FMVAL.300003": 1, "WPVAL.300043": "false", "NSVAL.300018": 85, "WPVAL.300046": 1606, "WPVAL.300040": "true", "WPVAL.300039": "", "FMVAL.300002": 1, "FMVAL.300001": "gmail.com", "NSVAL.300020": 0, "FCvFM.name": 1, "WPVAL.300048": 8188, "WPVAL.300059": 1, "FMVAL.300004": 0, "WPVAL.300044": "Match"}}, "response": {"data": {"class0": 0.99147662670517, "class1": 0.008523373294829962, "predictorType": "binomial"}, "status": "ok"}}, "phone": {"score": 0.9999999689936944, "model": {"id": -1, "name": "Phone Risk Model (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/phone_risk_model_glm_v2", "identifier": "phone_risk_model_glm_v2", "version": "2.0", "created_date": 1515699348063, "last_updated_date": 1515699348063}, "params": {"data": {"WPVAL.300008": "Mobile", "WPVAL.300005": "Match", "BLVAL.100001": 1, "NSVAL.300011": "", "NSVAL.300024": "U", "WPVAL.300060": 0.8333333333333333, "NSVAL.300010": -1, "NSVAL.300025": "", "NSVAL.300009": 1}}, "response": {"data": {"class0": 3.1006305634129205e-08, "class1": 0.9999999689936944, "predictorType": "binomial"}, "status": "ok"}}}, "instance_id": "i-07dd33cc5d88087bc", "caches_used": ["CACHE_L1"], "categorical_values": [{"rulecode": "HDVAL.100052", "value": "Exact"}, {"rulecode": "HDVAL.100041", "value": "None"}, {"rulecode": "HDVAL.100051", "value": "None"}, {"rulecode": "HDVAL.100024", "value": "Exact"}, {"rulecode": "HDVAL.100002", "value": "Transaction Successful; Results found."}, {"rulecode": "HDVAL.100054", "value": "Not Input"}, {"rulecode": "HDVAL.100053", "value": "Exact"}, {"rulecode": "HDVAL.100055", "value": "None"}, {"rulecode": "IFVAL.100031", "value": "T-MOBILE USA, INC."}, {"rulecode": "IFVAL.100046", "value": "IP"}, {"rulecode": "IFVAL.100048", "value": "N"}, {"rulecode": "IFVAL.100034", "value": "2"}, {"rulecode": "IFVAL.100047", "value": "100IP"}, {"rulecode": "IFVAL.100032", "value": "W"}, {"rulecode": "IFVAL.100044", "value": "Y"}, {"rulecode": "IFVAL.100033", "value": "R"}, {"rulecode": "IFVAL.100045", "value": "100"}, {"rulecode": "WPVAL.300055", "value": "FALSE"}, {"rulecode": "WPVAL.300039", "value": ""}, {"rulecode": "WPVAL.300001", "value": ""}, {"rulecode": "WPVAL.300053", "value": "FALSE"}, {"rulecode": "WPVAL.300018", "value": "false"}, {"rulecode": "WPVAL.300040", "value": "true"}, {"rulecode": "WPVAL.300009", "value": "T-Mobile USA"}, {"rulecode": "WPVAL.300008", "value": "Mobile"}, {"rulecode": "WPVAL.300044", "value": "Match"}, {"rulecode": "WPVAL.300043", "value": "false"}, {"rulecode": "WPVAL.300042", "value": "false"}, {"rulecode": "WPVAL.300017", "value": "true"}, {"rulecode": "WPVAL.300005", "value": "Match"}, {"rulecode": "WPVAL.300006", "value": "Zip+4 match"}, {"rulecode": "WPVAL.300013", "value": "false"}, {"rulecode": "WPVAL.300056", "value": "TRUE"}, {"rulecode": "WPVAL.300041", "value": "Syntax OK, domain exists, and mailbox does not reject mail"}, {"rulecode": "WPVAL.300007", "value": "US"}, {"rulecode": "WPVAL.300016", "value": "Multi unit"}, {"rulecode": "WPVAL.300014", "value": "Missing unit/apt/suite number"}, {"rulecode": "WPVAL.300047", "value": "1995-08-13"}, {"rulecode": "WPVAL.300002", "value": "false"}, {"rulecode": "WPVAL.300015", "value": "Match"}, {"rulecode": "WPVAL.300067", "value": "true"}, {"rulecode": "WPVAL.300054", "value": "FALSE"}, {"rulecode": "WPVAL.300045", "value": "2013-08-20"}, {"rulecode": "NSVAL.300034", "value": "C"}, {"rulecode": "NSVAL.300011", "value": ""}, {"rulecode": "NSVAL.300006", "value": "N"}, {"rulecode": "NSVAL.300027", "value": ""}, {"rulecode": "NSVAL.300016", "value": "U"}, {"rulecode": "NSVAL.300024", "value": "U"}, {"rulecode": "NSVAL.300030", "value": "65"}, {"rulecode": "NSVAL.300023", "value": ""}, {"rulecode": "NSVAL.300004", "value": "H"}, {"rulecode": "NSVAL.300003", "value": "D"}, {"rulecode": "NSVAL.300007", "value": "N"}, {"rulecode": "NSVAL.300029", "value": "6"}, {"rulecode": "NSVAL.300025", "value": ""}, {"rulecode": "NSVAL.300008", "value": "N"}, {"rulecode": "NSVAL.300031", "value": "0"}, {"rulecode": "NSVAL.300005", "value": "R"}, {"rulecode": "NSVAL.300028", "value": ""}, {"rulecode": "NSVAL.300013", "value": ""}, {"rulecode": "NSVAL.300014", "value": ""}, {"rulecode": "NSVAL.300022", "value": ""}, {"rulecode": "NSVAL.300039", "value": "U"}, {"rulecode": "NSVAL.300015", "value": "D"}, {"rulecode": "FCVAL.300005", "value": "male"}, {"rulecode": "GLOBAL.300085", "value": "R"}, {"rulecode": "GLOBAL.300077", "value": "Residential"}, {"rulecode": "GLOBAL.300064", "value": "H"}, {"rulecode": "GLOBAL.300070", "value": "Wireless"}, {"rulecode": "GLOBAL.300130", "value": "Street match"}, {"rulecode": "FMVAL.300001", "value": "gmail.com"}, {"rulecode": "FMVAL.400006", "value": "20180112"}], "query_plan": {"vendors": ["COVAL", "IFVAL", "BLVAL", "HDVAL", "FMVAL", "WPVAL", "GLOBAL", "FVANL", "RKVAL", "NSVAL", "FCVAL"], "id_plus_models": {"fraud_models": {"primary": {"id": -1, "name": "<PERSON><PERSON> (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/gen_us_30_20171129_adjust_gbm_v1", "identifier": "gen_us_30_20171129_adjust_gbm_v1", "version": "3.0", "created_date": 1515699348058, "last_updated_date": 1515699348058}, "secondary": []}, "correlation_models": {"name_email": {"id": -1, "name": "Correlation Model - Name vs Email (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/Combined_Trainset_glm_model_vI_HDWP_N_4", "identifier": "Combined_Trainset_glm_model_vI_HDWP_N_4", "version": "4.0", "created_date": 1515699348061, "last_updated_date": 1515699348061}, "name_address": {"id": -1, "name": "Correlation Model - Name vs Address (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/Name_Address_w9_HD_IF_corr_gbm_v2", "identifier": "Name_Address_w9_HD_IF_corr_gbm_v2", "version": "6.0", "created_date": 1515699348058, "last_updated_date": 1515699348058}, "name_phone": {"id": -1, "name": "Correlation Model - Name vs Phone (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/Name_Phone_prior80_WP_NS_corr_drf_v2", "identifier": "Name_Phone_prior80_WP_NS_corr_drf_v2", "version": "4.0", "created_date": 1515699348061, "last_updated_date": 1515699348061}}, "risk_models": {"address": {"id": -1, "name": "Address Risk Model (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/addrrisk_oct2017_glm_model_vX", "identifier": "addrrisk_oct2017_glm_model_vX", "version": "3.0", "created_date": 1515699348063, "last_updated_date": 1515699348063}, "email": {"id": -1, "name": "Email Risk Model (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/emailrisk_oct2017_gbm_model_v_23", "identifier": "emailrisk_oct2017_gbm_model_v_23", "version": "4.0", "created_date": 1515699348063, "last_updated_date": 1515699348063}, "phone": {"id": -1, "name": "Phone Risk Model (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/phone_risk_model_glm_v2", "identifier": "phone_risk_model_glm_v2", "version": "2.0", "created_date": 1515699348063, "last_updated_date": 1515699348063}}}, "id_plus_features": {"requested_features": ["FraudScore", "AllCorrelations"], "resolved_features": ["FraudScore", "Social", "EmailRiskScore", "AllCorrelations", "NameEmailCorrelation", "NamePhoneCorrelation", "NameAddressCorrelation", "AddressRiskScore", "Blacklist", "PhoneRiskScore"], "outputted_features": ["FraudScore", "Social", "EmailRiskScore", "AllCorrelations", "NameEmailCorrelation", "NamePhoneCorrelation", "NameAddressCorrelation", "AddressRiskScore", "Blacklist", "PhoneRiskScore"]}, "scheduler_timeout": {"length": 5000, "unit": "MILLISECONDS"}, "vendor_timeouts": {"IFVAL": {"length": 4000, "unit": "MILLISECONDS"}, "BLVAL": {"length": 4000, "unit": "MILLISECONDS"}, "HDVAL": {"length": 4000, "unit": "MILLISECONDS"}, "FMVAL": {"length": 5000, "unit": "MILLISECONDS"}, "WPVAL": {"length": 5000, "unit": "MILLISECONDS"}, "FVANL": {"length": 5000, "unit": "MILLISECONDS"}, "NSVAL": {"length": 5000, "unit": "MILLISECONDS"}, "FCVAL": {"length": 5000, "unit": "MILLISECONDS"}}}, "http_status": 200, "reason_codes": ["I556", "I618", "I708", "I520", "I720", "R620", "R106", "I127", "I707", "I553", "I609", "I555", "I602", "R606", "I121", "I611", "I704"]}]