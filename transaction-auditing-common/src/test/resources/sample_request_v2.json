{"requestParams": {"pagination": {"page": 1, "size": 5}, "sorting": [{"column": "transactionDate", "ascending": false}], "filters": {"columns": {"accountId": 3517, "startDate": "2022-01-01 00:00:00.000+0000", "endDate": "2022-03-17 00:00:00.000+0000", "product": ["/api/3.0/EmailAuthScore"], "isError": false, "hasRiskCode": true}, "custom": {"modules": {"filterType": "Any", "values": ["watchlists<PERSON><PERSON><PERSON>", "watchlistpremier", "watchlistplus"]}, "reasoncodes": {"filterType": "Any", "values": ["R186"]}}}}, "responseParams": {"columns": ["reasonCodes", "parameters", "transactionDate"]}}