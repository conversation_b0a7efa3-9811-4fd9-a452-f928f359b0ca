{"referenceId": "01d1bcbc-b0e2-4772-a050-c212d57ddd17", "authenticity": {"score": 7, "reasonCodes": ["I708", "I618", "R551"]}, "blacklist": {"reasonCodes": [], "matches": []}, "nameAddressCorrelation": {"reasonCodes": ["I708"], "score": 0.9915}, "nameEmailCorrelation": {"reasonCodes": ["R551"], "score": 0.3075}, "namePhoneCorrelation": {"reasonCodes": ["I618"], "score": 0.9463}, "fraud": {"reasonCodes": ["I704", "I708", "I618", "I609", "I707", "R551", "R606", "I611", "I602"], "scores": [{"name": "generic", "version": "3.0", "score": 0.0674}, {"name": "My TestModel-1", "version": "11.1", "score": 0.1665}]}, "kyc": {"reasonCodes": ["I914", "R905", "I917", "R915", "I905"], "cvi": 40, "correlationIndices": {"nameAddressPhoneIndex": 8.0, "nameAddressSsnIndex": 8.0}, "decision": {"modelName": "SOCURE_FIX", "modelVersion": "1.0", "status": "REFER"}, "fieldValidations": {"firstName": 0.99, "surName": 0.99, "streetAddress": 0.99, "city": 0.99, "state": 0.99, "zip": 0.99, "mobileNumber": 0.01, "dob": 0.99}}, "social": {"profilesFound": [], "reasonCodes": []}, "addressRisk": {"score": 0.1152, "reasonCodes": ["I704", "I708", "I720", "I707"]}, "emailRisk": {"score": 0.9945, "reasonCodes": ["R551", "R520"]}, "phoneRisk": {"score": 0.9953, "reasonCodes": ["R620", "R606", "I611", "I618", "I602", "I609"]}, "watchlist": {"reasonCodes": ["I196"], "matches": {}}, "alertList": {"reasonCodes": ["R113", "R111"], "matches": [{"element": "ssn", "datasetName": "bancorp", "reason": "Chargeback <PERSON>", "industryName": "E-Commerce", "lastReportedDate": "2018-06-01", "reportedCount": 2}, {"element": "ssn", "datasetName": "consortium", "reason": "Chargeback <PERSON>", "lastReportedDate": "2018-06-01"}, {"element": "mobilenumber", "datasetName": "consortium", "reason": "Chargeback <PERSON>", "industryName": "E-Commerce", "lastReportedDate": "1999-05-09"}, {"element": "email", "datasetName": "consortium", "reason": "Chargeback <PERSON>", "industryName": "E-Commerce", "lastReportedDate": "1999-05-09"}, {"element": "ipaddress", "datasetName": "consortium"}]}, "debug": {"rule_codes": [{"rulecode": "scoreComponents.PBVAL.200272", "score": "-0.1", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.200272"}, {"rulecode": "scoreComponents.IFVAL.100042", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100042"}, {"rulecode": "scoreComponents.PBVAL.200216", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200216"}, {"rulecode": "scoreComponents.NSVAL.300001", "score": "0.0", "confidence": "0.0", "originalScore": "72.0", "ruleCodeShort": "NSVAL.300001"}, {"rulecode": "scoreComponents.WPVAL.100056", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100056"}, {"rulecode": "scoreComponents.LNVAL.100128", "score": "0.0", "confidence": "0.0", "originalScore": "8.0", "ruleCodeShort": "LNVAL.100128"}, {"rulecode": "scoreComponents.NSVAL.300019", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.300019"}, {"rulecode": "scoreComponents.ASANL.100502", "score": "0.1", "confidence": "0.0", "originalScore": "0.5", "ruleCodeShort": "ASANL.100502"}, {"rulecode": "scoreComponents.NSVAL.100067", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100067"}, {"rulecode": "scoreComponents.IFVAL.200010", "score": "0.0", "confidence": "0.0", "originalScore": "100.0", "ruleCodeShort": "IFVAL.200010"}, {"rulecode": "scoreComponents.FMVAL.300002", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.300002"}, {"rulecode": "scoreComponents.IFVAL.200001", "score": "0.0", "confidence": "0.0", "originalScore": "92.0", "ruleCodeShort": "IFVAL.200001"}, {"rulecode": "scoreComponents.WPVAL.300060", "score": "0.0", "confidence": "0.0", "originalScore": "0.8333", "ruleCodeShort": "WPVAL.300060"}, {"rulecode": "scoreComponents.RKVAL.400007", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "RKVAL.400007"}, {"rulecode": "scoreComponents.WPVAL.700025", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.700025"}, {"rulecode": "scoreComponents.WPVAL.100026", "score": "0.02", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100026"}, {"rulecode": "scoreComponents.PBVAL.200205", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200205"}, {"rulecode": "scoreComponents.NSVAL.300035", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.300035"}, {"rulecode": "scoreComponents.IFVAL.100001", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100001"}, {"rulecode": "scoreComponents.GLOBAL.300037", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300037"}, {"rulecode": "scoreComponents.GLOBAL.300067", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300067"}, {"rulecode": "scoreComponents.WPVAL.100025", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100025"}, {"rulecode": "scoreComponents.WPVAL.300052", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.300052"}, {"rulecode": "scoreComponents.GLOBAL.300090", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "GLOBAL.300090"}, {"rulecode": "scoreComponents.COVAL.400016", "score": "0.0", "confidence": "0.0", "originalScore": "0.9915", "ruleCodeShort": "COVAL.400016"}, {"rulecode": "scoreComponents.IFVAL.200014", "score": "0.0", "confidence": "0.0", "originalScore": "100.0", "ruleCodeShort": "IFVAL.200014"}, {"rulecode": "scoreComponents.NSVAL.100125", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100125"}, {"rulecode": "scoreComponents.WPVAL.100021", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100021"}, {"rulecode": "scoreComponents.PBVAL.100120", "score": "0.3", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.100120"}, {"rulecode": "scoreComponents.FMVAL.100611", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.100611"}, {"rulecode": "scoreComponents.WPVAL.700047", "score": "0.0", "confidence": "0.0", "originalScore": "6.0", "ruleCodeShort": "WPVAL.700047"}, {"rulecode": "scoreComponents.GLOBAL.300003", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "GLOBAL.300003"}, {"rulecode": "scoreComponents.WPVAL.700028", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.700028"}, {"rulecode": "scoreComponents.IFVAL.100039", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100039"}, {"rulecode": "scoreComponents.WPVAL.100027", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100027"}, {"rulecode": "scoreComponents.WPVAL.100063", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100063"}, {"rulecode": "scoreComponents.IFVAL.200006", "score": "0.0", "confidence": "0.0", "originalScore": "10.0", "ruleCodeShort": "IFVAL.200006"}, {"rulecode": "scoreComponents.PBVAL.200246", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200246"}, {"rulecode": "scoreComponents.PBVAL.200252", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200252"}, {"rulecode": "scoreComponents.HDVAL.100027", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "HDVAL.100027"}, {"rulecode": "scoreComponents.PBVAL.200201", "score": "0.1", "confidence": "0.0", "originalScore": "2.0", "ruleCodeShort": "PBVAL.200201"}, {"rulecode": "scoreComponents.GLOBAL.300072", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300072"}, {"rulecode": "scoreComponents.GLOBAL.100143", "score": "-0.5", "confidence": "0.05", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.100143"}, {"rulecode": "scoreComponents.NSVAL.100023", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100023"}, {"rulecode": "scoreComponents.NSVAL.300012", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.300012"}, {"rulecode": "scoreComponents.FMVAL.300005", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.300005"}, {"rulecode": "scoreComponents.WPVAL.100048", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100048"}, {"rulecode": "scoreComponents.FMVAL.100140", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FMVAL.100140"}, {"rulecode": "scoreComponents.LNVAL.100108", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "LNVAL.100108"}, {"rulecode": "scoreComponents.IFVAL.100038", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100038"}, {"rulecode": "scoreComponents.GLOBAL.300004", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300004"}, {"rulecode": "scoreComponents.IFVAL.200004", "score": "0.0", "confidence": "0.0", "originalScore": "100.0", "ruleCodeShort": "IFVAL.200004"}, {"rulecode": "scoreComponents.GLOBAL.300088", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300088"}, {"rulecode": "scoreComponents.NSVAL.100068", "score": "0.02", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100068"}, {"rulecode": "scoreComponents.WPVAL.100020", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100020"}, {"rulecode": "scoreComponents.GLOBAL.300087", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300087"}, {"rulecode": "scoreComponents.IFVAL.100043", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100043"}, {"rulecode": "scoreComponents.FMVAL.300003", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.300003"}, {"rulecode": "scoreComponents.PBVAL.200278", "score": "0.0", "confidence": "0.0", "originalScore": "42.0", "ruleCodeShort": "PBVAL.200278"}, {"rulecode": "scoreComponents.IFVAL.300014", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.300014"}, {"rulecode": "scoreComponents.WPVAL.100008", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100008"}, {"rulecode": "scoreComponents.IFVAL.300071", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.300071"}, {"rulecode": "scoreComponents.WPVAL.300066", "score": "0.0", "confidence": "0.0", "originalScore": "8195.0", "ruleCodeShort": "WPVAL.300066"}, {"rulecode": "scoreComponents.WPVAL.300048", "score": "0.0", "confidence": "0.0", "originalScore": "8195.0", "ruleCodeShort": "WPVAL.300048"}, {"rulecode": "scoreComponents.PBVAL.200211", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200211"}, {"rulecode": "scoreComponents.GLOBAL.300079", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300079"}, {"rulecode": "scoreComponents.IFVAL.300041", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.300041"}, {"rulecode": "scoreComponents.PBVAL.200256", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200256"}, {"rulecode": "scoreComponents.HDVAL.100001", "score": "0.0", "confidence": "0.0", "originalScore": "100.0", "ruleCodeShort": "HDVAL.100001"}, {"rulecode": "scoreComponents.GLOBAL.300044", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300044"}, {"rulecode": "scoreComponents.GLOBAL.300080", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300080"}, {"rulecode": "scoreComponents.NSVAL.300017", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.300017"}, {"rulecode": "scoreComponents.IFVAL.300051", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.300051"}, {"rulecode": "scoreComponents.NSVAL.100124", "score": "-0.02", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100124"}, {"rulecode": "scoreComponents.IFVAL.300001", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.300001"}, {"rulecode": "scoreComponents.NSVAL.300018", "score": "0.0", "confidence": "0.0", "originalScore": "80.0", "ruleCodeShort": "NSVAL.300018"}, {"rulecode": "scoreComponents.FVANL.400003", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FVANL.400003"}, {"rulecode": "scoreComponents.NSVAL.100029", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100029"}, {"rulecode": "scoreComponents.HDVAL.100003", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "HDVAL.100003"}, {"rulecode": "scoreComponents.COVAL.400013", "score": "0.0", "confidence": "0.0", "originalScore": "0.3075", "ruleCodeShort": "COVAL.400013"}, {"rulecode": "scoreComponents.PBVAL.200266", "score": "-0.1", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.200266"}, {"rulecode": "scoreComponents.LNVAL.100004", "score": "0.0", "confidence": "0.0", "originalScore": "0.4", "ruleCodeShort": "LNVAL.100004"}, {"rulecode": "scoreComponents.WPVAL.100035", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100035"}, {"rulecode": "scoreComponents.PBVAL.200277", "score": "0.0", "confidence": "0.0", "originalScore": "0.66", "ruleCodeShort": "PBVAL.200277"}, {"rulecode": "scoreComponents.NSVAL.100123", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100123"}, {"rulecode": "scoreComponents.COVAL.400017", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "COVAL.400017"}, {"rulecode": "scoreComponents.PBVAL.200265", "score": "-0.1", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.200265"}, {"rulecode": "scoreComponents.NSVAL.100129", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100129"}, {"rulecode": "scoreComponents.PBVAL.200281", "score": "0.0", "confidence": "0.0", "originalScore": "2.0", "ruleCodeShort": "PBVAL.200281"}, {"rulecode": "scoreComponents.IFVAL.300034", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.300034"}, {"rulecode": "scoreComponents.PBVAL.200250", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200250"}, {"rulecode": "scoreComponents.IFVAL.400004", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.400004"}, {"rulecode": "scoreComponents.IFVAL.300044", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.300044"}, {"rulecode": "scoreComponents.NSVAL.300009", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.300009"}, {"rulecode": "scoreComponents.NSVAL.100028", "score": "0.02", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100028"}, {"rulecode": "scoreComponents.IFVAL.300021", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.300021"}, {"rulecode": "scoreComponents.WPVAL.100013", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100013"}, {"rulecode": "scoreComponents.NSVAL.100072", "score": "-0.02", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100072"}, {"rulecode": "scoreComponents.NSVAL.100053", "score": "0.02", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100053"}, {"rulecode": "scoreComponents.LNVAL.100127", "score": "0.0", "confidence": "0.0", "originalScore": "8.0", "ruleCodeShort": "LNVAL.100127"}, {"rulecode": "scoreComponents.IFVAL.100037", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.100037"}, {"rulecode": "scoreComponents.WPVAL.300046", "score": "0.0", "confidence": "0.0", "originalScore": "6.0", "ruleCodeShort": "WPVAL.300046"}, {"rulecode": "scoreComponents.PBVAL.200264", "score": "-0.1", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.200264"}, {"rulecode": "scoreComponents.PBVAL.200260", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200260"}, {"rulecode": "scoreComponents.GLOBAL.300045", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300045"}, {"rulecode": "scoreComponents.PBVAL.200244", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200244"}, {"rulecode": "scoreComponents.NSVAL.300002", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.300002"}, {"rulecode": "scoreComponents.LNVAL.100029", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "LNVAL.100029"}, {"rulecode": "scoreComponents.IFVAL.200008", "score": "0.0", "confidence": "0.0", "originalScore": "2.8", "ruleCodeShort": "IFVAL.200008"}, {"rulecode": "scoreComponents.ASANL.100500", "score": "0.6687", "confidence": "0.0", "originalScore": "0.6687", "ruleCodeShort": "ASANL.100500"}, {"rulecode": "scoreComponents.IFVAL.300011", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.300011"}, {"rulecode": "scoreComponents.COVAL.400019", "score": "0.0", "confidence": "0.0", "originalScore": "0.6495", "ruleCodeShort": "COVAL.400019"}, {"rulecode": "scoreComponents.PBVAL.200209", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200209"}, {"rulecode": "scoreComponents.PBVAL.200203", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200203"}, {"rulecode": "scoreComponents.FMVAL.300021", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.300021"}, {"rulecode": "scoreComponents.LNVAL.100121", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "LNVAL.100121"}, {"rulecode": "scoreComponents.PBVAL.200235", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200235"}, {"rulecode": "scoreComponents.PBVAL.200279", "score": "0.0", "confidence": "0.0", "originalScore": "6.0", "ruleCodeShort": "PBVAL.200279"}, {"rulecode": "scoreComponents.NSVAL.100066", "score": "0.02", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100066"}, {"rulecode": "scoreComponents.IFVAL.100006", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100006"}, {"rulecode": "scoreComponents.IFVAL.200005", "score": "0.0", "confidence": "0.0", "originalScore": "80.0", "ruleCodeShort": "IFVAL.200005"}, {"rulecode": "scoreComponents.PBVAL.200128", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200128"}, {"rulecode": "scoreComponents.IFVAL.300054", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.300054"}, {"rulecode": "scoreComponents.LNVAL.100020", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "LNVAL.100020"}, {"rulecode": "scoreComponents.NSVAL.100027", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100027"}, {"rulecode": "scoreComponents.WLVAL.200001", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WLVAL.200001"}, {"rulecode": "scoreComponents.LNVAL.100070", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "LNVAL.100070"}, {"rulecode": "scoreComponents.NSVAL.100017", "score": "0.02", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100017"}, {"rulecode": "scoreComponents.IFVAL.200011", "score": "0.0", "confidence": "0.0", "originalScore": "100.0", "ruleCodeShort": "IFVAL.200011"}, {"rulecode": "scoreComponents.LNVAL.100022", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "LNVAL.100022"}, {"rulecode": "scoreComponents.FSVAL.610001", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FSVAL.610001"}, {"rulecode": "scoreComponents.PBVAL.200215", "score": "0.1", "confidence": "0.0", "originalScore": "2.0", "ruleCodeShort": "PBVAL.200215"}, {"rulecode": "scoreComponents.PBVAL.200254", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200254"}, {"rulecode": "scoreComponents.GLOBAL.300062", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300062"}, {"rulecode": "scoreComponents.PBVAL.200263", "score": "-0.1", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.200263"}, {"rulecode": "scoreComponents.PBVAL.100611", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.100611"}, {"rulecode": "scoreComponents.WPVAL.100050", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100050"}, {"rulecode": "scoreComponents.WPVAL.300065", "score": "0.0", "confidence": "0.0", "originalScore": "6.0", "ruleCodeShort": "WPVAL.300065"}, {"rulecode": "scoreComponents.ASANL.100501", "score": "0.7484", "confidence": "0.0", "originalScore": "0.7484", "ruleCodeShort": "ASANL.100501"}, {"rulecode": "scoreComponents.PBVAL.200212", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200212"}, {"rulecode": "scoreComponents.WPVAL.100034", "score": "0.02", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100034"}, {"rulecode": "scoreComponents.PBVAL.200282", "score": "0.0", "confidence": "0.0", "originalScore": "9.0", "ruleCodeShort": "PBVAL.200282"}, {"rulecode": "scoreComponents.PBVAL.200204", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200204"}, {"rulecode": "scoreComponents.GLOBAL.300091", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300091"}, {"rulecode": "scoreComponents.WPVAL.100039", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100039"}, {"rulecode": "scoreComponents.LNVAL.100021", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "LNVAL.100021"}, {"rulecode": "scoreComponents.WPVAL.100002", "score": "-0.02", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100002"}, {"rulecode": "scoreComponents.LNVAL.100023", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "LNVAL.100023"}, {"rulecode": "scoreComponents.NSVAL.100073", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100073"}, {"rulecode": "scoreComponents.FMVAL.200150", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FMVAL.200150"}, {"rulecode": "scoreComponents.NSVAL.300021", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.300021"}, {"rulecode": "scoreComponents.IFVAL.100003", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100003"}, {"rulecode": "scoreComponents.WPVAL.100004", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100004"}, {"rulecode": "scoreComponents.NSVAL.100038", "score": "-0.02", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100038"}, {"rulecode": "scoreComponents.IFVAL.200007", "score": "0.0", "confidence": "0.0", "originalScore": "10.0", "ruleCodeShort": "IFVAL.200007"}, {"rulecode": "scoreComponents.WPVAL.100053", "score": "-0.02", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100053"}, {"rulecode": "scoreComponents.RKVAL.400005", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "RKVAL.400005"}, {"rulecode": "scoreComponents.PBVAL.200249", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200249"}, {"rulecode": "scoreComponents.NSVAL.300026", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.300026"}, {"rulecode": "scoreComponents.WPVAL.100030", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100030"}, {"rulecode": "scoreComponents.FMVAL.300666", "score": "0.0", "confidence": "0.0", "originalScore": "42.0", "ruleCodeShort": "FMVAL.300666"}, {"rulecode": "scoreComponents.WPVAL.100003", "score": "0.03", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100003"}, {"rulecode": "scoreComponents.PBVAL.200253", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200253"}, {"rulecode": "scoreComponents.NSVAL.300033", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.300033"}, {"rulecode": "scoreComponents.IFVAL.200003", "score": "0.0", "confidence": "0.0", "originalScore": "100.0", "ruleCodeShort": "IFVAL.200003"}, {"rulecode": "scoreComponents.NSVAL.100133", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100133"}, {"rulecode": "scoreComponents.ASANL.100503", "score": "0.732", "confidence": "0.0", "originalScore": "0.915", "ruleCodeShort": "ASANL.100503"}, {"rulecode": "scoreComponents.FCVAL.100117", "score": "-0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FCVAL.100117"}, {"rulecode": "scoreComponents.PBVAL.200268", "score": "-0.1", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.200268"}, {"rulecode": "scoreComponents.PBVAL.200255", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200255"}, {"rulecode": "scoreComponents.NSVAL.300010", "score": "-0.0", "confidence": "0.0", "originalScore": "-1.0", "ruleCodeShort": "NSVAL.300010"}, {"rulecode": "scoreComponents.PBVAL.200259", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200259"}, {"rulecode": "scoreComponents.IFVAL.100002", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100002"}, {"rulecode": "scoreComponents.WPVAL.100049", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100049"}, {"rulecode": "scoreComponents.GLOBAL.300036", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300036"}, {"rulecode": "scoreComponents.RKVAL.400002", "score": "0.0", "confidence": "0.0", "originalScore": "0.9953", "ruleCodeShort": "RKVAL.400002"}, {"rulecode": "scoreComponents.NSVAL.100052", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100052"}, {"rulecode": "scoreComponents.PBVAL.200261", "score": "0.0", "confidence": "0.0", "originalScore": "2.0", "ruleCodeShort": "PBVAL.200261"}, {"rulecode": "scoreComponents.PBVAL.200210", "score": "0.1", "confidence": "0.0", "originalScore": "2.0", "ruleCodeShort": "PBVAL.200210"}, {"rulecode": "scoreComponents.IFVAL.300004", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.300004"}, {"rulecode": "scoreComponents.IFVAL.300064", "score": "0.0", "confidence": "0.0", "originalScore": "4136.2841", "ruleCodeShort": "IFVAL.300064"}, {"rulecode": "scoreComponents.GLOBAL.300046", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300046"}, {"rulecode": "scoreComponents.IFVAL.300072", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.300072"}, {"rulecode": "scoreComponents.PBVAL.200251", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200251"}, {"rulecode": "scoreComponents.PBVAL.200208", "score": "0.1", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.200208"}, {"rulecode": "scoreComponents.FVANL.400001", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FVANL.400001"}, {"rulecode": "scoreComponents.NSVAL.100069", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100069"}, {"rulecode": "scoreComponents.GLOBAL.300082", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300082"}, {"rulecode": "scoreComponents.WPVAL.100060", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100060"}, {"rulecode": "scoreComponents.PBVAL.200247", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200247"}, {"rulecode": "scoreComponents.HDVAL.100004", "score": "0.0", "confidence": "0.0", "originalScore": "61.0", "ruleCodeShort": "HDVAL.100004"}, {"rulecode": "scoreComponents.PBVAL.200243", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200243"}, {"rulecode": "scoreComponents.FMVAL.300018", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.300018"}, {"rulecode": "scoreComponents.RKVAL.400003", "score": "0.0", "confidence": "0.0", "originalScore": "0.1152", "ruleCodeShort": "RKVAL.400003"}, {"rulecode": "scoreComponents.NSVAL.100024", "score": "0.02", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100024"}, {"rulecode": "scoreComponents.NSVAL.300032", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.300032"}, {"rulecode": "scoreComponents.NSVAL.100132", "score": "-0.02", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100132"}, {"rulecode": "scoreComponents.IFVAL.200002", "score": "0.0", "confidence": "0.0", "originalScore": "10.0", "ruleCodeShort": "IFVAL.200002"}, {"rulecode": "scoreComponents.BLVAL.100001", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "BLVAL.100001"}, {"rulecode": "scoreComponents.FSVAL.600002", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FSVAL.600002"}, {"rulecode": "scoreComponents.IFVAL.300024", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.300024"}, {"rulecode": "scoreComponents.NSVAL.100015", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100015"}, {"rulecode": "scoreComponents.GLOBAL.300060", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300060"}, {"rulecode": "scoreComponents.COVAL.400021", "score": "0.0", "confidence": "0.0", "originalScore": "0.7484", "ruleCodeShort": "COVAL.400021"}, {"rulecode": "scoreComponents.PBVAL.200271", "score": "-0.1", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.200271"}, {"rulecode": "scoreComponents.FBVAL.100131", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FBVAL.100131"}, {"rulecode": "scoreComponents.GLOBAL.300069", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "GLOBAL.300069"}, {"rulecode": "scoreComponents.PBVAL.200214", "score": "0.1", "confidence": "0.0", "originalScore": "4.0", "ruleCodeShort": "PBVAL.200214"}, {"rulecode": "scoreComponents.NSVAL.100122", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100122"}, {"rulecode": "scoreComponents.IFVAL.400002", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.400002"}, {"rulecode": "scoreComponents.COVAL.400020", "score": "0.0", "confidence": "0.0", "originalScore": "0.9689", "ruleCodeShort": "COVAL.400020"}, {"rulecode": "scoreComponents.WPVAL.100052", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100052"}, {"rulecode": "scoreComponents.IFVAL.100041", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100041"}, {"rulecode": "scoreComponents.IFVAL.100005", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100005"}, {"rulecode": "scoreComponents.IFVAL.100004", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100004"}, {"rulecode": "scoreComponents.NSVAL.100020", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100020"}, {"rulecode": "scoreComponents.IFVAL.300031", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.300031"}, {"rulecode": "scoreComponents.FMVAL.300004", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FMVAL.300004"}, {"rulecode": "scoreComponents.NSVAL.100120", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100120"}, {"rulecode": "scoreComponents.PBVAL.200276", "score": "-0.1", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.200276"}, {"rulecode": "scoreComponents.PBVAL.200280", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200280"}, {"rulecode": "scoreComponents.WPVAL.300061", "score": "0.0", "confidence": "0.0", "originalScore": "0.8333", "ruleCodeShort": "WPVAL.300061"}, {"rulecode": "scoreComponents.PBVAL.200283", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200283"}, {"rulecode": "scoreComponents.PBVAL.200245", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200245"}, {"rulecode": "scoreComponents.WPVAL.700053", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.700053"}, {"rulecode": "scoreComponents.FMVAL.300017", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.300017"}, {"rulecode": "scoreComponents.PBVAL.200284", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200284"}, {"rulecode": "scoreComponents.IFVAL.200013", "score": "0.0", "confidence": "0.0", "originalScore": "3.7", "ruleCodeShort": "IFVAL.200013"}, {"rulecode": "scoreComponents.COVAL.400010", "score": "0.0", "confidence": "0.0", "originalScore": "0.9463", "ruleCodeShort": "COVAL.400010"}, {"rulecode": "scoreComponents.RKVAL.400008", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "RKVAL.400008"}, {"rulecode": "scoreComponents.RKVAL.400001", "score": "0.0", "confidence": "0.0", "originalScore": "0.9945", "ruleCodeShort": "RKVAL.400001"}, {"rulecode": "scoreComponents.WPVAL.100051", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100051"}, {"rulecode": "scoreComponents.COVAL.400022", "score": "0.0", "confidence": "0.0", "originalScore": "0.6269", "ruleCodeShort": "COVAL.400022"}, {"rulecode": "scoreComponents.IFVAL.300061", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.300061"}, {"rulecode": "scoreComponents.FMVAL.300022", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.300022"}, {"rulecode": "scoreComponents.NSVAL.100071", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100071"}, {"rulecode": "scoreComponents.WPVAL.100057", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100057"}, {"rulecode": "scoreComponents.IFVAL.200015", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.200015"}, {"rulecode": "scoreComponents.NSVAL.300020", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.300020"}, {"rulecode": "scoreComponents.IFVAL.200009", "score": "0.0", "confidence": "0.0", "originalScore": "100.0", "ruleCodeShort": "IFVAL.200009"}, {"rulecode": "scoreComponents.GLOBAL.300106", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300106"}, {"rulecode": "scoreComponents.IFVAL.100040", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.100040"}, {"rulecode": "scoreComponents.HDVAL.100028", "score": "0.0", "confidence": "0.0", "originalScore": "3.0", "ruleCodeShort": "HDVAL.100028"}, {"rulecode": "scoreComponents.IFVAL.200012", "score": "0.0", "confidence": "0.0", "originalScore": "70.0", "ruleCodeShort": "IFVAL.200012"}, {"rulecode": "scoreComponents.COVAL.400011", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "COVAL.400011"}, {"rulecode": "scoreComponents.WPVAL.100007", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100007"}, {"rulecode": "scoreComponents.IFVAL.400001", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IFVAL.400001"}, {"rulecode": "scoreComponents.IFVAL.400003", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "IFVAL.400003"}, {"rulecode": "scoreComponents.PBVAL.200213", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200213"}, {"rulecode": "scoreComponents.GLOBAL.300047", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300047"}, {"rulecode": "scoreComponents.NSVAL.100121", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "NSVAL.100121"}, {"rulecode": "scoreComponents.PBVAL.200248", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200248"}, {"rulecode": "scoreComponents.PBVAL.100119", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.100119"}, {"rulecode": "scoreComponents.WPVAL.100031", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100031"}, {"rulecode": "scoreComponents.NSVAL.100058", "score": "-0.02", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "NSVAL.100058"}, {"rulecode": "entityResolution.PBvFM.name", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBvFM.name"}, {"rulecode": "entityResolution.PBvFM.geocode", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBvFM.geocode"}, {"rulecode": "entityResolution.PBvNS.name", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBvNS.name"}, {"rulecode": "entityResolution.FMvWP.mobilenumber", "score": "0.0", "confidence": "0.0", "originalScore": "0.33", "ruleCodeShort": "FMvWP.mobilenumber"}, {"rulecode": "entityResolution.FMvNS.name", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMvNS.name"}, {"rulecode": "entityResolution.FMvNS.mobilenumber", "score": "0.0", "confidence": "0.0", "originalScore": "0.33", "ruleCodeShort": "FMvNS.mobilenumber"}, {"rulecode": "entityResolution.FMvNS.address", "score": "0.0", "confidence": "0.0", "originalScore": "0.7333", "ruleCodeShort": "FMvNS.address"}, {"rulecode": "entityResolution.FMvNS.email", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMvNS.email"}, {"rulecode": "entityResolution.WPvNS.mobilenumber", "score": "0.0", "confidence": "0.0", "originalScore": "0.99", "ruleCodeShort": "WPvNS.mobilenumber"}], "raw_score": 6.686582300269665, "version_applied": "3", "fraud_score_info": {"primary": {"score": 0.06740892531564346, "model": {"id": -1, "name": "<PERSON><PERSON> (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/gen_us_30_20171129_adjust_gbm_v1", "identifier": "gen_us_30_20171129_adjust_gbm_v1", "version": "3.0", "created_date": 1516378066097, "last_updated_date": 1516378066097}, "params": null, "response": {"data": {"class0": 0.9325910746843565, "class1": 0.06740892531564346, "predictorType": "binomial"}, "status": "ok"}}, "custom": {"My TestModel-1__11.1": {"score": 0.1665124339511477, "model": {"id": 71, "name": "My TestModel-1", "url": "https://socure-prediction-prod.socure.com/predictors/predict/synchrony_dig_tn_models_v2auc_model_gbm2", "identifier": "synchrony_dig_tn_models_v2auc_model_gbm2", "version": "11.1", "created_date": 1515715200000, "last_updated_date": 1515715200000}, "params": null, "response": {"data": {"class0": 0.8334875660488523, "class1": 0.1665124339511477, "predictorType": "binomial"}, "status": "ok"}}}}, "correlation_score_info": {"name_address": {"score": 0.9914661996876485, "model": {"id": -1, "name": "Correlation Model - Name vs Address (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/Name_Address_w9_HD_IF_corr_gbm_v2", "identifier": "Name_Address_w9_HD_IF_corr_gbm_v2", "version": "6.0", "created_date": 1516378066098, "last_updated_date": 1516378066098}, "params": {"data": {"IFVAL.200012": 70.0, "HDVAL.100022": null, "IFVAL.100002": 1.0, "HDVAL.100052": "Exact", "IFVAL.200004": 100.0, "IFVAL.100001": 1.0, "HDVAL.100024": "Exact", "HDVAL.100053": "Exact"}}, "response": {"data": {"class0": 0.008533800312351514, "class1": 0.9914661996876485, "predictorType": "binomial"}, "status": "ok"}}, "name_email": {"score": 0.30749248908054966, "model": {"id": -1, "name": "Correlation Model - Name vs Email (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/Combined_Trainset_glm_model_vI_HDWP_N_4", "identifier": "Combined_Trainset_glm_model_vI_HDWP_N_4", "version": "4.0", "created_date": 1516378066103, "last_updated_date": 1516378066103}, "params": {"data": {"WPVAL.300040": "false", "WPVAL.300039": "The mailbox is invalid or does not exist", "HDVAL.100052": "Exact", "HDVAL.100041": "None", "FCvFM.name": null, "HDVAL.100053": "Exact", "WPVAL.300044": "No name found", "FCVAL.100117": 1.0}}, "response": {"data": {"class0": 0.6925075109194503, "class1": 0.30749248908054966, "predictorType": "binomial"}, "status": "ok"}}, "name_phone": {"score": 0.9462615894444752, "model": {"id": -1, "name": "Correlation Model - Name vs Phone (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/Name_Phone_prior80_WP_NS_corr_drf_v2", "identifier": "Name_Phone_prior80_WP_NS_corr_drf_v2", "version": "4.0", "created_date": 1516378066103, "last_updated_date": 1516378066103}, "params": {"data": {"WPVAL.300005": "Match", "NSVAL.300010": -1.0, "WPVAL.300056": "TRUE", "NSVAL.300017": 0.0, "NSVAL.300009": 1.0}}, "response": {"data": {"class0": 0.05373841055552475, "class1": 0.9462615894444752, "predictorType": "binomial"}, "status": "ok"}}}, "risk_score_info": {"address": {"score": 0.11518155603028522, "model": {"id": -1, "name": "Address Risk Model (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/addrrisk_oct2017_glm_model_vX", "identifier": "addrrisk_oct2017_glm_model_vX", "version": "3.0", "created_date": 1516378066106, "last_updated_date": 1516378066106}, "params": {"data": {"IFVAL.100003": 1.0, "IFVAL.100005": 1.0, "IFVAL.200012": 70.0, "IFVAL.100002": 1.0, "HDVAL.100052": "Exact", "IFVAL.200004": 100.0, "IFVAL.100001": 1.0, "HDVAL.100024": "Exact", "HDVAL.100028": 3.0, "HDVAL.100053": "Exact", "HDVAL.100027": 1.0, "IFVAL.100004": 1.0, "IFVAL.100006": 1.0}}, "response": {"data": {"class0": 0.8848184439697148, "class1": 0.11518155603028522, "predictorType": "binomial"}, "status": "ok"}}, "email": {"score": 0.9944635843319801, "model": {"id": -1, "name": "Email Risk Model (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/emailrisk_oct2017_gbm_model_v_23", "identifier": "emailrisk_oct2017_gbm_model_v_23", "version": "4.0", "created_date": 1516378066107, "last_updated_date": 1516378066107}, "params": {"data": {"WPVAL.300042": "false", "NSVAL.300019": 1.0, "NSVAL.300032": 1.0, "FMVAL.300003": 1.0, "WPVAL.300043": "false", "NSVAL.300018": 80.0, "WPVAL.300046": 6.0, "WPVAL.300040": "false", "WPVAL.300039": "The mailbox is invalid or does not exist", "FMVAL.300002": 1.0, "FMVAL.300001": "gmail.com", "NSVAL.300020": 0.0, "WPVAL.300048": 8195.0, "FMVAL.300004": 0.0, "WPVAL.300044": "No name found", "FCVAL.100117": 1.0}}, "response": {"data": {"class0": 0.005536415668019878, "class1": 0.9944635843319801, "predictorType": "binomial"}, "status": "ok"}}, "phone": {"score": 0.9953317117811311, "model": {"id": -1, "name": "Phone Risk Model (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/phone_risk_model_glm_v2", "identifier": "phone_risk_model_glm_v2", "version": "2.0", "created_date": 1516378066107, "last_updated_date": 1516378066107}, "params": {"data": {"WPVAL.300008": "Mobile", "WPVAL.300005": "Match", "BLVAL.100001": 0.0, "NSVAL.300011": "", "NSVAL.300024": "U", "WPVAL.300060": 0.8333333333333333, "NSVAL.300010": -1.0, "NSVAL.300025": "", "NSVAL.300009": 1.0}}, "response": {"data": {"class0": 0.004668288218868888, "class1": 0.9953317117811311, "predictorType": "binomial"}, "status": "ok"}}}, "instance_id": "i-0e3bcf472071379a9", "caches_used": [], "categorical_values": [{"rulecode": "HDVAL.100055", "value": "None"}, {"rulecode": "HDVAL.100051", "value": "None"}, {"rulecode": "HDVAL.100041", "value": "None"}, {"rulecode": "HDVAL.100024", "value": "Exact"}, {"rulecode": "HDVAL.100053", "value": "Exact"}, {"rulecode": "HDVAL.100054", "value": "Not Input"}, {"rulecode": "HDVAL.100052", "value": "Exact"}, {"rulecode": "HDVAL.100002", "value": "Transaction Successful; Results found."}, {"rulecode": "IFVAL.100033", "value": "R"}, {"rulecode": "IFVAL.100031", "value": "T-MOBILE USA, INC."}, {"rulecode": "IFVAL.100047", "value": "100IP"}, {"rulecode": "IFVAL.100045", "value": "100"}, {"rulecode": "IFVAL.100046", "value": "IP"}, {"rulecode": "IFVAL.100048", "value": "N"}, {"rulecode": "IFVAL.100044", "value": "Y"}, {"rulecode": "IFVAL.100034", "value": "2"}, {"rulecode": "IFVAL.100032", "value": "W"}, {"rulecode": "WPVAL.300042", "value": "false"}, {"rulecode": "WPVAL.300040", "value": "false"}, {"rulecode": "WPVAL.300039", "value": "The mailbox is invalid or does not exist"}, {"rulecode": "WPVAL.300067", "value": "true"}, {"rulecode": "WPVAL.300041", "value": "The mailbox is invalid or does not exist"}, {"rulecode": "WPVAL.300054", "value": "FALSE"}, {"rulecode": "WPVAL.300053", "value": "FALSE"}, {"rulecode": "WPVAL.300044", "value": "No name found"}, {"rulecode": "WPVAL.300016", "value": "Multi unit"}, {"rulecode": "WPVAL.300008", "value": "Mobile"}, {"rulecode": "WPVAL.300006", "value": "Zip+4 match"}, {"rulecode": "WPVAL.300047", "value": "1995-08-13"}, {"rulecode": "WPVAL.300005", "value": "Match"}, {"rulecode": "WPVAL.300013", "value": "false"}, {"rulecode": "WPVAL.300018", "value": "false"}, {"rulecode": "WPVAL.300055", "value": "FALSE"}, {"rulecode": "WPVAL.300043", "value": "false"}, {"rulecode": "WPVAL.300002", "value": "false"}, {"rulecode": "WPVAL.300017", "value": "true"}, {"rulecode": "WPVAL.300007", "value": "US"}, {"rulecode": "WPVAL.300009", "value": "T-Mobile USA"}, {"rulecode": "WPVAL.300015", "value": "Match"}, {"rulecode": "WPVAL.300045", "value": "2018-01-13"}, {"rulecode": "WPVAL.300014", "value": "Missing unit/apt/suite number"}, {"rulecode": "WPVAL.300056", "value": "TRUE"}, {"rulecode": "WPVAL.300001", "value": ""}, {"rulecode": "NSVAL.300029", "value": "6"}, {"rulecode": "NSVAL.300015", "value": "D"}, {"rulecode": "NSVAL.300016", "value": "U"}, {"rulecode": "NSVAL.300013", "value": ""}, {"rulecode": "NSVAL.300024", "value": "U"}, {"rulecode": "NSVAL.300005", "value": "R"}, {"rulecode": "NSVAL.300008", "value": "N"}, {"rulecode": "NSVAL.300006", "value": "N"}, {"rulecode": "NSVAL.300027", "value": ""}, {"rulecode": "NSVAL.300025", "value": ""}, {"rulecode": "NSVAL.300007", "value": "N"}, {"rulecode": "NSVAL.300039", "value": "U"}, {"rulecode": "NSVAL.300028", "value": ""}, {"rulecode": "NSVAL.300023", "value": ""}, {"rulecode": "NSVAL.300003", "value": "D"}, {"rulecode": "NSVAL.300022", "value": ""}, {"rulecode": "NSVAL.300030", "value": "65"}, {"rulecode": "NSVAL.300014", "value": ""}, {"rulecode": "NSVAL.300034", "value": "C"}, {"rulecode": "NSVAL.300011", "value": ""}, {"rulecode": "NSVAL.300004", "value": "H"}, {"rulecode": "NSVAL.300031", "value": "0"}, {"rulecode": "GLOBAL.300077", "value": "Residential"}, {"rulecode": "GLOBAL.300070", "value": "Wireless"}, {"rulecode": "GLOBAL.300130", "value": "Street match"}, {"rulecode": "GLOBAL.300085", "value": "R"}, {"rulecode": "GLOBAL.300064", "value": "H"}, {"rulecode": "FMVAL.300001", "value": "gmail.com"}, {"rulecode": "FMVAL.400006", "value": "20180119"}], "query_plan": {"vendors": ["COVAL", "PBVAL", "PYVAL", "PNANL", "VDANL", "RKVAL", "HDVAL", "INANL", "TWANL", "FSVAL", "TWVAL", "GLOBAL", "FCVAL", "YOVAL", "WLVAL", "IFVAL", "TOVAL", "FMVAL", "WPVAL", "LNVAL", "FVANL", "NSVAL", "BLVAL", "LIANL", "XNANL", "LIVAL", "GPVAL", "ASANL", "FBANL", "GPANL", "FBVAL"], "id_plus_models": {"fraud_models": {"primary": {"id": -1, "name": "<PERSON><PERSON> (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/gen_us_30_20171129_adjust_gbm_v1", "identifier": "gen_us_30_20171129_adjust_gbm_v1", "version": "3.0", "created_date": 1516378066097, "last_updated_date": 1516378066097}, "secondary": [{"id": 71, "name": "My TestModel-1", "url": "https://socure-prediction-prod.socure.com/predictors/predict/synchrony_dig_tn_models_v2auc_model_gbm2", "identifier": "synchrony_dig_tn_models_v2auc_model_gbm2", "version": "11.1", "created_date": 1515715200000, "last_updated_date": 1515715200000}]}, "correlation_models": {"name_email": {"id": -1, "name": "Correlation Model - Name vs Email (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/Combined_Trainset_glm_model_vI_HDWP_N_4", "identifier": "Combined_Trainset_glm_model_vI_HDWP_N_4", "version": "4.0", "created_date": 1516378066103, "last_updated_date": 1516378066103}, "name_address": {"id": -1, "name": "Correlation Model - Name vs Address (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/Name_Address_w9_HD_IF_corr_gbm_v2", "identifier": "Name_Address_w9_HD_IF_corr_gbm_v2", "version": "6.0", "created_date": 1516378066098, "last_updated_date": 1516378066098}, "name_phone": {"id": -1, "name": "Correlation Model - Name vs Phone (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/Name_Phone_prior80_WP_NS_corr_drf_v2", "identifier": "Name_Phone_prior80_WP_NS_corr_drf_v2", "version": "4.0", "created_date": 1516378066103, "last_updated_date": 1516378066103}}, "risk_models": {"address": {"id": -1, "name": "Address Risk Model (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/addrrisk_oct2017_glm_model_vX", "identifier": "addrrisk_oct2017_glm_model_vX", "version": "3.0", "created_date": 1516378066106, "last_updated_date": 1516378066106}, "email": {"id": -1, "name": "Email Risk Model (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/emailrisk_oct2017_gbm_model_v_23", "identifier": "emailrisk_oct2017_gbm_model_v_23", "version": "4.0", "created_date": 1516378066107, "last_updated_date": 1516378066107}, "phone": {"id": -1, "name": "Phone Risk Model (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/phone_risk_model_glm_v2", "identifier": "phone_risk_model_glm_v2", "version": "2.0", "created_date": 1516378066107, "last_updated_date": 1516378066107}}}, "id_plus_features": {"requested_features": ["FraudScore", "Social", "EmailRiskScore", "KYC", "AllCorrelations", "AddressRiskScore", "Blacklist", "PhoneRiskScore", "AuthScore", "Watchlist"], "resolved_features": ["Social", "AllCorrelations", "NameEmailCorrelation", "NameAddressCorrelation", "PhoneRiskScore", "Watchlist", "FraudScore", "EmailRiskScore", "KYC", "NamePhoneCorrelation", "AddressRiskScore", "Blacklist", "AuthScore"], "outputted_features": ["Social", "AllCorrelations", "NameEmailCorrelation", "NameAddressCorrelation", "PhoneRiskScore", "Watchlist", "FraudScore", "EmailRiskScore", "KYC", "NamePhoneCorrelation", "AddressRiskScore", "Blacklist", "AuthScore"]}, "scheduler_timeout": {"length": 5000, "unit": "MILLISECONDS"}, "vendor_timeouts": {"PBVAL": {"length": 5000, "unit": "MILLISECONDS"}, "PYVAL": {"length": 5000, "unit": "MILLISECONDS"}, "PNANL": {"length": 5000, "unit": "MILLISECONDS"}, "VDANL": {"length": 5000, "unit": "MILLISECONDS"}, "HDVAL": {"length": 4000, "unit": "MILLISECONDS"}, "INANL": {"length": 5000, "unit": "MILLISECONDS"}, "TWANL": {"length": 4000, "unit": "MILLISECONDS"}, "FSVAL": {"length": 5000, "unit": "MILLISECONDS"}, "TWVAL": {"length": 5000, "unit": "MILLISECONDS"}, "FCVAL": {"length": 5000, "unit": "MILLISECONDS"}, "YOVAL": {"length": 5000, "unit": "MILLISECONDS"}, "WLVAL": {"length": 5000, "unit": "MILLISECONDS"}, "IFVAL": {"length": 4000, "unit": "MILLISECONDS"}, "TOVAL": {"length": 5000, "unit": "MILLISECONDS"}, "FMVAL": {"length": 5000, "unit": "MILLISECONDS"}, "WPVAL": {"length": 5000, "unit": "MILLISECONDS"}, "LNVAL": {"length": 5000, "unit": "MILLISECONDS"}, "FVANL": {"length": 5000, "unit": "MILLISECONDS"}, "NSVAL": {"length": 5000, "unit": "MILLISECONDS"}, "BLVAL": {"length": 4000, "unit": "MILLISECONDS"}, "LIANL": {"length": 5000, "unit": "MILLISECONDS"}, "XNANL": {"length": 5000, "unit": "MILLISECONDS"}, "LIVAL": {"length": 5000, "unit": "MILLISECONDS"}, "GPVAL": {"length": 5000, "unit": "MILLISECONDS"}, "FBANL": {"length": 4000, "unit": "MILLISECONDS"}, "GPANL": {"length": 4000, "unit": "MILLISECONDS"}, "FBVAL": {"length": 5000, "unit": "MILLISECONDS"}}}, "http_status": 200, "reason_codes": ["R620", "I704", "I602", "I914", "I707", "I609", "I708", "R915", "I917", "I196", "I905", "R551", "R905", "R520", "I611", "I720", "R606", "I618"]}}