[{"ruleCodes": [{"rulecode": "scoreComponents.LIVAL.200126", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "LIVAL.200126"}, {"rulecode": "scoreComponents.FVANL.100131", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FVANL.100131"}, {"rulecode": "scoreComponents.FBVAL.100130", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FBVAL.100130"}, {"rulecode": "scoreComponents.TWVAL.200127", "score": "0.5", "confidence": "0.25", "originalScore": "1.0", "ruleCodeShort": "TWVAL.200127"}, {"rulecode": "scoreComponents.TWVAL.200126", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "TWVAL.200126"}, {"rulecode": "scoreComponents.TWVAL.500001", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "TWVAL.500001"}, {"rulecode": "scoreComponents.EMVAL.200125", "score": "2.0", "confidence": "0.1", "originalScore": "1.0", "ruleCodeShort": "EMVAL.200125"}, {"rulecode": "scoreComponents.TWANL.300009", "score": "0.0", "confidence": "0.0", "originalScore": "8.0", "ruleCodeShort": "TWANL.300009"}, {"rulecode": "scoreComponents.TWANL.200006", "score": "1.1", "confidence": "0.075", "originalScore": "1.0", "ruleCodeShort": "TWANL.200006"}, {"rulecode": "scoreComponents.TWANL.100001", "score": "-0.05", "confidence": "0.075", "originalScore": "1.0", "ruleCodeShort": "TWANL.100001"}, {"rulecode": "scoreComponents.TWANL.200017", "score": "1.1", "confidence": "0.075", "originalScore": "1.0", "ruleCodeShort": "TWANL.200017"}, {"rulecode": "scoreComponents.TWANL.200002", "score": "1.1", "confidence": "0.075", "originalScore": "1.0", "ruleCodeShort": "TWANL.200002"}, {"rulecode": "scoreComponents.TWANL.100017", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "TWANL.100017"}, {"rulecode": "scoreComponents.TWANL.300011", "score": "0.0", "confidence": "0.0", "originalScore": "2.375", "ruleCodeShort": "TWANL.300011"}, {"rulecode": "scoreComponents.TWANL.300006", "score": "0.0", "confidence": "0.0", "originalScore": "72.0", "ruleCodeShort": "TWANL.300006"}, {"rulecode": "scoreComponents.TWANL.100011", "score": "-1.51", "confidence": "0.075", "originalScore": "1.0", "ruleCodeShort": "TWANL.100011"}, {"rulecode": "scoreComponents.TWANL.500002", "score": "0.0", "confidence": "0.0", "originalScore": "54.0", "ruleCodeShort": "TWANL.500002"}, {"rulecode": "scoreComponents.TWANL.200018", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "TWANL.200018"}, {"rulecode": "scoreComponents.TWANL.200001", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "TWANL.200001"}, {"rulecode": "scoreComponents.TWANL.300003", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "TWANL.300003"}, {"rulecode": "scoreComponents.TWANL.100009", "score": "-2.01", "confidence": "0.075", "originalScore": "1.0", "ruleCodeShort": "TWANL.100009"}, {"rulecode": "scoreComponents.TWANL.300005", "score": "0.0", "confidence": "0.0", "originalScore": "125.0", "ruleCodeShort": "TWANL.300005"}, {"rulecode": "scoreComponents.TWANL.300007", "score": "0.0", "confidence": "0.0", "originalScore": "23.0", "ruleCodeShort": "TWANL.300007"}, {"rulecode": "scoreComponents.TWANL.100020", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "TWANL.100020"}, {"rulecode": "scoreComponents.TWANL.300002", "score": "0.0", "confidence": "0.0", "originalScore": "3750.0", "ruleCodeShort": "TWANL.300002"}, {"rulecode": "scoreComponents.TWANL.200011", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "TWANL.200011"}, {"rulecode": "scoreComponents.TWANL.100010", "score": "-2.01", "confidence": "0.075", "originalScore": "1.0", "ruleCodeShort": "TWANL.100010"}, {"rulecode": "scoreComponents.TWANL.300010", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "TWANL.300010"}, {"rulecode": "scoreComponents.TWANL.200021", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "TWANL.200021"}, {"rulecode": "scoreComponents.TWANL.200005", "score": "1.1", "confidence": "0.075", "originalScore": "1.0", "ruleCodeShort": "TWANL.200005"}, {"rulecode": "scoreComponents.TWANL.100004", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "TWANL.100004"}, {"rulecode": "scoreComponents.TWANL.200008", "score": "1.1", "confidence": "0.075", "originalScore": "1.0", "ruleCodeShort": "TWANL.200008"}, {"rulecode": "scoreComponents.TWANL.300004", "score": "0.0", "confidence": "0.0", "originalScore": "2.0", "ruleCodeShort": "TWANL.300004"}, {"rulecode": "scoreComponents.TWANL.200016", "score": "1.1", "confidence": "0.075", "originalScore": "1.0", "ruleCodeShort": "TWANL.200016"}, {"rulecode": "scoreComponents.TWANL.300012", "score": "0.0", "confidence": "0.0", "originalScore": "1.8071", "ruleCodeShort": "TWANL.300012"}, {"rulecode": "scoreComponents.TWANL.200004", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "TWANL.200004"}, {"rulecode": "scoreComponents.TWANL.300008", "score": "0.0", "confidence": "0.0", "originalScore": "2.0", "ruleCodeShort": "TWANL.300008"}, {"rulecode": "scoreComponents.TWANL.300001", "score": "0.0", "confidence": "0.0", "originalScore": "54.0", "ruleCodeShort": "TWANL.300001"}, {"rulecode": "scoreComponents.FSVAL.600002", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FSVAL.600002"}, {"rulecode": "scoreComponents.FSVAL.610001", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FSVAL.610001"}, {"rulecode": "scoreComponents.FCVAL.200128", "score": "1.65", "confidence": "0.1", "originalScore": "5.0", "ruleCodeShort": "FCVAL.200128"}, {"rulecode": "scoreComponents.FCVAL.200127", "score": "0.0", "confidence": "0.0", "originalScore": "4.0", "ruleCodeShort": "FCVAL.200127"}, {"rulecode": "scoreComponents.FCVAL.200277", "score": "0.0", "confidence": "0.0", "originalScore": "0.925", "ruleCodeShort": "FCVAL.200277"}, {"rulecode": "scoreComponents.GLOBAL.100143", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "GLOBAL.100143"}, {"rulecode": "scoreComponents.PAVAL.100120", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PAVAL.100120"}, {"rulecode": "scoreComponents.PIVAL.100120", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PIVAL.100120"}, {"rulecode": "scoreComponents.IMVAL.100131", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "IMVAL.100131"}, {"rulecode": "scoreComponents.FMVAL.100140", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FMVAL.100140"}, {"rulecode": "scoreComponents.FMVAL.200150", "score": "0.0", "confidence": "0.0", "originalScore": "0.01", "ruleCodeShort": "FMVAL.200150"}, {"rulecode": "scoreComponents.FMVAL.200125", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.200125"}, {"rulecode": "entityResolution.PAvPI.name", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PAvPI.name"}, {"rulecode": "entityResolution.PAvPI.gender", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PAvPI.gender"}, {"rulecode": "entityResolution.PAvPI.username", "score": "0.0", "confidence": "0.0", "originalScore": "0.6364", "ruleCodeShort": "PAvPI.username"}, {"rulecode": "entityResolution.TWvFC.name", "score": "1.1", "confidence": "0.075", "originalScore": "1.0", "ruleCodeShort": "TWvFC.name"}, {"rulecode": "entityResolution.FMvFM.name", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMvFM.name"}, {"rulecode": "entityResolution.FMvFM.geocode", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMvFM.geocode"}, {"rulecode": "entityResolution.FMvFM.gender", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMvFM.gender"}, {"rulecode": "entityResolution.FMvFM.email", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMvFM.email"}, {"rulecode": "entityResolution.PAvFM.name", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PAvFM.name"}, {"rulecode": "entityResolution.PAvFM.gender", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PAvFM.gender"}, {"rulecode": "entityResolution.TWvPA.name", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "TWvPA.name"}, {"rulecode": "entityResolution.TWvPA.username", "score": "-0.0", "confidence": "0.0", "originalScore": "-0.0909", "ruleCodeShort": "TWvPA.username"}, {"rulecode": "entityResolution.FCvPI.name", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FCvPI.name"}, {"rulecode": "entityResolution.FMvTW.name", "score": "1.01", "confidence": "0.075", "originalScore": "1.0", "ruleCodeShort": "FMvTW.name"}, {"rulecode": "entityResolution.FMvPI.name", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMvPI.name"}, {"rulecode": "entityResolution.FMvPI.gender", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMvPI.gender"}, {"rulecode": "entityResolution.FCvFM.name", "score": "1.1", "confidence": "0.075", "originalScore": "1.0", "ruleCodeShort": "FCvFM.name"}, {"rulecode": "entityResolution.FCvFM.email", "score": "1.1", "confidence": "0.075", "originalScore": "1.0", "ruleCodeShort": "FCvFM.email"}, {"rulecode": "entityResolution.TWvPI.name", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "TWvPI.name"}, {"rulecode": "entityResolution.TWvPI.username", "score": "-0.0", "confidence": "0.0", "originalScore": "-0.1667", "ruleCodeShort": "TWvPI.username"}, {"rulecode": "entityResolution.FCvPA.name", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FCvPA.name"}], "rawScore": 10, "erDebug": {"FSvPI": {"result": {}, "PI": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["james.anto.7"], "gender": ["male"], "image": ["http://graph.facebook.com/1266550805/picture?type=large"]}, "FS": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}}, "FSvFB": {"result": {}, "FB": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}, "FS": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}}, "TWvPI": {"result": {"name": 1, "username": -0.16666666666666663}, "TW": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "JAMES", "middlename": "", "surname": "ANTO", "suffix": ""}], "companyname": [], "bio": ["I am a Software Engineer interested in doing innovative applications"], "address": [{"physicaladdress": "", "city": "VELLORE, TAMILNADU", "state": "", "zip": "", "country": ""}], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["javajamesb08"], "gender": [], "image": ["http://pbs.twimg.com/profile_images/776795258143113217/x9dvgvH3_normal.jpg"]}, "PI": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["james.anto.7"], "gender": ["male"], "image": ["http://graph.facebook.com/1266550805/picture?type=large"]}}, "TWvFB": {"result": {}, "TW": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "JAMES", "middlename": "", "surname": "ANTO", "suffix": ""}], "companyname": [], "bio": ["I am a Software Engineer interested in doing innovative applications"], "address": [{"physicaladdress": "", "city": "VELLORE, TAMILNADU", "state": "", "zip": "", "country": ""}], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["javajamesb08"], "gender": [], "image": ["http://pbs.twimg.com/profile_images/776795258143113217/x9dvgvH3_normal.jpg"]}, "FB": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}}, "FSvFC": {"result": {}, "FS": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}, "FC": {"ipaddress": [], "email": ["<EMAIL>"], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": ["Bosco its", "Java Developer"], "bio": [], "address": [{"physicaladdress": "", "city": "<PERSON><PERSON><PERSON>", "state": "Tamil Nadu", "zip": "", "country": "IN"}], "geocode": [], "nationalid": [], "dob": ["19920101"], "mobilenumber": [], "username": [], "gender": [], "image": ["https://d2ojpxxtu63wzl.cloudfront.net/static/fd9f3d9a271e7ec3d75e6cbc6663002d_1730c825f2507f731157646371ec2c04ba5e0ff58b3caa0e34d3b3300b9a82b9", "https://d2ojpxxtu63wzl.cloudfront.net/static/7ce31797eea8c1149da8ebdfa7b5e25e_3544530d8894874171fe9c3c75658653ecb977a2b51b5ee0a645e3c061e9d928", "https://d2ojpxxtu63wzl.cloudfront.net/static/c5afe8580e437cfdd4805c808e70423f_f7a16c31eebd19252c813b7a6870f743390615ca207035e2eee20376acfbba45"]}}, "TWvPA": {"result": {"name": 1, "username": -0.09090909090909094}, "PA": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON><PERSON><PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": [], "bio": [], "address": [{"physicaladdress": "", "city": "", "state": "", "zip": "", "country": "US"}, {"physicaladdress": "", "city": "", "state": "", "zip": "", "country": "NL"}], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["james.richard.54943", "<PERSON><PERSON><PERSON><PERSON>", "jaymesanto"], "gender": ["male"], "image": ["http://graph.facebook.com/100002250047930/picture?type=large", "http://graph.facebook.com/*********/picture?type=large", "http://graph.facebook.com/100001592374262/picture?type=large"]}, "TW": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "JAMES", "middlename": "", "surname": "ANTO", "suffix": ""}], "companyname": [], "bio": ["I am a Software Engineer interested in doing innovative applications"], "address": [{"physicaladdress": "", "city": "VELLORE, TAMILNADU", "state": "", "zip": "", "country": ""}], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["javajamesb08"], "gender": [], "image": ["http://pbs.twimg.com/profile_images/776795258143113217/x9dvgvH3_normal.jpg"]}}, "FSvFM": {"result": {}, "FM": {"ipaddress": [], "email": ["<EMAIL>"], "name": [{"salutation": "", "firstname": "JAMES", "middlename": "", "surname": "ANTO", "suffix": ""}], "companyname": [], "bio": [], "address": [{"physicaladdress": "", "city": "NEW YORK CITY", "state": "NY", "zip": "", "country": "US"}, null], "geocode": [{"lat": 40.702898, "lon": -74.010241}], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": ["M"], "image": []}, "FS": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}}, "PIvFB": {"result": {}, "PI": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["james.anto.7"], "gender": ["male"], "image": ["http://graph.facebook.com/1266550805/picture?type=large"]}, "FB": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}}, "FBvPC": {"result": {}, "PC": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}, "FB": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}}, "PAvPI": {"result": {"gender": 1, "name": 1, "username": 0.6363636363636365}, "PA": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON><PERSON><PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": [], "bio": [], "address": [{"physicaladdress": "", "city": "", "state": "", "zip": "", "country": "US"}, {"physicaladdress": "", "city": "", "state": "", "zip": "", "country": "NL"}], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["james.richard.54943", "<PERSON><PERSON><PERSON><PERSON>", "jaymesanto"], "gender": ["male"], "image": ["http://graph.facebook.com/100002250047930/picture?type=large", "http://graph.facebook.com/*********/picture?type=large", "http://graph.facebook.com/100001592374262/picture?type=large"]}, "PI": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["james.anto.7"], "gender": ["male"], "image": ["http://graph.facebook.com/1266550805/picture?type=large"]}}, "TWvFC": {"result": {"name": 1}, "TW": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "JAMES", "middlename": "", "surname": "ANTO", "suffix": ""}], "companyname": [], "bio": ["I am a Software Engineer interested in doing innovative applications"], "address": [{"physicaladdress": "", "city": "VELLORE, TAMILNADU", "state": "", "zip": "", "country": ""}], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["javajamesb08"], "gender": [], "image": ["http://pbs.twimg.com/profile_images/776795258143113217/x9dvgvH3_normal.jpg"]}, "FC": {"ipaddress": [], "email": ["<EMAIL>"], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": ["Bosco its", "Java Developer"], "bio": [], "address": [{"physicaladdress": "", "city": "<PERSON><PERSON><PERSON>", "state": "Tamil Nadu", "zip": "", "country": "IN"}], "geocode": [], "nationalid": [], "dob": ["19920101"], "mobilenumber": [], "username": [], "gender": [], "image": ["https://d2ojpxxtu63wzl.cloudfront.net/static/fd9f3d9a271e7ec3d75e6cbc6663002d_1730c825f2507f731157646371ec2c04ba5e0ff58b3caa0e34d3b3300b9a82b9", "https://d2ojpxxtu63wzl.cloudfront.net/static/7ce31797eea8c1149da8ebdfa7b5e25e_3544530d8894874171fe9c3c75658653ecb977a2b51b5ee0a645e3c061e9d928", "https://d2ojpxxtu63wzl.cloudfront.net/static/c5afe8580e437cfdd4805c808e70423f_f7a16c31eebd19252c813b7a6870f743390615ca207035e2eee20376acfbba45"]}}, "FCvFM": {"result": {"name": 1, "email": 1}, "FM": {"ipaddress": [], "email": ["<EMAIL>"], "name": [{"salutation": "", "firstname": "JAMES", "middlename": "", "surname": "ANTO", "suffix": ""}], "companyname": [], "bio": [], "address": [{"physicaladdress": "", "city": "NEW YORK CITY", "state": "NY", "zip": "", "country": "US"}, null], "geocode": [{"lat": 40.702898, "lon": -74.010241}], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": ["M"], "image": []}, "FC": {"ipaddress": [], "email": ["<EMAIL>"], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": ["Bosco its", "Java Developer"], "bio": [], "address": [{"physicaladdress": "", "city": "<PERSON><PERSON><PERSON>", "state": "Tamil Nadu", "zip": "", "country": "IN"}], "geocode": [], "nationalid": [], "dob": ["19920101"], "mobilenumber": [], "username": [], "gender": [], "image": ["https://d2ojpxxtu63wzl.cloudfront.net/static/fd9f3d9a271e7ec3d75e6cbc6663002d_1730c825f2507f731157646371ec2c04ba5e0ff58b3caa0e34d3b3300b9a82b9", "https://d2ojpxxtu63wzl.cloudfront.net/static/7ce31797eea8c1149da8ebdfa7b5e25e_3544530d8894874171fe9c3c75658653ecb977a2b51b5ee0a645e3c061e9d928", "https://d2ojpxxtu63wzl.cloudfront.net/static/c5afe8580e437cfdd4805c808e70423f_f7a16c31eebd19252c813b7a6870f743390615ca207035e2eee20376acfbba45"]}}, "PIvPC": {"result": {}, "PC": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}, "PI": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["james.anto.7"], "gender": ["male"], "image": ["http://graph.facebook.com/1266550805/picture?type=large"]}}, "PAvPC": {"result": {}, "PA": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON><PERSON><PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": [], "bio": [], "address": [{"physicaladdress": "", "city": "", "state": "", "zip": "", "country": "US"}, {"physicaladdress": "", "city": "", "state": "", "zip": "", "country": "NL"}], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["james.richard.54943", "<PERSON><PERSON><PERSON><PERSON>", "jaymesanto"], "gender": ["male"], "image": ["http://graph.facebook.com/100002250047930/picture?type=large", "http://graph.facebook.com/*********/picture?type=large", "http://graph.facebook.com/100001592374262/picture?type=large"]}, "PC": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}}, "PCvTW": {"result": {}, "PC": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}, "TW": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "JAMES", "middlename": "", "surname": "ANTO", "suffix": ""}], "companyname": [], "bio": ["I am a Software Engineer interested in doing innovative applications"], "address": [{"physicaladdress": "", "city": "VELLORE, TAMILNADU", "state": "", "zip": "", "country": ""}], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["javajamesb08"], "gender": [], "image": ["http://pbs.twimg.com/profile_images/776795258143113217/x9dvgvH3_normal.jpg"]}}, "PAvFB": {"result": {}, "PA": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON><PERSON><PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": [], "bio": [], "address": [{"physicaladdress": "", "city": "", "state": "", "zip": "", "country": "US"}, {"physicaladdress": "", "city": "", "state": "", "zip": "", "country": "NL"}], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["james.richard.54943", "<PERSON><PERSON><PERSON><PERSON>", "jaymesanto"], "gender": ["male"], "image": ["http://graph.facebook.com/100002250047930/picture?type=large", "http://graph.facebook.com/*********/picture?type=large", "http://graph.facebook.com/100001592374262/picture?type=large"]}, "FB": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}}, "FCvPI": {"result": {"name": 1}, "PI": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["james.anto.7"], "gender": ["male"], "image": ["http://graph.facebook.com/1266550805/picture?type=large"]}, "FC": {"ipaddress": [], "email": ["<EMAIL>"], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": ["Bosco its", "Java Developer"], "bio": [], "address": [{"physicaladdress": "", "city": "<PERSON><PERSON><PERSON>", "state": "Tamil Nadu", "zip": "", "country": "IN"}], "geocode": [], "nationalid": [], "dob": ["19920101"], "mobilenumber": [], "username": [], "gender": [], "image": ["https://d2ojpxxtu63wzl.cloudfront.net/static/fd9f3d9a271e7ec3d75e6cbc6663002d_1730c825f2507f731157646371ec2c04ba5e0ff58b3caa0e34d3b3300b9a82b9", "https://d2ojpxxtu63wzl.cloudfront.net/static/7ce31797eea8c1149da8ebdfa7b5e25e_3544530d8894874171fe9c3c75658653ecb977a2b51b5ee0a645e3c061e9d928", "https://d2ojpxxtu63wzl.cloudfront.net/static/c5afe8580e437cfdd4805c808e70423f_f7a16c31eebd19252c813b7a6870f743390615ca207035e2eee20376acfbba45"]}}, "FMvFM": {"result": {"gender": 1, "name": 1, "geocode": 0.999999619914651, "email": 1}, "FM": {"ipaddress": [], "email": ["<EMAIL>"], "name": [{"salutation": "", "firstname": "JAMES", "middlename": "", "surname": "ANTO", "suffix": ""}], "companyname": [], "bio": [], "address": [{"physicaladdress": "", "city": "NEW YORK CITY", "state": "NY", "zip": "", "country": "US"}, null], "geocode": [{"lat": 40.702898, "lon": -74.010241}], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": ["M"], "image": []}}, "TWvFS": {"result": {}, "TW": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "JAMES", "middlename": "", "surname": "ANTO", "suffix": ""}], "companyname": [], "bio": ["I am a Software Engineer interested in doing innovative applications"], "address": [{"physicaladdress": "", "city": "VELLORE, TAMILNADU", "state": "", "zip": "", "country": ""}], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["javajamesb08"], "gender": [], "image": ["http://pbs.twimg.com/profile_images/776795258143113217/x9dvgvH3_normal.jpg"]}, "FS": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}}, "PAvFM": {"result": {"gender": 1, "name": 1}, "PA": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON><PERSON><PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": [], "bio": [], "address": [{"physicaladdress": "", "city": "", "state": "", "zip": "", "country": "US"}, {"physicaladdress": "", "city": "", "state": "", "zip": "", "country": "NL"}], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["james.richard.54943", "<PERSON><PERSON><PERSON><PERSON>", "jaymesanto"], "gender": ["male"], "image": ["http://graph.facebook.com/100002250047930/picture?type=large", "http://graph.facebook.com/*********/picture?type=large", "http://graph.facebook.com/100001592374262/picture?type=large"]}, "FM": {"ipaddress": [], "email": ["<EMAIL>"], "name": [{"salutation": "", "firstname": "JAMES", "middlename": "", "surname": "ANTO", "suffix": ""}], "companyname": [], "bio": [], "address": [{"physicaladdress": "", "city": "NEW YORK CITY", "state": "NY", "zip": "", "country": "US"}, null], "geocode": [{"lat": 40.702898, "lon": -74.010241}], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": ["M"], "image": []}}, "FMvTW": {"result": {"name": 1}, "TW": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "JAMES", "middlename": "", "surname": "ANTO", "suffix": ""}], "companyname": [], "bio": ["I am a Software Engineer interested in doing innovative applications"], "address": [{"physicaladdress": "", "city": "VELLORE, TAMILNADU", "state": "", "zip": "", "country": ""}], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["javajamesb08"], "gender": [], "image": ["http://pbs.twimg.com/profile_images/776795258143113217/x9dvgvH3_normal.jpg"]}, "FM": {"ipaddress": [], "email": ["<EMAIL>"], "name": [{"salutation": "", "firstname": "JAMES", "middlename": "", "surname": "ANTO", "suffix": ""}], "companyname": [], "bio": [], "address": [{"physicaladdress": "", "city": "NEW YORK CITY", "state": "NY", "zip": "", "country": "US"}, null], "geocode": [{"lat": 40.702898, "lon": -74.010241}], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": ["M"], "image": []}}, "FCvFB": {"result": {}, "FB": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}, "FC": {"ipaddress": [], "email": ["<EMAIL>"], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": ["Bosco its", "Java Developer"], "bio": [], "address": [{"physicaladdress": "", "city": "<PERSON><PERSON><PERSON>", "state": "Tamil Nadu", "zip": "", "country": "IN"}], "geocode": [], "nationalid": [], "dob": ["19920101"], "mobilenumber": [], "username": [], "gender": [], "image": ["https://d2ojpxxtu63wzl.cloudfront.net/static/fd9f3d9a271e7ec3d75e6cbc6663002d_1730c825f2507f731157646371ec2c04ba5e0ff58b3caa0e34d3b3300b9a82b9", "https://d2ojpxxtu63wzl.cloudfront.net/static/7ce31797eea8c1149da8ebdfa7b5e25e_3544530d8894874171fe9c3c75658653ecb977a2b51b5ee0a645e3c061e9d928", "https://d2ojpxxtu63wzl.cloudfront.net/static/c5afe8580e437cfdd4805c808e70423f_f7a16c31eebd19252c813b7a6870f743390615ca207035e2eee20376acfbba45"]}}, "FCvPA": {"result": {"name": 1}, "PA": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON><PERSON><PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": [], "bio": [], "address": [{"physicaladdress": "", "city": "", "state": "", "zip": "", "country": "US"}, {"physicaladdress": "", "city": "", "state": "", "zip": "", "country": "NL"}], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["james.richard.54943", "<PERSON><PERSON><PERSON><PERSON>", "jaymesanto"], "gender": ["male"], "image": ["http://graph.facebook.com/100002250047930/picture?type=large", "http://graph.facebook.com/*********/picture?type=large", "http://graph.facebook.com/100001592374262/picture?type=large"]}, "FC": {"ipaddress": [], "email": ["<EMAIL>"], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": ["Bosco its", "Java Developer"], "bio": [], "address": [{"physicaladdress": "", "city": "<PERSON><PERSON><PERSON>", "state": "Tamil Nadu", "zip": "", "country": "IN"}], "geocode": [], "nationalid": [], "dob": ["19920101"], "mobilenumber": [], "username": [], "gender": [], "image": ["https://d2ojpxxtu63wzl.cloudfront.net/static/fd9f3d9a271e7ec3d75e6cbc6663002d_1730c825f2507f731157646371ec2c04ba5e0ff58b3caa0e34d3b3300b9a82b9", "https://d2ojpxxtu63wzl.cloudfront.net/static/7ce31797eea8c1149da8ebdfa7b5e25e_3544530d8894874171fe9c3c75658653ecb977a2b51b5ee0a645e3c061e9d928", "https://d2ojpxxtu63wzl.cloudfront.net/static/c5afe8580e437cfdd4805c808e70423f_f7a16c31eebd19252c813b7a6870f743390615ca207035e2eee20376acfbba45"]}}, "FMvFB": {"result": {}, "FM": {"ipaddress": [], "email": ["<EMAIL>"], "name": [{"salutation": "", "firstname": "JAMES", "middlename": "", "surname": "ANTO", "suffix": ""}], "companyname": [], "bio": [], "address": [{"physicaladdress": "", "city": "NEW YORK CITY", "state": "NY", "zip": "", "country": "US"}, null], "geocode": [{"lat": 40.702898, "lon": -74.010241}], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": ["M"], "image": []}, "FB": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}}, "FCvPC": {"result": {}, "PC": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}, "FC": {"ipaddress": [], "email": ["<EMAIL>"], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": ["Bosco its", "Java Developer"], "bio": [], "address": [{"physicaladdress": "", "city": "<PERSON><PERSON><PERSON>", "state": "Tamil Nadu", "zip": "", "country": "IN"}], "geocode": [], "nationalid": [], "dob": ["19920101"], "mobilenumber": [], "username": [], "gender": [], "image": ["https://d2ojpxxtu63wzl.cloudfront.net/static/fd9f3d9a271e7ec3d75e6cbc6663002d_1730c825f2507f731157646371ec2c04ba5e0ff58b3caa0e34d3b3300b9a82b9", "https://d2ojpxxtu63wzl.cloudfront.net/static/7ce31797eea8c1149da8ebdfa7b5e25e_3544530d8894874171fe9c3c75658653ecb977a2b51b5ee0a645e3c061e9d928", "https://d2ojpxxtu63wzl.cloudfront.net/static/c5afe8580e437cfdd4805c808e70423f_f7a16c31eebd19252c813b7a6870f743390615ca207035e2eee20376acfbba45"]}}, "FMvPI": {"result": {"gender": 1, "name": 1}, "FM": {"ipaddress": [], "email": ["<EMAIL>"], "name": [{"salutation": "", "firstname": "JAMES", "middlename": "", "surname": "ANTO", "suffix": ""}], "companyname": [], "bio": [], "address": [{"physicaladdress": "", "city": "NEW YORK CITY", "state": "NY", "zip": "", "country": "US"}, null], "geocode": [{"lat": 40.702898, "lon": -74.010241}], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": ["M"], "image": []}, "PI": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["james.anto.7"], "gender": ["male"], "image": ["http://graph.facebook.com/1266550805/picture?type=large"]}}, "FSvPA": {"result": {}, "PA": {"ipaddress": [], "email": [], "name": [{"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON><PERSON><PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}, {"salutation": "", "firstname": "<PERSON>", "middlename": "", "surname": "<PERSON><PERSON>", "suffix": ""}], "companyname": [], "bio": [], "address": [{"physicaladdress": "", "city": "", "state": "", "zip": "", "country": "US"}, {"physicaladdress": "", "city": "", "state": "", "zip": "", "country": "NL"}], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": ["james.richard.54943", "<PERSON><PERSON><PERSON><PERSON>", "jaymesanto"], "gender": ["male"], "image": ["http://graph.facebook.com/100002250047930/picture?type=large", "http://graph.facebook.com/*********/picture?type=large", "http://graph.facebook.com/100001592374262/picture?type=large"]}, "FS": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}}, "FSvPC": {"result": {}, "PC": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}, "FS": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}}, "FMvPC": {"result": {}, "PC": {"ipaddress": [], "email": [], "name": [], "companyname": [], "bio": [], "address": [], "geocode": [], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": [], "image": []}, "FM": {"ipaddress": [], "email": ["<EMAIL>"], "name": [{"salutation": "", "firstname": "JAMES", "middlename": "", "surname": "ANTO", "suffix": ""}], "companyname": [], "bio": [], "address": [{"physicaladdress": "", "city": "NEW YORK CITY", "state": "NY", "zip": "", "country": "US"}, null], "geocode": [{"lat": 40.702898, "lon": -74.010241}], "nationalid": [], "dob": [], "mobilenumber": [], "username": [], "gender": ["M"], "image": []}}}, "versionApplied": "2", "fraudScoreInfo": {"primary": {"score": 0.024693052768707258, "model": {"id": 34, "name": "gyft_model_rf2", "url": "https://socure-prediction-prod.socure.com/predictors/predict/gyft_model_rf2", "identifier": "gyft_model_rf2", "version": "1.0", "created_date": 1479686400000, "last_updated_date": 1479686400000}, "params": {"data": {"PBVAL.200259": 0, "WPVAL.100053": 0, "WPVAL.100050": 0, "R559": 0, "PBVAL.200210": 0, "NSVAL.100071": 0, "I326": 0, "WPVAL.300043": "false", "NSVAL.300012": 0, "NSVAL.100123": 0, "NSVAL.300018": 60, "WPVAL.300046": 1385, "PBvNS.name": 1, "PBVAL.200128": 0, "FCvNS.name": 1, "PBVAL.200205": 0, "I330": 1, "I322": 0, "confidence": 0.9486, "PBVAL.200216": 0, "profilesFoundCount": 11, "I240": 1, "FCVAL.200127": 4, "I121": 1, "PBVAL.200260": 0, "FV.email": 0.99, "PBVAL.200209": 0, "I329": 1, "NSVAL.300024": "U", "PBVAL.200211": 0, "I556": 1, "PBVAL.200215": 19, "WPVAL.100002": 0, "PBvFC.name": 1, "I552": 0, "PBVAL.200214": 18, "I332": 0, "FCVAL.200128": 5, "PBVAL.200208": 3, "PBVAL.200261": 18, "authScore": 10, "WPVAL.100051": 0, "PBVAL.200246": 0, "PBVAL.200203": 14, "FCvFM.name": 1, "WPVAL.300048": 7817, "PBVAL.200235": 0, "NSVAL.100073": 0, "FV.address": 0.99, "I334": 0, "PBVAL.200212": 0, "PBVAL.200201": 19, "FV.name": 0.99, "PBvFM.name": 1, "PBVAL.200213": 0, "FMVAL.200150": 0.01, "WPVAL.300044": "No name found", "FMvNS.name": 1, "PBVAL.200244": 0, "I602": 0, "NSVAL.300009": 0}}, "response": {"data": {"class0": 0.9753069472312927, "class1": 0.024693052768707258, "predictorType": "binomial"}, "status": "ok"}}, "custom": {"Generic.US.Turo__1.0": {"score": 0.0571096080541611, "model": {"id": 25, "name": "Generic.US.Turo", "url": "https://socure-prediction-prod.socure.com/predictors/predict/generic_us_tsadjust_rf2", "identifier": "Generic.US.Turo", "version": "1.0", "created_date": 1479859200000, "last_updated_date": 1479859200000}, "params": {"data": {"ASANL.100500": 0.9746, "R606": 0, "WPVAL.100053": 0, "state": "NY", "COVAL.400013": 0.8935, "PBVAL.200210": 0, "NSVAL.100071": 0, "NSVAL.300012": 0, "PBVAL.200247": 0, "NSVAL.300018": 60, "WPVAL.300046": 1385, "FCvNS.name": 1, "PBVAL.200205": 0, "WPVAL.100052": 1, "confidence": 0.9486, "I553": 1, "FCVAL.200127": 4, "PBVAL.200204": 0, "NSVAL.300024": "U", "I556": 1, "PBVAL.200215": 19, "PBvFC.name": 1, "I552": 0, "PBVAL.200214": 18, "ASANL.100501": 0.8935, "FCVAL.200128": 5, "NSVAL.300021": 0, "authScore": 10, "PBVAL.200203": 14, "FCvFM.name": 1, "WPVAL.300048": 7817, "FV.address": 0.99, "NSVAL.100072": 1, "PBVAL.200201": 19, "FV.name": 0.99, "NSVAL.300001": 0, "PBVAL.200213": 0, "FMVAL.200150": 0.01, "WPVAL.300044": "No name found", "PBVAL.200244": 0, "TLD": "com", "NSVAL.300009": 0}}, "response": {"data": {"class0": 0.9428903919458389, "class1": 0.0571096080541611, "predictorType": "binomial"}, "status": "ok"}}}}, "correlationScoreInfo": {"nameAddress": {"score": 0.01, "model": {"id": -1, "name": "Correlation Model - Name vs Address (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/drf_name_address_v6", "identifier": "default name_address", "version": "1", "created_date": 1488543163245, "last_updated_date": 1488543163245}, "params": {"data": {"WPVAL.100050": 0, "NSVAL.300018": 60, "WPVAL.100051": 0, "FCvFM.name": null, "PBvFM.email": null, "PBvFM.name": 1, "FCVAL.100117": 1}}, "response": {"data": {"class0": 0.44239433109760284, "class1": 0.5576056689023972, "predictorType": "binomial"}, "status": "ok"}}, "nameEmail": {"score": 0.02, "model": {"id": -1, "name": "Correlation Model - Name vs Email (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/drf_name_email_v6", "identifier": "default name_email", "version": "2", "created_date": 1488543163245, "last_updated_date": 1488543163245}, "params": {"data": {"WPVAL.100050": 0, "NSVAL.300018": 60, "WPVAL.100051": 0, "FCvFM.name": null, "PBvFM.email": null, "PBvFM.name": 1, "FCVAL.100117": 1}}, "response": {"data": {"class0": 0.44239433109760284, "class1": 0.5576056689023972, "predictorType": "binomial"}, "status": "ok"}}, "namePhone": {"score": 0.03, "model": {"id": -1, "name": "Correlation Model - Name vs Phone (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/drf_name_phone_v6", "identifier": "default name_phone", "version": "3", "created_date": 1488543163245, "last_updated_date": 1488543163245}, "params": {"data": {"WPVAL.100050": 0, "NSVAL.300018": 60, "WPVAL.100051": 0, "FCvFM.name": null, "PBvFM.email": null, "PBvFM.name": 1, "FCVAL.100117": 1}}, "response": {"data": {"class0": 0.44239433109760284, "class1": 0.5576056689023972, "predictorType": "binomial"}, "status": "ok"}}}, "riskScoreInfo": {"address": {"score": 0.01, "model": {"id": -1, "name": "Address Risk Score Model", "url": "https://socure-prediction-stage.socure.com/predictors/predict/drf_name_address_v6", "identifier": "default address", "version": "1", "created_date": 1488543163245, "last_updated_date": 1488543163245}, "params": {"data": {"WPVAL.100050": 0, "NSVAL.300018": 60, "WPVAL.100051": 0, "FCvFM.name": null, "PBvFM.email": null, "PBvFM.name": 1, "FCVAL.100117": 1}}, "response": {"data": {"class0": 0.44239433109760284, "class1": 0.5576056689023972, "predictorType": "binomial"}, "status": "ok"}}, "email": {"score": 0.02, "model": {"id": -1, "name": "Email Risk Score Model", "url": "https://socure-prediction-stage.socure.com/predictors/predict/drf_name_email_v6", "identifier": "default email", "version": "2", "created_date": 1488543163245, "last_updated_date": 1488543163245}, "params": {"data": {"WPVAL.100050": 0, "NSVAL.300018": 60, "WPVAL.100051": 0, "FCvFM.name": null, "PBvFM.email": null, "PBvFM.name": 1, "FCVAL.100117": 1}}, "response": {"data": {"class0": 0.44239433109760284, "class1": 0.5576056689023972, "predictorType": "binomial"}, "status": "ok"}}, "phone": {"score": 0.03, "model": {"id": -1, "name": "Phone Risk Score Model", "url": "https://socure-prediction-stage.socure.com/predictors/predict/drf_name_phone_v6", "identifier": "default phone", "version": "3", "created_date": 1488543163245, "last_updated_date": 1488543163245}, "params": {"data": {"WPVAL.100050": 0, "NSVAL.300018": 60, "WPVAL.100051": 0, "FCvFM.name": null, "PBvFM.email": null, "PBvFM.name": 1, "FCVAL.100117": 1}}, "response": {"data": {"class0": 0.44239433109760284, "class1": 0.5576056689023972, "predictorType": "binomial"}, "status": "ok"}}}, "cachedTransactionId": null, "instanceId": "i-090f2ab887495e3a6", "cachesUsed": [], "categoricalValues": []}]