{"status": "Ok", "data": {"referenceid": "acd38ba7-1f76-4ebc-b689-42490860a948", "authscore": 5, "reasoncodes": ["I554", "I230", "I330", "I129", "I329", "I127", "I331", "R186", "I552", "I121", "R106"], "confidence": 0.06, "fraudscore": 0.1026, "custommodels": [], "details": [{"fieldValidation": {"address": "", "mobilenumber": "", "name": "", "email": ""}, "blacklisted": {"reason": "Violated ToS", "reporteddate": "2017-01-17", "industry": "Finance and Insurance"}, "profilesFound": ["https://facebook.com/***************"], "watchLists": {"EU CONSOLIDATED LIST.BDF": {"matchscore": "100", "matchfields": ["name", "dob"], "sourceUrls": []}, "OSFI CONSOLIDATED LIST.BDF": {"matchscore": "100", "matchfields": ["name", "dob"], "sourceUrls": []}, "OFAC SDN.BDF": {"matchscore": "100", "matchfields": ["name", "dob"], "sourceUrls": []}, "UN CONSOLIDATED LIST.BDF": {"matchscore": "100", "matchfields": ["name", "dob"], "sourceUrls": []}, "BANK OF ENGLAND CONSOLIDATED LIST.BDF": {"matchscore": "100", "matchfields": ["name", "dob"], "sourceUrls": []}}, "correlationScores": {"name_address_email": "0.44", "name_phone_email": "", "name_address_phone_email": "", "name_address_phone": ""}}], "debug": [{"rule_codes": [{"rulecode": "scoreComponents.PBVAL.200272", "score": "-0.1", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.200272"}, {"rulecode": "scoreComponents.PBVAL.200216", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200216"}, {"rulecode": "scoreComponents.WPVAL.100056", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100056"}, {"rulecode": "scoreComponents.ASANL.100502", "score": "0.1", "confidence": "0.0", "originalScore": "0.5", "ruleCodeShort": "ASANL.100502"}, {"rulecode": "scoreComponents.FMVAL.300002", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FMVAL.300002"}, {"rulecode": "scoreComponents.FMVAL.100126", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.100126"}, {"rulecode": "scoreComponents.WPVAL.700025", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.700025"}, {"rulecode": "scoreComponents.WPVAL.100026", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100026"}, {"rulecode": "scoreComponents.PBVAL.200205", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200205"}, {"rulecode": "scoreComponents.WPVAL.100025", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100025"}, {"rulecode": "scoreComponents.COVAL.400016", "score": "0.0", "confidence": "0.0", "originalScore": "0.4172", "ruleCodeShort": "COVAL.400016"}, {"rulecode": "scoreComponents.WPVAL.100021", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100021"}, {"rulecode": "scoreComponents.PBVAL.100120", "score": "0.3", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.100120"}, {"rulecode": "scoreComponents.FMVAL.100611", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.100611"}, {"rulecode": "scoreComponents.WPVAL.700047", "score": "0.0", "confidence": "0.0", "originalScore": "645.0", "ruleCodeShort": "WPVAL.700047"}, {"rulecode": "scoreComponents.GLOBAL.300003", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300003"}, {"rulecode": "scoreComponents.WPVAL.700028", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.700028"}, {"rulecode": "scoreComponents.WPVAL.100027", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100027"}, {"rulecode": "scoreComponents.GLOBAL.300038", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300038"}, {"rulecode": "scoreComponents.PBVAL.200246", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200246"}, {"rulecode": "scoreComponents.PBVAL.200252", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200252"}, {"rulecode": "scoreComponents.PBVAL.200201", "score": "0.1", "confidence": "0.0", "originalScore": "14.0", "ruleCodeShort": "PBVAL.200201"}, {"rulecode": "scoreComponents.FMVAL.100150", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FMVAL.100150"}, {"rulecode": "scoreComponents.GLOBAL.100143", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "GLOBAL.100143"}, {"rulecode": "scoreComponents.WPVAL.100048", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100048"}, {"rulecode": "scoreComponents.FMVAL.100140", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FMVAL.100140"}, {"rulecode": "scoreComponents.WPVAL.100020", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100020"}, {"rulecode": "scoreComponents.FMVAL.300003", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FMVAL.300003"}, {"rulecode": "scoreComponents.PBVAL.200278", "score": "0.0", "confidence": "0.0", "originalScore": "69.0", "ruleCodeShort": "PBVAL.200278"}, {"rulecode": "scoreComponents.WPVAL.100008", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100008"}, {"rulecode": "scoreComponents.WPVAL.300066", "score": "0.0", "confidence": "0.0", "originalScore": "125.0", "ruleCodeShort": "WPVAL.300066"}, {"rulecode": "scoreComponents.WPVAL.300048", "score": "0.0", "confidence": "0.0", "originalScore": "125.0", "ruleCodeShort": "WPVAL.300048"}, {"rulecode": "scoreComponents.PBVAL.200211", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200211"}, {"rulecode": "scoreComponents.PBVAL.200256", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200256"}, {"rulecode": "scoreComponents.WLVAL.100007", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WLVAL.100007"}, {"rulecode": "scoreComponents.FVANL.400003", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FVANL.400003"}, {"rulecode": "scoreComponents.COVAL.400013", "score": "0.0", "confidence": "0.0", "originalScore": "0.4602", "ruleCodeShort": "COVAL.400013"}, {"rulecode": "scoreComponents.PBVAL.200266", "score": "-0.1", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.200266"}, {"rulecode": "scoreComponents.WPVAL.100035", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100035"}, {"rulecode": "scoreComponents.PBVAL.200277", "score": "0.0", "confidence": "0.0", "originalScore": "0.66", "ruleCodeShort": "PBVAL.200277"}, {"rulecode": "scoreComponents.PBVAL.200265", "score": "-0.1", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.200265"}, {"rulecode": "scoreComponents.PBVAL.200281", "score": "0.0", "confidence": "0.0", "originalScore": "2.0", "ruleCodeShort": "PBVAL.200281"}, {"rulecode": "scoreComponents.FMVAL.200125", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FMVAL.200125"}, {"rulecode": "scoreComponents.PBVAL.200250", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200250"}, {"rulecode": "scoreComponents.GLOBAL.300042", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300042"}, {"rulecode": "scoreComponents.WPVAL.100013", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100013"}, {"rulecode": "scoreComponents.WPVAL.300046", "score": "0.0", "confidence": "0.0", "originalScore": "645.0", "ruleCodeShort": "WPVAL.300046"}, {"rulecode": "scoreComponents.PBVAL.200264", "score": "-0.1", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.200264"}, {"rulecode": "scoreComponents.PBVAL.200260", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200260"}, {"rulecode": "scoreComponents.PBVAL.200244", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200244"}, {"rulecode": "scoreComponents.ASANL.100500", "score": "0.4808", "confidence": "0.0", "originalScore": "0.4808", "ruleCodeShort": "ASANL.100500"}, {"rulecode": "scoreComponents.COVAL.400019", "score": "0.0", "confidence": "0.0", "originalScore": "0.4387", "ruleCodeShort": "COVAL.400019"}, {"rulecode": "scoreComponents.PBVAL.200209", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200209"}, {"rulecode": "scoreComponents.PBVAL.200203", "score": "0.1", "confidence": "0.0", "originalScore": "13.0", "ruleCodeShort": "PBVAL.200203"}, {"rulecode": "scoreComponents.PBVAL.200235", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200235"}, {"rulecode": "scoreComponents.PBVAL.200279", "score": "0.0", "confidence": "0.0", "originalScore": "27.0", "ruleCodeShort": "PBVAL.200279"}, {"rulecode": "scoreComponents.EMVAL.200125", "score": "0.49", "confidence": "0.1", "originalScore": "1.0", "ruleCodeShort": "EMVAL.200125"}, {"rulecode": "scoreComponents.PBVAL.200128", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200128"}, {"rulecode": "scoreComponents.GLOBAL.300043", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300043"}, {"rulecode": "scoreComponents.FSVAL.610001", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FSVAL.610001"}, {"rulecode": "scoreComponents.PBVAL.200215", "score": "0.1", "confidence": "0.0", "originalScore": "14.0", "ruleCodeShort": "PBVAL.200215"}, {"rulecode": "scoreComponents.PBVAL.200254", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200254"}, {"rulecode": "scoreComponents.WPVAL.100050", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100050"}, {"rulecode": "scoreComponents.WPVAL.300065", "score": "0.0", "confidence": "0.0", "originalScore": "645.0", "ruleCodeShort": "WPVAL.300065"}, {"rulecode": "scoreComponents.ASANL.100501", "score": "0.4387", "confidence": "0.0", "originalScore": "0.4387", "ruleCodeShort": "ASANL.100501"}, {"rulecode": "scoreComponents.PBVAL.200212", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200212"}, {"rulecode": "scoreComponents.PBVAL.200282", "score": "0.0", "confidence": "0.0", "originalScore": "46.0", "ruleCodeShort": "PBVAL.200282"}, {"rulecode": "scoreComponents.PBVAL.200204", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200204"}, {"rulecode": "scoreComponents.WPVAL.100039", "score": "0.02", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "WPVAL.100039"}, {"rulecode": "scoreComponents.WPVAL.100002", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100002"}, {"rulecode": "scoreComponents.WPVAL.100004", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100004"}, {"rulecode": "scoreComponents.WPVAL.100053", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100053"}, {"rulecode": "scoreComponents.PBVAL.200249", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200249"}, {"rulecode": "scoreComponents.WPVAL.100030", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100030"}, {"rulecode": "scoreComponents.FMVAL.300666", "score": "0.0", "confidence": "0.0", "originalScore": "79.0", "ruleCodeShort": "FMVAL.300666"}, {"rulecode": "scoreComponents.WPVAL.100003", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100003"}, {"rulecode": "scoreComponents.PBVAL.200253", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200253"}, {"rulecode": "scoreComponents.GLOBAL.300039", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "GLOBAL.300039"}, {"rulecode": "scoreComponents.ASANL.100503", "score": "0.732", "confidence": "0.0", "originalScore": "0.915", "ruleCodeShort": "ASANL.100503"}, {"rulecode": "scoreComponents.FCVAL.100117", "score": "-0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FCVAL.100117"}, {"rulecode": "scoreComponents.PBVAL.200268", "score": "-0.1", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.200268"}, {"rulecode": "scoreComponents.PBVAL.200255", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200255"}, {"rulecode": "scoreComponents.PBVAL.200259", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200259"}, {"rulecode": "scoreComponents.WPVAL.100049", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100049"}, {"rulecode": "scoreComponents.PBVAL.200261", "score": "0.0", "confidence": "0.0", "originalScore": "14.0", "ruleCodeShort": "PBVAL.200261"}, {"rulecode": "scoreComponents.PBVAL.200210", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200210"}, {"rulecode": "scoreComponents.PBVAL.200251", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200251"}, {"rulecode": "scoreComponents.PBVAL.200208", "score": "0.1", "confidence": "0.0", "originalScore": "2.0", "ruleCodeShort": "PBVAL.200208"}, {"rulecode": "scoreComponents.FVANL.400001", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FVANL.400001"}, {"rulecode": "scoreComponents.WPVAL.100060", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100060"}, {"rulecode": "scoreComponents.PBVAL.200247", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200247"}, {"rulecode": "scoreComponents.PBVAL.200243", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200243"}, {"rulecode": "scoreComponents.BLVAL.100001", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "BLVAL.100001"}, {"rulecode": "scoreComponents.FSVAL.600002", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FSVAL.600002"}, {"rulecode": "scoreComponents.FBVAL.100131", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FBVAL.100131"}, {"rulecode": "scoreComponents.PBVAL.200214", "score": "0.1", "confidence": "0.0", "originalScore": "14.0", "ruleCodeShort": "PBVAL.200214"}, {"rulecode": "scoreComponents.WPVAL.100052", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100052"}, {"rulecode": "scoreComponents.FMVAL.300004", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "FMVAL.300004"}, {"rulecode": "scoreComponents.EMVAL.100126", "score": "-0.51", "confidence": "0.1", "originalScore": "1.0", "ruleCodeShort": "EMVAL.100126"}, {"rulecode": "scoreComponents.PBVAL.200276", "score": "-0.1", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.200276"}, {"rulecode": "scoreComponents.PBVAL.200280", "score": "0.0", "confidence": "0.0", "originalScore": "10.0", "ruleCodeShort": "PBVAL.200280"}, {"rulecode": "scoreComponents.PBVAL.200283", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200283"}, {"rulecode": "scoreComponents.PBVAL.200245", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200245"}, {"rulecode": "scoreComponents.WPVAL.700053", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.700053"}, {"rulecode": "scoreComponents.PBVAL.200284", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200284"}, {"rulecode": "scoreComponents.WPVAL.100051", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100051"}, {"rulecode": "scoreComponents.WPVAL.100057", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100057"}, {"rulecode": "scoreComponents.FBVAL.200131", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FBVAL.200131"}, {"rulecode": "scoreComponents.WPVAL.100007", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100007"}, {"rulecode": "scoreComponents.PBVAL.200213", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200213"}, {"rulecode": "scoreComponents.PBVAL.200248", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "PBVAL.200248"}, {"rulecode": "scoreComponents.PBVAL.100119", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBVAL.100119"}, {"rulecode": "scoreComponents.WPVAL.100031", "score": "-0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100031"}, {"rulecode": "entityResolution.PBvFM.name", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "PBvFM.name"}], "raw_score": 4.808286049263366, "version_applied": "2.5", "fraud_score_info": {"primary": {"score": 0.1025725591182709, "model": {"id": -1, "name": "<PERSON><PERSON> (International)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/generic_int_tsadjust_rf1", "identifier": "generic_int_tsadjust_rf1", "version": "1.0", "created_date": 1517603170578, "last_updated_date": 1517603170578}, "params": null, "response": {"data": {"class0": 0.8974274408817291, "class1": 0.1025725591182709, "predictorType": "binomial"}, "status": "ok"}}, "custom": {}}, "correlation_score_info": {"name_address": {"score": 0.4172416409848304, "model": {"id": -1, "name": "Correlation Model - Name vs Address (International)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/gbm_int_name_address_v13", "identifier": "gbm_int_name_address_v13", "version": "5.0", "created_date": 1517603170580, "last_updated_date": 1517603170580}, "params": {"data": {"FMVAL.100142": null, "WPVAL.300050": null, "PBVAL.200259": 0, "WPVAL.100053": 0, "FCvFM.email": null, "WPVAL.300008": null, "FMVAL.500110": null, "FMVAL.100150": 0, "PBVAL.200210": 0, "WPVAL.100057": 0, "WPVAL.300005": null, "WPVAL.300046": null, "PBVAL.200128": 0, "WPVAL.300040": "true", "PBvFC.email": null, "WPVAL.300051": null, "WPVAL.100052": 0, "WPVAL.300039": "", "WPVAL.300009": null, "EMVAL.100126": 1, "PBVAL.200216": 0, "FMVAL.500103": null, "FCVAL.200127": null, "PBVAL.200260": 0, "TWVAL.200126": null, "PBVAL.200209": 0, "PBVAL.200127": null, "PBVAL.200211": 0, "TWANL.300011": null, "WPVAL.300041": "Domain does not support validation (accepts all mailboxes)", "PBVAL.200215": 14, "PBvFC.name": null, "PBVAL.200214": 14, "FCVAL.200128": null, "PBVAL.200208": 2, "PBVAL.200261": 14, "PBVAL.200246": 0, "PBVAL.200203": 13, "FCvFM.name": null, "PBVAL.200268": 1, "WPVAL.300048": null, "FMVAL.500101": null, "WPVAL.300007": null, "FMVAL.300666": 79, "PBVAL.200272": 1, "PBVAL.200212": 0, "PBVAL.200201": 14, "PBvFM.email": null, "PBvFM.name": 1, "PBVAL.200213": 0, "FMVAL.200150": null, "WPVAL.300044": "No name found", "FCVAL.100117": 1}}, "response": {"data": {"class0": 0.5827583590151696, "class1": 0.4172416409848304, "predictorType": "binomial"}, "status": "ok"}}, "name_email": {"score": 0.4601508906322912, "model": {"id": -1, "name": "Correlation Model - Name vs Email (International)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/gbm_int_name_email_v19", "identifier": "gbm_int_name_email_v19", "version": "7.0", "created_date": 1517603170580, "last_updated_date": 1517603170580}, "params": {"data": {"PBvFM.companyname": null, "FMVAL.100142": null, "PBVAL.200206": null, "WPVAL.300050": null, "WPVAL.100053": 0, "FCvFM.email": null, "WPVAL.300008": null, "FMVAL.500110": null, "FMVAL.100150": 0, "PBVAL.200210": 0, "WPVAL.300043": "false", "WPVAL.100057": 0, "WPVAL.300005": null, "WPVAL.300046": null, "PBVAL.200128": 0, "WPVAL.300040": "true", "WPVAL.300051": null, "WPVAL.100052": 0, "WPVAL.300039": "", "WPVAL.300009": null, "EMVAL.100126": 1, "WPVAL.100056": 0, "PBVAL.200216": 0, "FMVAL.500103": null, "FCVAL.200127": null, "PBVAL.200260": 0, "PBVAL.200209": 0, "PBVAL.200211": 0, "WPVAL.300041": "Domain does not support validation (accepts all mailboxes)", "PBVAL.200215": 14, "PBvFC.name": null, "PBVAL.200214": 14, "FCVAL.200128": null, "PBVAL.200208": 2, "PBVAL.200261": 14, "PBVAL.200246": 0, "PBVAL.200203": 13, "FCvFM.name": null, "PBVAL.200268": 1, "WPVAL.300048": null, "WPVAL.300007": null, "FMVAL.300666": 79, "PBVAL.200272": 1, "PBVAL.200212": 0, "PBVAL.200201": 14, "PBvFM.email": null, "PBvFM.name": 1, "PBVAL.200213": 0, "FMVAL.200150": null, "WPVAL.300044": "No name found"}}, "response": {"data": {"class0": 0.5398491093677088, "class1": 0.4601508906322912, "predictorType": "binomial"}, "status": "ok"}}, "name_phone": null}, "instance_id": "i-02e721fa512f98c1c", "caches_used": [], "categorical_values": [{"rulecode": "FMVAL.300001", "value": "Other"}, {"rulecode": "FMVAL.400006", "value": "20180202"}, {"rulecode": "WPVAL.300040", "value": "true"}, {"rulecode": "WPVAL.300045", "value": "2016-04-28"}, {"rulecode": "WPVAL.300044", "value": "No name found"}, {"rulecode": "WPVAL.300039", "value": ""}, {"rulecode": "WPVAL.300014", "value": ""}, {"rulecode": "WPVAL.300043", "value": "false"}, {"rulecode": "WPVAL.300002", "value": "false"}, {"rulecode": "WPVAL.300053", "value": "FALSE"}, {"rulecode": "WPVAL.300042", "value": "false"}, {"rulecode": "WPVAL.300047", "value": "2017-09-30"}, {"rulecode": "WPVAL.300001", "value": ""}, {"rulecode": "WPVAL.300041", "value": "Domain does not support validation (accepts all mailboxes)"}], "query_plan": {"vendors": ["WLVAL", "FMVAL", "WPVAL", "PBVAL", "EMVAL", "PNANL", "VDANL", "FVANL", "PNVAL", "BLVAL", "INANL", "LIANL", "TWANL", "XNANL", "LIVAL", "FSVAL", "IMVAL", "TWVAL", "GPVAL", "FBANL", "GPANL", "FBVAL", "FCVAL"], "id_plus_models": {"fraud_models": {"primary": {"id": -1, "name": "<PERSON><PERSON> (International)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/generic_int_tsadjust_rf1", "identifier": "generic_int_tsadjust_rf1", "version": "1.0", "created_date": 1517603170578, "last_updated_date": 1517603170578}, "secondary": []}, "correlation_models": {"name_email": {"id": -1, "name": "Correlation Model - Name vs Email (International)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/gbm_int_name_email_v19", "identifier": "gbm_int_name_email_v19", "version": "7.0", "created_date": 1517603170580, "last_updated_date": 1517603170580}, "name_address": {"id": -1, "name": "Correlation Model - Name vs Address (International)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/gbm_int_name_address_v13", "identifier": "gbm_int_name_address_v13", "version": "5.0", "created_date": 1517603170580, "last_updated_date": 1517603170580}, "name_phone": {"id": -1, "name": "Correlation Model - Name vs Phone (International)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/gbm_int_name_phone_v28", "identifier": "gbm_int_name_phone_v28", "version": "5.0", "created_date": 1517603170580, "last_updated_date": 1517603170580}}, "risk_models": {"address": {"id": -1, "name": "Address Risk Model (International)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/drf_name_address_v21", "identifier": "drf_name_address_v21", "version": "4.0", "created_date": 1517603170581, "last_updated_date": 1517603170581}, "email": {"id": -1, "name": "Email Risk Model (International)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/drf_name_address_v21", "identifier": "drf_name_address_v21", "version": "4.0", "created_date": 1517603170581, "last_updated_date": 1517603170581}}}, "id_plus_features": {"requested_features": ["Watchlist"], "resolved_features": ["Watchlist"], "outputted_features": ["Watchlist"]}, "scheduler_timeout": {"length": 5000, "unit": "MILLISECONDS"}, "vendor_timeouts": {"WLVAL": {"length": 5000, "unit": "MILLISECONDS"}, "FMVAL": {"length": 5000, "unit": "MILLISECONDS"}, "WPVAL": {"length": 5000, "unit": "MILLISECONDS"}, "PBVAL": {"length": 5000, "unit": "MILLISECONDS"}, "EMVAL": {"length": 5000, "unit": "MILLISECONDS"}, "PNANL": {"length": 5000, "unit": "MILLISECONDS"}, "VDANL": {"length": 5000, "unit": "MILLISECONDS"}, "FVANL": {"length": 5000, "unit": "MILLISECONDS"}, "PNVAL": {"length": 4000, "unit": "MILLISECONDS"}, "BLVAL": {"length": 1000, "unit": "MILLISECONDS"}, "INANL": {"length": 5000, "unit": "MILLISECONDS"}, "LIANL": {"length": 5000, "unit": "MILLISECONDS"}, "TWANL": {"length": 4000, "unit": "MILLISECONDS"}, "XNANL": {"length": 5000, "unit": "MILLISECONDS"}, "LIVAL": {"length": 5000, "unit": "MILLISECONDS"}, "FSVAL": {"length": 5000, "unit": "MILLISECONDS"}, "IMVAL": {"length": 5000, "unit": "MILLISECONDS"}, "TWVAL": {"length": 5000, "unit": "MILLISECONDS"}, "GPVAL": {"length": 5000, "unit": "MILLISECONDS"}, "FBANL": {"length": 4000, "unit": "MILLISECONDS"}, "GPANL": {"length": 4000, "unit": "MILLISECONDS"}, "FBVAL": {"length": 1000, "unit": "MILLISECONDS"}, "FCVAL": {"length": 5000, "unit": "MILLISECONDS"}}}}], "uuid": "502165c2-0a42-4b0f-b0be-61485fbaceec"}, "msg": ""}