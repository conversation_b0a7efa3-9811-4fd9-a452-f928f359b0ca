{"referenceId": "d5b6d95c-ed4e-4e0a-88a0-7156fca38a76", "blacklist": {"reasonCodes": [], "matches": []}, "nameAddressCorrelation": {"reasonCodes": ["I708"], "score": 0.9915}, "nameEmailCorrelation": {"reasonCodes": ["R551"], "score": 0.3075}, "namePhoneCorrelation": {"reasonCodes": ["I618"], "score": 0.9463}, "fraud": {"reasonCodes": ["I704", "I708", "I618", "I609", "I707", "R551", "R606", "I611", "I602"], "scores": [{"name": "generic", "version": "3.0", "score": 0.0697}, {"name": "My TestModel-1", "version": "11.1", "score": 0.1665}]}, "social": {"profilesFound": [], "reasonCodes": []}, "addressRisk": {"score": 0.1152, "reasonCodes": ["I704", "I708", "I720", "I707"]}, "emailRisk": {"score": 0.9945, "reasonCodes": ["R551", "R520"]}, "phoneRisk": {"score": 0.9953, "reasonCodes": ["R620", "R606", "I611", "I618", "I602", "I609"]}}