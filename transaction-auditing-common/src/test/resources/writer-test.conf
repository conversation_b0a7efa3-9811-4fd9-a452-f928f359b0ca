withDBMigration=false

threadpool {
  poolSize=50
}

pipeline {

  region = "us-east-1"

  parallelism = 3

  buffering {
    //try to buffer upto this count. It could go upto targetCount + (receive.message.count)
    targetCount = 5 //waits upto maxDuration to receive targetCount, it could be less, exact or more
    //how long to wait for the targetCount
    maxDuration = "1 minutes"
  }

  transaction {
    sqs {
      queue.name = "transaction-auditing-stage"
      receive.message.count = 10
      visibility.timeout.seconds = "900" //15 minutes
      large.files.s3.bucket.name = "sqs-storage-stage"
      s3.masking-failures.bucket = "stage-audit-errors/pii_mask_failures"
    }
  }

  thirdparty {
    sqs {
      queue.name = "third-party-transaction-auditing-stage"
      receive.message.count = 10
      visibility.timeout.seconds = "900" //15 minutes
      large.files.s3.bucket.name = "thirdparty-stats-stage"
    }
  }
}

