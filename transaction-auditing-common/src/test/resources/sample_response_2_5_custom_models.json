{"status": "Ok", "data": {"referenceid": "a8aa4e1f-6144-439c-a260-c960c2fb97f7", "authscore": 4, "reasoncodes": ["I144", "I916", "I252", "I186", "I241", "I158", "I901", "I182", "I326", "I230", "I159", "I172", "I129", "I322", "I333", "I240", "I553", "I611", "I614", "I329", "I127", "I704", "I254", "I556", "I180", "I906", "I917", "I250", "I178", "I708", "I156", "I253", "R702", "I618", "I555", "I260", "I707", "I602", "I157", "I257", "I121", "R106"], "confidence": 0.33, "fraudscore": 0.0508, "custommodels": [{"name": "vesta_f1_022017_ta_model_gbm2", "version": "1.0", "score": 0.0012}, {"name": "xoom_oct2016_combine_sender_v1f1_model_rf2", "version": "1.02", "score": 0.0291}, {"name": "radiusbank_model_glm1", "version": "1.0", "score": 0.0508}], "details": [{"fieldValidation": {"address": "", "mobilenumber": "", "name": "", "email": ""}, "blacklisted": {"reason": "Spammer", "reporteddate": "2017-04-28", "industry": "No Industry"}, "profilesFound": ["https://www.facebook.com/azazeldt", "https://www.flickr.com/people/********@N06", "http://www.linkedin.com/in/roger-plotz-2729969", "https://angel.co/azazeldt", "https://myspace.com/azazeldt", "https://plus.google.com/117247490108010810232", "http://www.facebook.com/people/_/*********", "http://www.amazon.com/gp/pdp/profile/A2YV0MN55C2FG4/", "http://www.hi5.com/friend/p100155053--profile--html", "https://twitter.com/azazeldt", "http://www.amazon.com/wishlist/TKCEWUO7JDDI", "https://whitepages.plus/n/<PERSON>_<PERSON>/Grantsville_UT/032be7e7374eaaa901e1e0be70043837", "https://www.linkedin.com/in/roger-plotz-********", "https://whitepages.plus/n/<PERSON>_<PERSON>/San_francisco_CA/d5fff8071d9a0884182fff39d8b62784", "http://pinterest.com/azazeldt/", "http://vimeo.com/user10901589", "http://www.pinterest.com/azazeldt/", "https://twitter.com/?profile_id=14230203", "http://www.flickr.com/people/********@N06/"], "kyc": {"firstname": "0.99", "surname": "0.99", "streetaddress": "0.99", "city": "0.99", "state": "0.99", "zip": "0.99", "mobilenumber": "0.99"}, "correlationScores": {"name_address_email": "0.99", "name_phone_email": "0.99", "name_address_phone_email": "0.99", "name_address_phone": "0.99"}}], "debug": [{"rule_codes": [{"rulecode": "scoreComponents.TWANL.100009", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "TWANL.100009"}, {"rulecode": "scoreComponents.PBVAL.200216", "score": "0.1", "confidence": "0.0", "originalScore": "10.0", "ruleCodeShort": "PBVAL.200216"}, {"rulecode": "scoreComponents.NSVAL.300001", "score": "0.0", "confidence": "0.0", "originalScore": "99.0", "ruleCodeShort": "NSVAL.300001"}, {"rulecode": "scoreComponents.WPVAL.100056", "score": "0.0", "confidence": "0.0", "originalScore": "0.0", "ruleCodeShort": "WPVAL.100056"}, {"rulecode": "scoreComponents.FCVAL.300002", "score": "0.0", "confidence": "0.0", "originalScore": "1.0", "ruleCodeShort": "FCVAL.300002"}, {"rulecode": "scoreComponents.LNVAL.100128", "score": "0.0", "confidence": "0.0", "originalScore": "8.0", "ruleCodeShort": "LNVAL.100128"}], "raw_score": 3.****************, "version_applied": "2.5", "fraud_score_info": {"primary": {"score": 0.*****************, "model": {"id": 52, "name": "Xoom.US.********", "url": "https://socure-prediction-stage.socure.com/predictors/predict/radiusbank_model_glm1", "identifier": "radiusbank_model_glm1", "version": "1.0", "created_date": *************, "last_updated_date": *************}, "params": null, "response": {"data": {"class0": 0.****************, "class1": 0.*****************, "predictorType": "binomial"}, "status": "ok"}}, "custom": {"radiusbank_model_glm1__1.0": {"score": 0.*****************, "model": {"id": 55, "name": "radiusbank_model_glm1", "url": "https://socure-prediction-stage.socure.com/predictors/predict/radiusbank_model_glm1", "identifier": "radiusbank_model_glm1", "version": "1.0", "created_date": *************, "last_updated_date": *************}, "params": null, "response": {"data": {"class0": 0.****************, "class1": 0.*****************, "predictorType": "binomial"}, "status": "ok"}}}}, "correlation_score_info": {"name_address": {"score": 0.****************, "model": {"id": -1, "name": "Correlation Model - Name vs Address (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/name_addr_corr_gbm_final_v2", "identifier": "name_addr_corr_gbm_final_v2", "version": "5.0", "created_date": *************, "last_updated_date": *************}, "params": {"data": {"PBvFM.address": null, "WPVAL.100057": 0.0, "WPVAL.100034": 1.0, "WPVAL.300006": "Match", "WPVAL.100056": 0.0, "WPVAL.100027": 0.0, "NSVAL.300001": 99.0, "NSVAL.300002": 1.0, "WPVAL.100026": 1.0}}, "response": {"data": {"class0": 0.0037846894877475723, "class1": 0.****************, "predictorType": "binomial"}, "status": "ok"}}, "name_email": {"score": 0.9938626708346419, "model": {"id": -1, "name": "Correlation Model - Name vs Email (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/drf_name_email_v6", "identifier": "drf_name_email_v6", "version": "3.0", "created_date": *************, "last_updated_date": *************}, "params": {"data": {"WPVAL.100050": 1.0, "NSVAL.300018": 85.0, "WPVAL.100051": 0.0, "FCvFM.name": 1.0, "PBvFM.email": -0.6, "PBvFM.name": 1.0, "FCVAL.100117": null}}, "response": {"data": {"class0": 0.0061373291653580965, "class1": 0.9938626708346419, "predictorType": "binomial"}, "status": "ok"}}, "name_phone": {"score": 0.9996730769600254, "model": {"id": -1, "name": "Correlation Model - Name vs Phone (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/drf_name_phone_v3", "identifier": "drf_name_phone_v3", "version": "3.0", "created_date": *************, "last_updated_date": *************}, "params": {"data": {"PBvFM.mobilenumber": null, "WPVAL.100004": 0.0, "PBvFM.name": 1.0, "WPVAL.100003": 1.0, "NSVAL.300009": 99.0}}, "response": {"data": {"class0": 0.00032692303997464476, "class1": 0.9996730769600254, "predictorType": "binomial"}, "status": "ok"}}}, "instance_id": "i-02e721fa512f98c1c", "caches_used": [], "categorical_values": [{"rulecode": "FCVAL.300005", "value": "male"}, {"rulecode": "FMVAL.300001", "value": "gmail.com"}, {"rulecode": "FMVAL.400006", "value": "20180202"}, {"rulecode": "NSVAL.300027", "value": ""}, {"rulecode": "NSVAL.300023", "value": "U"}, {"rulecode": "NSVAL.300030", "value": "0"}, {"rulecode": "NSVAL.300004", "value": "H"}, {"rulecode": "NSVAL.300022", "value": "N"}, {"rulecode": "NSVAL.300025", "value": "W"}, {"rulecode": "NSVAL.300003", "value": "Y"}, {"rulecode": "NSVAL.300034", "value": "C"}, {"rulecode": "NSVAL.300015", "value": "U"}, {"rulecode": "NSVAL.300005", "value": "R"}, {"rulecode": "NSVAL.300029", "value": "0"}, {"rulecode": "NSVAL.300028", "value": "N"}, {"rulecode": "NSVAL.300024", "value": "A7"}, {"rulecode": "NSVAL.300007", "value": "N"}, {"rulecode": "NSVAL.300011", "value": "Y"}, {"rulecode": "NSVAL.300039", "value": "A7"}, {"rulecode": "NSVAL.300006", "value": "N"}, {"rulecode": "NSVAL.300014", "value": "CA"}, {"rulecode": "NSVAL.300016", "value": "C"}, {"rulecode": "NSVAL.300008", "value": "N"}, {"rulecode": "NSVAL.300013", "value": "Y"}, {"rulecode": "NSVAL.300031", "value": "0"}, {"rulecode": "WPVAL.300009", "value": "T-Mobile USA"}, {"rulecode": "WPVAL.300007", "value": "US"}, {"rulecode": "WPVAL.300040", "value": "true"}, {"rulecode": "WPVAL.300045", "value": "2013-08-20"}, {"rulecode": "WPVAL.300044", "value": "Match"}, {"rulecode": "WPVAL.300067", "value": "true"}, {"rulecode": "WPVAL.300039", "value": ""}, {"rulecode": "WPVAL.300005", "value": "Match"}, {"rulecode": "WPVAL.300008", "value": "Mobile"}, {"rulecode": "WPVAL.300014", "value": ""}, {"rulecode": "WPVAL.300018", "value": "true"}, {"rulecode": "WPVAL.300006", "value": "Match"}, {"rulecode": "WPVAL.300013", "value": "false"}, {"rulecode": "WPVAL.300015", "value": "Match"}, {"rulecode": "WPVAL.300043", "value": "false"}, {"rulecode": "WPVAL.300002", "value": "false"}, {"rulecode": "WPVAL.300053", "value": "FALSE"}, {"rulecode": "WPVAL.300042", "value": "false"}, {"rulecode": "WPVAL.300056", "value": "TRUE"}, {"rulecode": "WPVAL.300047", "value": "1995-08-13"}, {"rulecode": "WPVAL.300055", "value": "FALSE"}, {"rulecode": "WPVAL.300017", "value": "true"}, {"rulecode": "WPVAL.300001", "value": ""}, {"rulecode": "WPVAL.300041", "value": "Syntax OK, domain exists, and mailbox does not reject mail"}, {"rulecode": "WPVAL.300016", "value": "Multi unit"}], "query_plan": {"vendors": ["FMVAL"], "id_plus_models": {"fraud_models": {"primary": {"id": 52, "name": "Xoom.US.********", "url": "https://socure-prediction-stage.socure.com/predictors/predict/radiusbank_model_glm1", "identifier": "radiusbank_model_glm1", "version": "1.0", "created_date": *************, "last_updated_date": *************}, "secondary": [{"id": 63, "name": "xoom_oct2016_combine_sender_v1f1_model_rf2", "url": "https://socure-prediction-stage.socure.com/predictors/predict/xoom_oct2016_combine_sender_v1f1_model_rf2", "identifier": "Modified - xoomOct2016", "version": "1.02", "created_date": *************, "last_updated_date": *************}, {"id": 55, "name": "radiusbank_model_glm1", "url": "https://socure-prediction-stage.socure.com/predictors/predict/radiusbank_model_glm1", "identifier": "radiusbank_model_glm1", "version": "1.0", "created_date": *************, "last_updated_date": *************}, {"id": 58, "name": "vesta_f1_022017_ta_model_gbm2", "url": "https://socure-prediction-stage.socure.com/predictors/predict/vesta_f1_022017_ta_model_gbm2", "identifier": "vesta_f1_022017_ta_model_gbm2", "version": "1.0", "created_date": *************, "last_updated_date": *************}]}, "correlation_models": {"name_email": {"id": -1, "name": "Correlation Model - Name vs Email (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/drf_name_email_v6", "identifier": "drf_name_email_v6", "version": "3.0", "created_date": *************, "last_updated_date": *************}, "name_address": {"id": -1, "name": "Correlation Model - Name vs Address (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/name_addr_corr_gbm_final_v2", "identifier": "name_addr_corr_gbm_final_v2", "version": "5.0", "created_date": *************, "last_updated_date": *************}, "name_phone": {"id": -1, "name": "Correlation Model - Name vs Phone (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/drf_name_phone_v3", "identifier": "drf_name_phone_v3", "version": "3.0", "created_date": *************, "last_updated_date": *************}}, "risk_models": {"address": {"id": -1, "name": "Address Risk Model (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/address_risk_model_glm_v3", "identifier": "address_risk_model_glm_v3", "version": "2.0", "created_date": 1517603170581, "last_updated_date": 1517603170581}, "email": {"id": -1, "name": "Email Risk Model (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/email_risk_model_gbm_v2", "identifier": "email_risk_model_gbm_v2", "version": "3.0", "created_date": 1517603170581, "last_updated_date": 1517603170581}, "phone": {"id": -1, "name": "Phone Risk Model (US)", "url": "https://socure-prediction-stage.socure.com/predictors/predict/phone_risk_model_glm_v2", "identifier": "phone_risk_model_glm_v2", "version": "2.0", "created_date": 1517603170581, "last_updated_date": 1517603170581}}}, "id_plus_features": {"requested_features": ["KYC"], "resolved_features": ["KYC"], "outputted_features": ["KYC"]}, "scheduler_timeout": {"length": 5000, "unit": "MILLISECONDS"}, "vendor_timeouts": {"FMVAL": {"length": 5000, "unit": "MILLISECONDS"}}}}], "uuid": "b4aaab52-0985-460e-bc4b-368e91bb6594"}, "msg": ""}