<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>transaction-auditing</artifactId>
        <groupId>me.socure</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>transaction-auditing-services-modules</artifactId>

    <dependencies>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-ms-dependencies</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>transaction-encryption-decryptor</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>transaction-encryption-encryptor</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>transaction-auditing-common</artifactId>
            <version>${revision}</version>
        </dependency>
    </dependencies>


</project>
