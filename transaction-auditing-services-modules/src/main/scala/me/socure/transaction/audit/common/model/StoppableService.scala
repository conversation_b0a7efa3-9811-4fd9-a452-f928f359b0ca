package me.socure.transaction.audit.common.model

import scala.concurrent.{ExecutionContext, Future}

trait StoppableService {
  def start(): Future[Unit]

  def stop(): Unit
}

class CompositeStoppableServices(services: Set[StoppableService])(implicit ec: ExecutionContext) extends StoppableService {
  override def start(): Future[Unit] = {
    Future.sequence(services.map(_.start())).map(_ => ())
  }

  override def stop(): Unit = {
    services.foreach(_.stop())
  }
}
