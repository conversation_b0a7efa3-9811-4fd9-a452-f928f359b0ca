package me.socure.transaction.audit.common.modules

import com.amazonaws.auth.AWSCredentialsProviderChain
import com.amazonaws.encryptionsdk.caching.CachingCryptoMaterialsManager
import com.google.inject.{AbstractModule, Provides}
import com.typesafe.config.Config
import javax.inject.{Named, Singleton}
import me.socure.common.aws.ProfileBasedCredentialsProviderChain
import me.socure.common.clock.{Clock, RealClock}
import me.socure.common.environment.{AppRegion, AppRegionResolver}
import me.socure.crypto.key.common.{CachingCryptoMaterialsManagerFactory, CryptoMaterialsManagerAccountsService, CryptoMaterialsManagerAccountsServiceFactory, SocureCryptoMaterialsManagerFactory}
import me.socure.transaction.auditing.encryption.decryptor.AuditDataDecryptor
import me.socure.transaction.auditing.encryption.decryptor.v2.{AuditDataDecryptorV2, AuditDataDecryptorV2Factory}
import me.socure.transaction.auditing.encryption.encryptor.AuditDataEncryptor
import me.socure.transaction.auditing.encryption.encryptor.v2.{AuditDataEncryptorV2, AuditDataEncryptorV2Factory}
import me.socure.transactionauditing.common.factory.{AuditDataDecryptorFactory, AuditDataEncryptorFactory}
import net.codingwell.scalaguice.ScalaModule

import scala.concurrent.ExecutionContext

class TransactionAuditCommonModules extends AbstractModule with ScalaModule {
  override def configure(): Unit = {
    bind(classOf[RealClock]).toInstance(new RealClock)
    bind(classOf[Clock]).toInstance(new RealClock)
  }

  @Provides
  @Singleton
  def appRegion(): AppRegion = AppRegionResolver.resolve()

  @Provides
  @Singleton
  def getCryptoMaterialsManagerAccountsService(config: Config)(implicit ec: ExecutionContext): CryptoMaterialsManagerAccountsService = {
    CryptoMaterialsManagerAccountsServiceFactory.create(config)
  }

  @Provides
  @Named("awsCredentialChainProvider")
  @Singleton
  def getCredentialChainProvider: AWSCredentialsProviderChain = {
    new ProfileBasedCredentialsProviderChain
  }

  @Provides
  @Singleton
  def getCachingCryptoMaterialsManager(
                                        config: Config,
                                        cryptoMaterialsManagerAccountsService: CryptoMaterialsManagerAccountsService,
                                        @Named("awsCredentialChainProvider") awsCredentialProviderChain: AWSCredentialsProviderChain
                                      )(implicit ec: ExecutionContext): CachingCryptoMaterialsManager = {

    val socureCryptoMaterialsManager = SocureCryptoMaterialsManagerFactory.create(config, cryptoMaterialsManagerAccountsService, awsCredentialProviderChain)

    CachingCryptoMaterialsManagerFactory.create(
      cryptoMaterialsManager = socureCryptoMaterialsManager,
      maxAgeSeconds = config.getInt("client.specific.encryption.kms.cache.time.seconds")
    )
  }

  @Provides
  @Singleton
  def getAuditDataDecryptor(
                             config: Config,
                             cachingCryptoMaterialsManager: CachingCryptoMaterialsManager,
                             cryptoMaterialsManagerAccountsService: CryptoMaterialsManagerAccountsService
                           )(implicit ec: ExecutionContext): AuditDataDecryptor = {
    AuditDataDecryptorFactory.create(
      config = config,
      cryptoMaterialsManager = cachingCryptoMaterialsManager,
      cryptoMaterialsManagerAccountsService = cryptoMaterialsManagerAccountsService
    )
  }

  @Provides
  @Singleton
  def getAuditDataEncryptor(
                             config: Config,
                             cachingCryptoMaterialsManager: CachingCryptoMaterialsManager,
                             cryptoMaterialsManagerAccountsService: CryptoMaterialsManagerAccountsService
                           )(implicit ec: ExecutionContext): AuditDataEncryptor = {
    AuditDataEncryptorFactory.create(
      config = config,
      cryptoMaterialsManager = cachingCryptoMaterialsManager,
      cryptoMaterialsManagerAccountsService = cryptoMaterialsManagerAccountsService
    )
  }

  @Provides
  @Singleton
  def getAuditDataEncryptorV2(config: Config, appRegion: AppRegion)
                             (implicit ec: ExecutionContext): AuditDataEncryptorV2 = {
    AuditDataEncryptorV2Factory.create(
      config = config,
      appRegion = appRegion
    )
  }

  @Provides
  @Singleton
  def getAuditDataDecryptorV2(config: Config, appRegion: AppRegion)
                             (implicit ec: ExecutionContext): AuditDataDecryptorV2 = {
    AuditDataDecryptorV2Factory.create(
      config = config,
      appRegion = appRegion
    )
  }
}
