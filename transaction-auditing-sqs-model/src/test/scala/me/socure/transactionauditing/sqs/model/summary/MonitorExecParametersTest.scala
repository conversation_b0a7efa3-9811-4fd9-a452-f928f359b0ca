package me.socure.transactionauditing.sqs.model.summary

import me.socure.transactionauditing.common.constants.Constants
import org.json4s.{DefaultFormats, Formats}
import org.scalatest.{FunSuite, Matchers}

class MonitorExecParametersTest extends FunSuite with Matchers {
  implicit val jsonFormats: Formats = DefaultFormats
  test("should return expected output") {
    val res = MonitorExecParameters.apply1(
      transactionIdBase = Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"),
      apiName = Some(Constants.emailAuthScoreV3ApiName),
      accountIdBase = Some(744731),
      environmentIdBase = Some(2),
      parameters = Some("{\"country\":\"us\",\"ipaddress\":null,\"firstname\":\"abu\",\"addresses\":null,\"mobilenumber\":null,\"impersonatorapikey\":null,\"submissiondate\":null,\"city\":null,\"payments\":null,\"userid\":null,\"nationalid\":null,\"deviceSessionId\":null,\"physicaladdress2\":null,\"surname\":\"bakhar\",\"accountcreationdate\":null,\"orderamount\":null,\"watchlistfilters\":{\"categories\":[\"adverseMedia\"],\"matchingCriteria\":{\"nameDob\":false,\"nameFuzzinessTolerance\":0.5,\"dobFuzzinessTolerance\":true},\"limit\":100,\"countries\":[\"gb\"]},\"nocache\":0,\"prevordercount\":0,\"details\":false,\"state\":null,\"runid\":null,\"isConsenting\":null,\"customeruserid\":\"\",\"vendors\":null,\"email\":\"<EMAIL>\",\"zip\":null,\"debug\":false,\"documentuuid\":null,\"orderchannel\":null,\"datascience\":false,\"lastorderdate\":null,\"modules\":[\"watchlistpremier\"],\"physicaladdress\":null,\"driverlicensestate\":null,\"forcerefresh\":true,\"companyname\":null,\"dob\":\"\",\"geocode\":null,\"socurekey\":\"3f4e62a0-6424-4117-a593-baae2aefd43c\",\"fullname\":null,\"device\":null,\"driverlicense\":null,\"entityName\":null,\"entityType\":null}"),
      debug = Some("[{\"rule_codes\":[{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100007\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100007\"},{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100135\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100135\"},{\"rulecode\":\"scoreComponents.GLOBAL.300003\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"0.0\",\"ruleCodeShort\":\"GLOBAL.300003\"},{\"rulecode\":\"scoreComponents.GLOBAL.100143\",\"score\":\"-0.5\",\"confidence\":\"0.05\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"GLOBAL.100143\"}],\"raw_score\":4.5,\"version_applied\":\"3\",\"instance_id\":\"i-08d515f999dc35904\",\"caches_used\":[],\"categorical_values\":[],\"query_plan\":{\"vendors\":[\"COMPLY_WLVAL\"],\"id_plus_models\":{\"correlation_models\":{},\"risk_models\":{}},\"id_plus_features\":{\"requested_features\":[\"Watchlist3_0\"],\"resolved_features\":[\"Watchlist3_0\"],\"outputted_features\":[\"Watchlist3_0\"]},\"scheduler_timeout\":{\"length\":5000,\"unit\":\"MILLISECONDS\"},\"vendor_timeouts\":{\"COMPLY_WLVAL\":{\"length\":5000,\"unit\":\"MILLISECONDS\"}}},\"http_status\":200,\"reason_codes\":[\"R186\"],\"watchlist3_0_settings\":{\"searchId\":********,\"dobAndName\":false,\"dobTolerance\":true,\"dobMatchLogic\":\"exactDob\",\"fuzzinessTolerance\":0.5,\"screeningCategory\":[\"adverse-media\"],\"limit\":10,\"country\":[],\"monitoring\":false,\"sourceNameSet\":[\"Adverse Media Financial Crime\",\"Adverse Media General\",\"Adverse Media Fraud\"],\"watchlistTransactionType\":\"hybrid\",\"invokedVendors\":[\"ComplyAdvantage\",\"InHouseScraper\"]}}]"),
      transactionDate = None
    )
    res shouldBe MonitorExecParameters(Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"), Some("abu"), Some("bakhar"), Some(false), Some(true), Some("exactDob"), Some(0.5), None, Some("watchlistpremier"), Some(********), Some(744731), Some(2), Some(true), None, None, None, None, Some(Set("adverse-media")), Some(10), Some(Set.empty), Some(Set("Adverse Media Financial Crime", "Adverse Media General", "Adverse Media Fraud")), Some(true), None, None, Some("us"), Some("hybrid"), None, None,None, None, None, Some("<EMAIL>"), None, None,None, None, Some(Set("ComplyAdvantage", "InHouseScraper")))
  }

  test("should return expected output with encrypted emailauthscore api") {
    val res = MonitorExecParameters.apply1(
      transactionIdBase = Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"),
      apiName = Some(Constants.encryptedEmailAuthScoreV3ApiName),
      accountIdBase = Some(744731),
      environmentIdBase = Some(2),
      parameters = Some("{\"country\":\"us\",\"ipaddress\":null,\"firstname\":\"abu\",\"addresses\":null,\"mobilenumber\":null,\"impersonatorapikey\":null,\"submissiondate\":null,\"city\":null,\"payments\":null,\"userid\":null,\"nationalid\":null,\"deviceSessionId\":null,\"physicaladdress2\":null,\"surname\":\"bakhar\",\"accountcreationdate\":null,\"orderamount\":null,\"watchlistfilters\":{\"categories\":[\"adverseMedia\"],\"matchingCriteria\":{\"nameDob\":false,\"nameFuzzinessTolerance\":0.5,\"dobFuzzinessTolerance\":true},\"limit\":100,\"countries\":[\"gb\"]},\"nocache\":0,\"prevordercount\":0,\"details\":false,\"state\":null,\"runid\":null,\"isConsenting\":null,\"customeruserid\":\"\",\"vendors\":null,\"email\":\"<EMAIL>\",\"zip\":null,\"debug\":false,\"documentuuid\":null,\"orderchannel\":null,\"datascience\":false,\"lastorderdate\":null,\"modules\":[\"watchlistpremier\"],\"physicaladdress\":null,\"driverlicensestate\":null,\"forcerefresh\":true,\"companyname\":null,\"dob\":\"\",\"geocode\":null,\"socurekey\":\"3f4e62a0-6424-4117-a593-baae2aefd43c\",\"fullname\":null,\"device\":null,\"driverlicense\":null,\"entityName\":null,\"entityType\":null}"),
      debug = Some("[{\"rule_codes\":[{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100007\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100007\"},{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100135\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100135\"},{\"rulecode\":\"scoreComponents.GLOBAL.300003\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"0.0\",\"ruleCodeShort\":\"GLOBAL.300003\"},{\"rulecode\":\"scoreComponents.GLOBAL.100143\",\"score\":\"-0.5\",\"confidence\":\"0.05\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"GLOBAL.100143\"}],\"raw_score\":4.5,\"version_applied\":\"3\",\"instance_id\":\"i-08d515f999dc35904\",\"caches_used\":[],\"categorical_values\":[],\"query_plan\":{\"vendors\":[\"COMPLY_WLVAL\"],\"id_plus_models\":{\"correlation_models\":{},\"risk_models\":{}},\"id_plus_features\":{\"requested_features\":[\"Watchlist3_0\"],\"resolved_features\":[\"Watchlist3_0\"],\"outputted_features\":[\"Watchlist3_0\"]},\"scheduler_timeout\":{\"length\":5000,\"unit\":\"MILLISECONDS\"},\"vendor_timeouts\":{\"COMPLY_WLVAL\":{\"length\":5000,\"unit\":\"MILLISECONDS\"}}},\"http_status\":200,\"reason_codes\":[\"R186\"],\"watchlist3_0_settings\":{\"searchId\":********,\"dobAndName\":false,\"dobTolerance\":true,\"dobMatchLogic\":\"exactDob\",\"fuzzinessTolerance\":0.5,\"screeningCategory\":[\"adverse-media\"],\"limit\":10,\"country\":[],\"monitoring\":false,\"sourceNameSet\":[\"Adverse Media Financial Crime\",\"Adverse Media General\",\"Adverse Media Fraud\"]}}]"),
      transactionDate = None
    )
    res shouldBe MonitorExecParameters(Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"), Some("abu"), Some("bakhar"), Some(false), Some(true), Some("exactDob"), Some(0.5), None, Some("watchlistpremier"), Some(********), Some(744731), Some(2), Some(true), None, None, None, None, Some(Set("adverse-media")), Some(10), Some(Set.empty), Some(Set("Adverse Media Financial Crime", "Adverse Media General", "Adverse Media Fraud")), Some(true), None, None, Some("us"), None, None, None, None, None, None, Some("<EMAIL>"), None, None, None, None, None)
  }


  test("should return expected output for non emailauthscore based apis") {
    val res = MonitorExecParameters.apply1(
      transactionIdBase = Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"),
      apiName = Some("/api/3.0/events"),
      accountIdBase = Some(744731),
      environmentIdBase = Some(2),
      parameters = Some("{\"country\":\"us\",\"ipaddress\":null,\"firstname\":\"abu\",\"addresses\":null,\"mobilenumber\":null,\"impersonatorapikey\":null,\"submissiondate\":null,\"city\":null,\"payments\":null,\"userid\":null,\"nationalid\":null,\"deviceSessionId\":null,\"physicaladdress2\":null,\"surname\":\"bakhar\",\"accountcreationdate\":null,\"orderamount\":null,\"watchlistfilters\":{\"categories\":[\"adverseMedia\"],\"matchingCriteria\":{\"nameDob\":false,\"nameFuzzinessTolerance\":0.5,\"dobFuzzinessTolerance\":true},\"limit\":100,\"countries\":[\"gb\"]},\"nocache\":0,\"prevordercount\":0,\"details\":false,\"state\":null,\"runid\":null,\"isConsenting\":null,\"customeruserid\":\"\",\"vendors\":null,\"email\":\"<EMAIL>\",\"zip\":null,\"debug\":false,\"documentuuid\":null,\"orderchannel\":null,\"datascience\":false,\"lastorderdate\":null,\"modules\":[\"watchlistpremier\"],\"physicaladdress\":null,\"driverlicensestate\":null,\"forcerefresh\":true,\"companyname\":null,\"dob\":\"\",\"geocode\":null,\"socurekey\":\"3f4e62a0-6424-4117-a593-baae2aefd43c\",\"fullname\":null,\"device\":null,\"driverlicense\":null,\"entityName\":\"Dolce & Gabbana\",\"entityType\":\"organisation\"}"),
      debug = Some("[{\"rule_codes\":[{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100007\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100007\"},{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100135\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100135\"},{\"rulecode\":\"scoreComponents.GLOBAL.300003\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"0.0\",\"ruleCodeShort\":\"GLOBAL.300003\"},{\"rulecode\":\"scoreComponents.GLOBAL.100143\",\"score\":\"-0.5\",\"confidence\":\"0.05\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"GLOBAL.100143\"}],\"raw_score\":4.5,\"version_applied\":\"3\",\"instance_id\":\"i-08d515f999dc35904\",\"caches_used\":[],\"categorical_values\":[],\"query_plan\":{\"vendors\":[\"COMPLY_WLVAL\"],\"id_plus_models\":{\"correlation_models\":{},\"risk_models\":{}},\"id_plus_features\":{\"requested_features\":[\"Watchlist3_0\"],\"resolved_features\":[\"Watchlist3_0\"],\"outputted_features\":[\"Watchlist3_0\"]},\"scheduler_timeout\":{\"length\":5000,\"unit\":\"MILLISECONDS\"},\"vendor_timeouts\":{\"COMPLY_WLVAL\":{\"length\":5000,\"unit\":\"MILLISECONDS\"}}},\"http_status\":200,\"reason_codes\":[\"R186\"],\"watchlist3_0_settings\":{\"searchId\":********,\"dobAndName\":false,\"dobTolerance\":true,\"dobMatchLogic\":\"exactDob\",\"fuzzinessTolerance\":0.5,\"screeningCategory\":[\"adverse-media\"],\"limit\":10,\"country\":[],\"monitoring\":false,\"sourceNameSet\":[\"Adverse Media Financial Crime\",\"Adverse Media General\",\"Adverse Media Fraud\"]}}]"),
      transactionDate = None
    )
    val default = MonitorExecParameters.apply(
      transactionId = Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"),
      accountId = Some(744731),
      environmentId = Some(2),
      transactionDate = None)
    val expectedParams = default.copy(message = Some("It is not an ID+(EmailAuthScore) transaction"))
    res shouldBe expectedParams
  }

  test("should handle non watchlist transaction") {
    val res = MonitorExecParameters.apply1(
      transactionIdBase = Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"),
      apiName = Some(Constants.emailAuthScoreV3ApiName),
      accountIdBase = Some(744731),
      environmentIdBase = Some(2),
      parameters = Some("{\"country\":\"us\",\"ipaddress\":null,\"firstname\":\"abu\",\"addresses\":null,\"mobilenumber\":null,\"impersonatorapikey\":null,\"submissiondate\":null,\"city\":null,\"payments\":null,\"userid\":null,\"nationalid\":null,\"deviceSessionId\":null,\"physicaladdress2\":null,\"surname\":\"bakhar\",\"accountcreationdate\":null,\"orderamount\":null,\"watchlistfilters\":{\"categories\":[\"adverseMedia\"],\"matchingCriteria\":{\"nameDob\":false,\"nameFuzzinessTolerance\":0.5,\"dobFuzzinessTolerance\":true},\"limit\":100,\"countries\":[\"gb\"]},\"nocache\":0,\"prevordercount\":0,\"details\":false,\"state\":null,\"runid\":null,\"isConsenting\":null,\"customeruserid\":\"\",\"vendors\":null,\"email\":\"<EMAIL>\",\"zip\":null,\"debug\":false,\"documentuuid\":null,\"orderchannel\":null,\"datascience\":false,\"lastorderdate\":null,\"modules\":[\"auth\"],\"physicaladdress\":null,\"driverlicensestate\":null,\"forcerefresh\":true,\"companyname\":null,\"dob\":\"\",\"geocode\":null,\"socurekey\":\"3f4e62a0-6424-4117-a593-baae2aefd43c\",\"fullname\":null,\"device\":null,\"driverlicense\":null}"),
      debug = Some("[{\"rule_codes\":[{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100007\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100007\"},{\"rulecode\":\"scoreComponents.GLOBAL.300003\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"0.0\",\"ruleCodeShort\":\"GLOBAL.300003\"},{\"rulecode\":\"scoreComponents.GLOBAL.100143\",\"score\":\"-0.5\",\"confidence\":\"0.05\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"GLOBAL.100143\"}],\"raw_score\":4.5,\"version_applied\":\"3\",\"correlation_score_info\":{\"name_address\":null,\"name_email\":null,\"name_phone\":null},\"instance_id\":\"i-0556c22fa3fc7ea22\",\"caches_used\":[],\"categorical_values\":[],\"query_plan\":{\"vendors\":[\"COMPLY_WLVAL\"],\"id_plus_models\":{\"correlation_models\":{},\"risk_models\":{}},\"id_plus_features\":{\"requested_features\":[\"Watchlist3_0\"],\"resolved_features\":[\"Watchlist3_0\"],\"outputted_features\":[\"Watchlist3_0\"]},\"scheduler_timeout\":{\"length\":5000,\"unit\":\"MILLISECONDS\"},\"vendor_timeouts\":{\"COMPLY_WLVAL\":{\"length\":5000,\"unit\":\"MILLISECONDS\"}}},\"http_status\":200,\"reason_codes\":[\"R186\"],\"watchlist3_0_settings\":{\"searchId\":********,\"dobAndName\":false,\"dobTolerance\":true,\"dobMatchLogic\":\"exactDob\",\"fuzzinessTolerance\":0.5,\"screeningCategory\":[\"adverse-media\",\"warning\",\"pep\",\"fitness-probity\",\"sanctions\"],\"limit\":10,\"country\":[],\"monitoring\":false}}]"),
      transactionDate = None
    )
    res shouldBe MonitorExecParameters(Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"), None, None, None, None, None, None, None, None, None, accountId = Some(744731), environmentId = Some(2), None, None, Some("It is not a watchlist transaction"), None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None)
  }

  test("should handle non watchlist transaction with encrypted emailauthscore api") {
    val res = MonitorExecParameters.apply1(
      transactionIdBase = Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"),
      apiName = Some(Constants.encryptedEmailAuthScoreV3ApiName),
      accountIdBase = Some(744731),
      environmentIdBase = Some(2),
      parameters = Some("{\"country\":\"us\",\"ipaddress\":null,\"firstname\":\"abu\",\"addresses\":null,\"mobilenumber\":null,\"impersonatorapikey\":null,\"submissiondate\":null,\"city\":null,\"payments\":null,\"userid\":null,\"nationalid\":null,\"deviceSessionId\":null,\"physicaladdress2\":null,\"surname\":\"bakhar\",\"accountcreationdate\":null,\"orderamount\":null,\"watchlistfilters\":{\"categories\":[\"adverseMedia\"],\"matchingCriteria\":{\"nameDob\":false,\"nameFuzzinessTolerance\":0.5,\"dobFuzzinessTolerance\":true},\"limit\":100,\"countries\":[\"gb\"]},\"nocache\":0,\"prevordercount\":0,\"details\":false,\"state\":null,\"runid\":null,\"isConsenting\":null,\"customeruserid\":\"\",\"vendors\":null,\"email\":\"<EMAIL>\",\"zip\":null,\"debug\":false,\"documentuuid\":null,\"orderchannel\":null,\"datascience\":false,\"lastorderdate\":null,\"modules\":[\"auth\"],\"physicaladdress\":null,\"driverlicensestate\":null,\"forcerefresh\":true,\"companyname\":null,\"dob\":\"\",\"geocode\":null,\"socurekey\":\"3f4e62a0-6424-4117-a593-baae2aefd43c\",\"fullname\":null,\"device\":null,\"driverlicense\":null}"),
      debug = Some("[{\"rule_codes\":[{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100007\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100007\"},{\"rulecode\":\"scoreComponents.GLOBAL.300003\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"0.0\",\"ruleCodeShort\":\"GLOBAL.300003\"},{\"rulecode\":\"scoreComponents.GLOBAL.100143\",\"score\":\"-0.5\",\"confidence\":\"0.05\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"GLOBAL.100143\"}],\"raw_score\":4.5,\"version_applied\":\"3\",\"correlation_score_info\":{\"name_address\":null,\"name_email\":null,\"name_phone\":null},\"instance_id\":\"i-0556c22fa3fc7ea22\",\"caches_used\":[],\"categorical_values\":[],\"query_plan\":{\"vendors\":[\"COMPLY_WLVAL\"],\"id_plus_models\":{\"correlation_models\":{},\"risk_models\":{}},\"id_plus_features\":{\"requested_features\":[\"Watchlist3_0\"],\"resolved_features\":[\"Watchlist3_0\"],\"outputted_features\":[\"Watchlist3_0\"]},\"scheduler_timeout\":{\"length\":5000,\"unit\":\"MILLISECONDS\"},\"vendor_timeouts\":{\"COMPLY_WLVAL\":{\"length\":5000,\"unit\":\"MILLISECONDS\"}}},\"http_status\":200,\"reason_codes\":[\"R186\"],\"watchlist3_0_settings\":{\"searchId\":********,\"dobAndName\":false,\"dobTolerance\":true,\"dobMatchLogic\":\"exactDob\",\"fuzzinessTolerance\":0.5,\"screeningCategory\":[\"adverse-media\",\"warning\",\"pep\",\"fitness-probity\",\"sanctions\"],\"limit\":10,\"country\":[],\"monitoring\":false}}]"),
      transactionDate = None
    )
    res shouldBe MonitorExecParameters(Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"), None, None, None, None, None, None, None, None, None, accountId = Some(744731), environmentId = Some(2), None, None, Some("It is not a watchlist transaction"), None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None)
  }

  test("should handle error out trx while fetching params") {
    val res = MonitorExecParameters.apply1(
      transactionIdBase = Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"),
      apiName = Some(Constants.emailAuthScoreV3ApiName),
      accountIdBase = Some(744731),
      environmentIdBase = Some(2),
      parameters = Some("{\"country\":\"us\",\"ipaddress\":null,\"firstname\":\"abu\",\"addresses\":null,\"mobilenumber\":null,\"impersonatorapikey\":null,\"submissiondate\":null,\"city\":null,\"payments\":null,\"userid\":null,\"nationalid\":null,\"deviceSessionId\":null,\"physicaladdress2\":null,\"surname\":\"bakhar\",\"accountcreationdate\":null,\"orderamount\":null,\"watchlistfilters\":{\"categories\":[\"adverseMedia\"],\"matchingCriteria\":{\"nameDob\":false,\"nameFuzzinessTolerance\":0.5,\"dobFuzzinessTolerance\":true},\"limit\":100,\"countries\":[\"gb\"]},\"nocache\":0,\"prevordercount\":0,\"details\":false,\"state\":null,\"runid\":null,\"isConsenting\":null,\"customeruserid\":\"\",\"vendors\":null,\"email\":\"<EMAIL>\",\"zip\":null,\"debug\":false,\"documentuuid\":null,\"orderchannel\":null,\"datascience\":false,\"lastorderdate\":null,\"modules\":[\"watchlistpremier\"],\"physicaladdress\":null,\"driverlicensestate\":null,\"forcerefresh\":true,\"companyname\":null,\"dob\":\"\",\"geocode\":null,\"socurekey\":\"3f4e62a0-6424-4117-a593-baae2aefd43c\",\"fullname\":null,\"device\":null,\"driverlicense\":null}"),
      debug = Some("[{\"rule_codes\":[{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100007\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100007\"},{\"rulecode\":\"scoreComponents.GLOBAL.300003\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"0.0\",\"ruleCodeShort\":\"GLOBAL.300003\"},{\"rulecode\":\"scoreComponents.GLOBAL.100143\",\"score\":\"-0.5\",\"confidence\":\"0.05\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"GLOBAL.100143\"}],\"raw_score\":4.5,\"version_applied\":\"3\",\"correlation_score_info\":{\"name_address\":null,\"name_email\":null,\"name_phone\":null},\"instance_id\":\"i-0556c22fa3fc7ea22\",\"caches_used\":[],\"categorical_values\":[],\"query_plan\":{\"vendors\":[\"COMPLY_WLVAL\"],\"id_plus_models\":{\"correlation_models\":{},\"risk_models\":{}},\"id_plus_features\":{\"requested_features\":[\"Watchlist3_0\"],\"resolved_features\":[\"Watchlist3_0\"],\"outputted_features\":[\"Watchlist3_0\"]},\"scheduler_timeout\":{\"length\":5000,\"unit\":\"MILLISECONDS\"},\"vendor_timeouts\":{\"COMPLY_WLVAL\":{\"length\":5000,\"unit\":\"MILLISECONDS\"}}},\"http_status\":500,\"reason_codes\":[\"R186\"],\"watchlist3_0_settings\":{\"searchId\":********,\"dobAndName\":false,\"dobTolerance\":true,\"dobMatchLogic\":\"exactDob\",\"fuzzinessTolerance\":0.5,\"screeningCategory\":[\"adverse-media\",\"warning\",\"pep\",\"fitness-probity\",\"sanctions\"],\"limit\":10,\"country\":[],\"monitoring\":false}}]"),
      transactionDate = None
    )
    res shouldBe MonitorExecParameters(Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"), None, None, None, None, None, None, None, None, None, accountId = Some(744731), environmentId = Some(2), None, None, Some("It is an error out watchlist transaction"), None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None)
  }

  test("should handle error out trx while fetching params with encrypted emailauthscore api") {
    val res = MonitorExecParameters.apply1(
      transactionIdBase = Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"),
      apiName = Some(Constants.encryptedEmailAuthScoreV3ApiName),
      accountIdBase = Some(744731),
      environmentIdBase = Some(2),
      parameters = Some("{\"country\":\"us\",\"ipaddress\":null,\"firstname\":\"abu\",\"addresses\":null,\"mobilenumber\":null,\"impersonatorapikey\":null,\"submissiondate\":null,\"city\":null,\"payments\":null,\"userid\":null,\"nationalid\":null,\"deviceSessionId\":null,\"physicaladdress2\":null,\"surname\":\"bakhar\",\"accountcreationdate\":null,\"orderamount\":null,\"watchlistfilters\":{\"categories\":[\"adverseMedia\"],\"matchingCriteria\":{\"nameDob\":false,\"nameFuzzinessTolerance\":0.5,\"dobFuzzinessTolerance\":true},\"limit\":100,\"countries\":[\"gb\"]},\"nocache\":0,\"prevordercount\":0,\"details\":false,\"state\":null,\"runid\":null,\"isConsenting\":null,\"customeruserid\":\"\",\"vendors\":null,\"email\":\"<EMAIL>\",\"zip\":null,\"debug\":false,\"documentuuid\":null,\"orderchannel\":null,\"datascience\":false,\"lastorderdate\":null,\"modules\":[\"watchlistpremier\"],\"physicaladdress\":null,\"driverlicensestate\":null,\"forcerefresh\":true,\"companyname\":null,\"dob\":\"\",\"geocode\":null,\"socurekey\":\"3f4e62a0-6424-4117-a593-baae2aefd43c\",\"fullname\":null,\"device\":null,\"driverlicense\":null,\"entityName\":\"Dolce & Gabbana\",\"entityType\":\"organisation\"}"),
      debug = Some("[{\"rule_codes\":[{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100007\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100007\"},{\"rulecode\":\"scoreComponents.GLOBAL.300003\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"0.0\",\"ruleCodeShort\":\"GLOBAL.300003\"},{\"rulecode\":\"scoreComponents.GLOBAL.100143\",\"score\":\"-0.5\",\"confidence\":\"0.05\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"GLOBAL.100143\"}],\"raw_score\":4.5,\"version_applied\":\"3\",\"correlation_score_info\":{\"name_address\":null,\"name_email\":null,\"name_phone\":null},\"instance_id\":\"i-0556c22fa3fc7ea22\",\"caches_used\":[],\"categorical_values\":[],\"query_plan\":{\"vendors\":[\"COMPLY_WLVAL\"],\"id_plus_models\":{\"correlation_models\":{},\"risk_models\":{}},\"id_plus_features\":{\"requested_features\":[\"Watchlist3_0\"],\"resolved_features\":[\"Watchlist3_0\"],\"outputted_features\":[\"Watchlist3_0\"]},\"scheduler_timeout\":{\"length\":5000,\"unit\":\"MILLISECONDS\"},\"vendor_timeouts\":{\"COMPLY_WLVAL\":{\"length\":5000,\"unit\":\"MILLISECONDS\"}}},\"http_status\":500,\"reason_codes\":[\"R186\"],\"watchlist3_0_settings\":{\"searchId\":********,\"dobAndName\":false,\"dobTolerance\":true,\"dobMatchLogic\":\"exactDob\",\"fuzzinessTolerance\":0.5,\"screeningCategory\":[\"adverse-media\",\"warning\",\"pep\",\"fitness-probity\",\"sanctions\"],\"limit\":10,\"country\":[],\"monitoring\":false}}]"),
      transactionDate = None
    )
    res shouldBe MonitorExecParameters(Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"), None, None, None, None, None, None, None, None, None, accountId = Some(744731), environmentId = Some(2), None, None, Some("It is an error out watchlist transaction"), None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None)
  }

  test("should return with userId") {
    val res = MonitorExecParameters.apply1(
      transactionIdBase = Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"),
      apiName = Some(Constants.emailAuthScoreV3ApiName),
      accountIdBase = Some(744731),
      environmentIdBase = Some(2),
      parameters = Some("{\"country\":\"us\",\"ipaddress\":null,\"firstname\":\"abu\",\"addresses\":null,\"mobilenumber\":null,\"impersonatorapikey\":null,\"submissiondate\":null,\"city\":null,\"payments\":null,\"userid\":null,\"nationalid\":null,\"deviceSessionId\":null,\"physicaladdress2\":null,\"surname\":\"bakhar\",\"accountcreationdate\":null,\"orderamount\":null,\"watchlistfilters\":{\"categories\":[\"adverseMedia\"],\"matchingCriteria\":{\"nameDob\":false,\"nameFuzzinessTolerance\":0.5,\"dobFuzzinessTolerance\":true},\"limit\":100,\"countries\":[\"gb\"]},\"nocache\":0,\"prevordercount\":0,\"details\":false,\"state\":null,\"runid\":null,\"isConsenting\":null,\"customeruserid\":\"customer\",\"vendors\":null,\"email\":\"<EMAIL>\",\"zip\":null,\"debug\":false,\"documentuuid\":null,\"orderchannel\":null,\"datascience\":false,\"lastorderdate\":null,\"modules\":[\"watchlistpremier\"],\"physicaladdress\":null,\"driverlicensestate\":null,\"forcerefresh\":true,\"companyname\":null,\"dob\":\"\",\"geocode\":null,\"socurekey\":\"3f4e62a0-6424-4117-a593-baae2aefd43c\",\"fullname\":null,\"device\":null,\"driverlicense\":null,\"entityName\":null,\"entityType\":null}"),
      debug = Some("[{\"rule_codes\":[{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100007\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100007\"},{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100135\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100135\"},{\"rulecode\":\"scoreComponents.GLOBAL.300003\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"0.0\",\"ruleCodeShort\":\"GLOBAL.300003\"},{\"rulecode\":\"scoreComponents.GLOBAL.100143\",\"score\":\"-0.5\",\"confidence\":\"0.05\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"GLOBAL.100143\"}],\"raw_score\":4.5,\"version_applied\":\"3\",\"instance_id\":\"i-08d515f999dc35904\",\"caches_used\":[],\"categorical_values\":[],\"query_plan\":{\"vendors\":[\"COMPLY_WLVAL\"],\"id_plus_models\":{\"correlation_models\":{},\"risk_models\":{}},\"id_plus_features\":{\"requested_features\":[\"Watchlist3_0\"],\"resolved_features\":[\"Watchlist3_0\"],\"outputted_features\":[\"Watchlist3_0\"]},\"scheduler_timeout\":{\"length\":5000,\"unit\":\"MILLISECONDS\"},\"vendor_timeouts\":{\"COMPLY_WLVAL\":{\"length\":5000,\"unit\":\"MILLISECONDS\"}}},\"http_status\":200,\"reason_codes\":[\"R186\"],\"watchlist3_0_settings\":{\"searchId\":********,\"dobAndName\":false,\"dobTolerance\":true,\"dobMatchLogic\":\"exactDob\",\"fuzzinessTolerance\":0.5,\"screeningCategory\":[\"adverse-media\"],\"limit\":10,\"country\":[],\"monitoring\":false,\"sourceNameSet\":[\"Adverse Media Financial Crime\",\"Adverse Media General\",\"Adverse Media Fraud\"]}}]"),
      transactionDate = None
    )
    res shouldBe MonitorExecParameters(Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"), Some("abu"), Some("bakhar"), Some(false), Some(true), Some("exactDob"), Some(0.5), None, Some("watchlistpremier"), Some(********), Some(744731), Some(2), Some(true), None, None, None, Some("customer"), Some(Set("adverse-media")), Some(10), Some(Set.empty), Some(Set("Adverse Media Financial Crime", "Adverse Media General", "Adverse Media Fraud")), Some(true), None, None, Some("us"), None, None, None, None, None, None, Some("<EMAIL>"), None, None, None, None, None)
  }

  test("should return with userId with encrypted emailauthscore api") {
    val res = MonitorExecParameters.apply1(
      transactionIdBase = Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"),
      apiName = Some(Constants.encryptedEmailAuthScoreV3ApiName),
      accountIdBase = Some(744731),
      environmentIdBase = Some(2),
      parameters = Some("{\"country\":\"us\",\"ipaddress\":null,\"firstname\":\"abu\",\"addresses\":null,\"mobilenumber\":null,\"impersonatorapikey\":null,\"submissiondate\":null,\"city\":null,\"payments\":null,\"userid\":null,\"nationalid\":null,\"deviceSessionId\":null,\"physicaladdress2\":null,\"surname\":\"bakhar\",\"accountcreationdate\":null,\"orderamount\":null,\"watchlistfilters\":{\"categories\":[\"adverseMedia\"],\"matchingCriteria\":{\"nameDob\":false,\"nameFuzzinessTolerance\":0.5,\"dobFuzzinessTolerance\":true},\"limit\":100,\"countries\":[\"gb\"]},\"nocache\":0,\"prevordercount\":0,\"details\":false,\"state\":null,\"runid\":null,\"isConsenting\":null,\"customeruserid\":\"customer\",\"vendors\":null,\"email\":\"<EMAIL>\",\"zip\":null,\"debug\":false,\"documentuuid\":null,\"orderchannel\":null,\"datascience\":false,\"lastorderdate\":null,\"modules\":[\"watchlistpremier\"],\"physicaladdress\":null,\"driverlicensestate\":null,\"forcerefresh\":true,\"companyname\":null,\"dob\":\"\",\"geocode\":null,\"socurekey\":\"3f4e62a0-6424-4117-a593-baae2aefd43c\",\"fullname\":null,\"device\":null,\"driverlicense\":null,\"entityName\":null,\"entityType\":null}"),
      debug = Some("[{\"rule_codes\":[{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100007\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100007\"},{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100135\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100135\"},{\"rulecode\":\"scoreComponents.GLOBAL.300003\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"0.0\",\"ruleCodeShort\":\"GLOBAL.300003\"},{\"rulecode\":\"scoreComponents.GLOBAL.100143\",\"score\":\"-0.5\",\"confidence\":\"0.05\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"GLOBAL.100143\"}],\"raw_score\":4.5,\"version_applied\":\"3\",\"instance_id\":\"i-08d515f999dc35904\",\"caches_used\":[],\"categorical_values\":[],\"query_plan\":{\"vendors\":[\"COMPLY_WLVAL\"],\"id_plus_models\":{\"correlation_models\":{},\"risk_models\":{}},\"id_plus_features\":{\"requested_features\":[\"Watchlist3_0\"],\"resolved_features\":[\"Watchlist3_0\"],\"outputted_features\":[\"Watchlist3_0\"]},\"scheduler_timeout\":{\"length\":5000,\"unit\":\"MILLISECONDS\"},\"vendor_timeouts\":{\"COMPLY_WLVAL\":{\"length\":5000,\"unit\":\"MILLISECONDS\"}}},\"http_status\":200,\"reason_codes\":[\"R186\"],\"watchlist3_0_settings\":{\"searchId\":********,\"dobAndName\":false,\"dobTolerance\":true,\"dobMatchLogic\":\"exactDob\",\"fuzzinessTolerance\":0.5,\"screeningCategory\":[\"adverse-media\"],\"limit\":10,\"country\":[],\"monitoring\":false,\"sourceNameSet\":[\"Adverse Media Financial Crime\",\"Adverse Media General\",\"Adverse Media Fraud\"]}}]"),
      transactionDate = None
    )
    res shouldBe MonitorExecParameters(Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"), Some("abu"), Some("bakhar"), Some(false), Some(true), Some("exactDob"), Some(0.5), None, Some("watchlistpremier"), Some(********), Some(744731), Some(2), Some(true), None, None, None, Some("customer"), Some(Set("adverse-media")), Some(10), Some(Set.empty), Some(Set("Adverse Media Financial Crime", "Adverse Media General", "Adverse Media Fraud")), Some(true), None, None, Some("us"), None, None, None, None, None, None, Some("<EMAIL>"), None, None, None, None, None)
  }

  test("should resolve firstName, lastName and fullName - when only fullname is provided"){
    val res = MonitorExecParameters.apply1(
      transactionIdBase = Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"),
      apiName = Some(Constants.emailAuthScoreV3ApiName),
      accountIdBase = Some(744731),
      environmentIdBase = Some(2),
      parameters = Some("{\"country\":\"us\",\"ipaddress\":null,\"firstname\":null,\"addresses\":null,\"mobilenumber\":null,\"impersonatorapikey\":null,\"submissiondate\":null,\"city\":null,\"payments\":null,\"userid\":null,\"nationalid\":null,\"deviceSessionId\":null,\"physicaladdress2\":null,\"surname\":null,\"accountcreationdate\":null,\"orderamount\":null,\"watchlistfilters\":{\"categories\":[\"adverseMedia\"],\"matchingCriteria\":{\"nameDob\":false,\"nameFuzzinessTolerance\":0.5,\"dobFuzzinessTolerance\":true},\"limit\":100,\"countries\":[\"gb\"]},\"nocache\":0,\"prevordercount\":0,\"details\":false,\"state\":null,\"runid\":null,\"isConsenting\":null,\"customeruserid\":\"\",\"vendors\":null,\"email\":\"<EMAIL>\",\"zip\":null,\"debug\":false,\"documentuuid\":null,\"orderchannel\":null,\"datascience\":false,\"lastorderdate\":null,\"modules\":[\"watchlistpremier\"],\"physicaladdress\":null,\"driverlicensestate\":null,\"forcerefresh\":true,\"companyname\":null,\"dob\":\"\",\"geocode\":null,\"socurekey\":\"3f4e62a0-6424-4117-a593-baae2aefd43c\",\"fullname\":\"abu bakhar\",\"device\":null,\"driverlicense\":null,\"entityName\":null,\"entityType\":null}"),
      debug = Some("[{\"rule_codes\":[{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100007\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100007\"},{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100135\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100135\"},{\"rulecode\":\"scoreComponents.GLOBAL.300003\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"0.0\",\"ruleCodeShort\":\"GLOBAL.300003\"},{\"rulecode\":\"scoreComponents.GLOBAL.100143\",\"score\":\"-0.5\",\"confidence\":\"0.05\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"GLOBAL.100143\"}],\"raw_score\":4.5,\"version_applied\":\"3\",\"instance_id\":\"i-08d515f999dc35904\",\"caches_used\":[],\"categorical_values\":[],\"query_plan\":{\"vendors\":[\"COMPLY_WLVAL\"],\"id_plus_models\":{\"correlation_models\":{},\"risk_models\":{}},\"id_plus_features\":{\"requested_features\":[\"Watchlist3_0\"],\"resolved_features\":[\"Watchlist3_0\"],\"outputted_features\":[\"Watchlist3_0\"]},\"scheduler_timeout\":{\"length\":5000,\"unit\":\"MILLISECONDS\"},\"vendor_timeouts\":{\"COMPLY_WLVAL\":{\"length\":5000,\"unit\":\"MILLISECONDS\"}}},\"http_status\":200,\"reason_codes\":[\"R186\"],\"watchlist3_0_settings\":{\"searchId\":********,\"dobAndName\":false,\"dobTolerance\":true,\"dobMatchLogic\":\"exactDob\",\"fuzzinessTolerance\":0.5,\"screeningCategory\":[\"adverse-media\"],\"limit\":10,\"country\":[],\"monitoring\":false,\"sourceNameSet\":[\"Adverse Media Financial Crime\",\"Adverse Media General\",\"Adverse Media Fraud\"]}}]"),
      transactionDate = None
    )
    res shouldBe MonitorExecParameters(Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"), Some("abu"), Some("bakhar"), Some(false), Some(true), Some("exactDob"), Some(0.5), None, Some("watchlistpremier"), Some(********), Some(744731), Some(2), Some(true), None, None, None, None, Some(Set("adverse-media")), Some(10), Some(Set.empty), Some(Set("Adverse Media Financial Crime", "Adverse Media General", "Adverse Media Fraud")), Some(true), Some("abu bakhar"), None, Some("us"), None, None, None, None, None, None, Some("<EMAIL>"), None, None, None, Some("abu bakhar"), None)
  }


  test("should resolve firstName, lastName and fullName - when only fullname is provided with single string"){
    val res = MonitorExecParameters.apply1(
      transactionIdBase = Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"),
      apiName = Some(Constants.emailAuthScoreV3ApiName),
      accountIdBase = Some(744731),
      environmentIdBase = Some(2),
      parameters = Some("{\"country\":\"us\",\"ipaddress\":null,\"firstname\":null,\"addresses\":null,\"mobilenumber\":null,\"impersonatorapikey\":null,\"submissiondate\":null,\"city\":null,\"payments\":null,\"userid\":null,\"nationalid\":null,\"deviceSessionId\":null,\"physicaladdress2\":null,\"surname\":null,\"accountcreationdate\":null,\"orderamount\":null,\"watchlistfilters\":{\"categories\":[\"adverseMedia\"],\"matchingCriteria\":{\"nameDob\":false,\"nameFuzzinessTolerance\":0.5,\"dobFuzzinessTolerance\":true},\"limit\":100,\"countries\":[\"gb\"]},\"nocache\":0,\"prevordercount\":0,\"details\":false,\"state\":null,\"runid\":null,\"isConsenting\":null,\"customeruserid\":\"\",\"vendors\":null,\"email\":\"<EMAIL>\",\"zip\":null,\"debug\":false,\"documentuuid\":null,\"orderchannel\":null,\"datascience\":false,\"lastorderdate\":null,\"modules\":[\"watchlistpremier\"],\"physicaladdress\":null,\"driverlicensestate\":null,\"forcerefresh\":true,\"companyname\":null,\"dob\":\"\",\"geocode\":null,\"socurekey\":\"3f4e62a0-6424-4117-a593-baae2aefd43c\",\"fullname\":\"abubakhar\",\"device\":null,\"driverlicense\":null,\"entityName\":null,\"entityType\":null}"),
      debug = Some("[{\"rule_codes\":[{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100007\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100007\"},{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100135\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100135\"},{\"rulecode\":\"scoreComponents.GLOBAL.300003\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"0.0\",\"ruleCodeShort\":\"GLOBAL.300003\"},{\"rulecode\":\"scoreComponents.GLOBAL.100143\",\"score\":\"-0.5\",\"confidence\":\"0.05\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"GLOBAL.100143\"}],\"raw_score\":4.5,\"version_applied\":\"3\",\"instance_id\":\"i-08d515f999dc35904\",\"caches_used\":[],\"categorical_values\":[],\"query_plan\":{\"vendors\":[\"COMPLY_WLVAL\"],\"id_plus_models\":{\"correlation_models\":{},\"risk_models\":{}},\"id_plus_features\":{\"requested_features\":[\"Watchlist3_0\"],\"resolved_features\":[\"Watchlist3_0\"],\"outputted_features\":[\"Watchlist3_0\"]},\"scheduler_timeout\":{\"length\":5000,\"unit\":\"MILLISECONDS\"},\"vendor_timeouts\":{\"COMPLY_WLVAL\":{\"length\":5000,\"unit\":\"MILLISECONDS\"}}},\"http_status\":200,\"reason_codes\":[\"R186\"],\"watchlist3_0_settings\":{\"searchId\":********,\"dobAndName\":false,\"dobTolerance\":true,\"dobMatchLogic\":\"exactDob\",\"fuzzinessTolerance\":0.5,\"screeningCategory\":[\"adverse-media\"],\"limit\":10,\"country\":[],\"monitoring\":false,\"sourceNameSet\":[\"Adverse Media Financial Crime\",\"Adverse Media General\",\"Adverse Media Fraud\"]}}]"),
      transactionDate = None
    )
    res shouldBe MonitorExecParameters(Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"), Some("abubakhar"), Some(""), Some(false), Some(true), Some("exactDob"), Some(0.5), None, Some("watchlistpremier"), Some(********), Some(744731), Some(2), Some(true), None, None, None, None, Some(Set("adverse-media")), Some(10), Some(Set.empty), Some(Set("Adverse Media Financial Crime", "Adverse Media General", "Adverse Media Fraud")), Some(true), Some("abubakhar"), None, Some("us"), None, None, None, None, None, None, Some("<EMAIL>"), None, None, None, Some("abubakhar"), None)
  }

  test("should resolve entity name when firstname, surname and fullname is empty"){
    val res = MonitorExecParameters.apply1(
      transactionIdBase = Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"),
      apiName = Some(Constants.emailAuthScoreV3ApiName),
      accountIdBase = Some(744731),
      environmentIdBase = Some(2),
      parameters = Some("{\"country\":\"us\",\"ipaddress\":null,\"firstname\":null,\"addresses\":null,\"mobilenumber\":null,\"impersonatorapikey\":null,\"submissiondate\":null,\"city\":null,\"payments\":null,\"userid\":null,\"nationalid\":null,\"deviceSessionId\":null,\"physicaladdress2\":null,\"surname\":null,\"accountcreationdate\":null,\"orderamount\":null,\"watchlistfilters\":{\"categories\":[\"adverseMedia\"],\"matchingCriteria\":{\"nameDob\":false,\"nameFuzzinessTolerance\":0.5,\"dobFuzzinessTolerance\":true},\"limit\":100,\"countries\":[\"gb\"]},\"nocache\":0,\"prevordercount\":0,\"details\":false,\"state\":null,\"runid\":null,\"isConsenting\":null,\"customeruserid\":\"\",\"vendors\":null,\"email\":\"<EMAIL>\",\"zip\":null,\"debug\":false,\"documentuuid\":null,\"orderchannel\":null,\"datascience\":false,\"lastorderdate\":null,\"modules\":[\"watchlistpremier\"],\"physicaladdress\":null,\"driverlicensestate\":null,\"forcerefresh\":true,\"companyname\":null,\"dob\":\"\",\"geocode\":null,\"socurekey\":\"3f4e62a0-6424-4117-a593-baae2aefd43c\",\"fullname\":null,\"device\":null,\"driverlicense\":null,\"entityname\":\"Dolce & Gabbana\",\"entitytype\":\"organisation\"}"),
      debug = Some("[{\"rule_codes\":[{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100007\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100007\"},{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100135\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100135\"},{\"rulecode\":\"scoreComponents.GLOBAL.300003\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"0.0\",\"ruleCodeShort\":\"GLOBAL.300003\"},{\"rulecode\":\"scoreComponents.GLOBAL.100143\",\"score\":\"-0.5\",\"confidence\":\"0.05\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"GLOBAL.100143\"}],\"raw_score\":4.5,\"version_applied\":\"3\",\"instance_id\":\"i-08d515f999dc35904\",\"caches_used\":[],\"categorical_values\":[],\"query_plan\":{\"vendors\":[\"COMPLY_WLVAL\"],\"id_plus_models\":{\"correlation_models\":{},\"risk_models\":{}},\"id_plus_features\":{\"requested_features\":[\"Watchlist3_0\"],\"resolved_features\":[\"Watchlist3_0\"],\"outputted_features\":[\"Watchlist3_0\"]},\"scheduler_timeout\":{\"length\":5000,\"unit\":\"MILLISECONDS\"},\"vendor_timeouts\":{\"COMPLY_WLVAL\":{\"length\":5000,\"unit\":\"MILLISECONDS\"}}},\"http_status\":200,\"reason_codes\":[\"R186\"],\"watchlist3_0_settings\":{\"searchId\":********,\"dobAndName\":false,\"dobTolerance\":true,\"dobMatchLogic\":\"exactDob\",\"fuzzinessTolerance\":0.5,\"screeningCategory\":[\"adverse-media\"],\"limit\":10,\"country\":[],\"monitoring\":false,\"sourceNameSet\":[\"Adverse Media Financial Crime\",\"Adverse Media General\",\"Adverse Media Fraud\"]}}]"),
      transactionDate = None
    )
    res shouldBe MonitorExecParameters(Some("xy9lZO1XdaYv8nJo14Z5gKw1UGb9aHSMVoM2"), Some("Dolce"), Some("Gabbana"), Some(false), Some(true), Some("exactDob"), Some(0.5), None, Some("watchlistpremier"), Some(********), Some(744731), Some(2), Some(true), None, None, None, None, Some(Set("adverse-media")), Some(10), Some(Set.empty), Some(Set("Adverse Media Financial Crime", "Adverse Media General", "Adverse Media Fraud")), Some(true), Some("Dolce & Gabbana"), Some("organisation"), Some("us"), None, None, None, None, None, None, Some("<EMAIL>"), None, None, None, None, None)
  }

}
