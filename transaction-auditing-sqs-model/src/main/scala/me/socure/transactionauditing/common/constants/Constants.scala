package me.socure.transactionauditing.common.constants

object Constants {

  val emailAuthScoreApiName: String = "EmailAuthScore"
  val emailAuthScoreV3ApiName: String = s"/api/3.0/$emailAuthScoreApiName"
  val encryptedEmailAuthScoreApiName: String = "EncryptedEmailAuthScore"
  val encryptedEmailAuthScoreV3ApiName: String = s"/api/3.0/$encryptedEmailAuthScoreApiName"
  val entityNameParam = "entityname"
  val entityTypeParam = "entitytype"
  val invokedVendorsParam = "invokedVendors"
  val fullNameParam = "fullname"
  val singleSpace: String  = " "
  val emptyString: String = ""
  val fullNameOrEntityNameSplitLength: Int = 2
  val reqCountryParam = "country"
  val reqNationalIdParam = "nationalId"
  val reqPhoneParam = "mobilenumber"
  val reqEmailParam = "email"
  val reqStateParam = "state"
  val reqCityParam = "city"
  val reqZipCodeParam = "zip"
  val parentTxnIdParam = "parentTxnId"
  val workflowParam = "workflow"
  val riskOSIdParam = "riskOSId"

}
