package me.socure.transactionauditing.common.model

import me.socure.common.options._
import org.joda.time.DateTime
import org.json4s.{DefaultFormats, JValue}
import org.slf4j.LoggerFactory

/**
 * <AUTHOR>
 */
case class TransactionInformation(
                                   transactionId: Option[String],
                                   apiName: Option[String],
                                   transactionDate: Option[String],
                                   accountId: Option[Long],
                                   environmentTypeId: Option[Long],
                                   docvTransactionToken: Option[String]
                                 )

object TransactionInformation {

  import me.socure.transactionauditing.sqs.model.JsonUtils._

  def cacheKey(trxInfo: String): String = {
    val trxId = ((parse(trxInfo) \ "transactionId").extractOpt[String]).getOrElse("unknown")
    s"ta_trx_info_$trxId"
  }

  private implicit val formats: DefaultFormats.type = DefaultFormats
  private val logger = LoggerFactory.getLogger(getClass)


  private val Empty = TransactionInformation(
    transactionId = None,
    apiName = None,
    transactionDate = None,
    accountId = None,
    environmentTypeId = None,
    docvTransactionToken = None
  )

  def apply(
             transactionIdBase: Option[String],
             apiName: Option[String],
             accountIdBase: Option[Long],
             environmentTypeIdBase: Option[Long],
             parameters: JValue,
             transactionDate: Option[DateTime]
           ): TransactionInformation = {
    val transactionId = transactionIdBase.clean()
    val accountId = accountIdBase.filter(_ > 0)
    val environmentTypeId = environmentTypeIdBase.filter(_ > 0)
    val cleanedApiName = apiName.clean().map(_.toLowerCase)
    val docvTransactionToken = (parameters \ "docvtransactiontoken").extractOpt[String]
    TransactionInformation(
      transactionId = transactionId,
      apiName = cleanedApiName,
      transactionDate = transactionDate.map(_.toString()),
      accountId = accountId,
      environmentTypeId = environmentTypeId,
      docvTransactionToken = docvTransactionToken
    )
  }

}