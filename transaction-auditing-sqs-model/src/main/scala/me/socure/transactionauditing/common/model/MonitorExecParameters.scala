package me.socure.transactionauditing.common.model

import me.socure.common.options._
import me.socure.transactionauditing.common.constants.Constants
import org.joda.time.DateTime
import org.json4s._
import org.slf4j.LoggerFactory
import me.socure.transactionauditing.common.constants.Constants.{emptyString, entityNameParam, entityTypeParam, fullNameOrEntityNameSplitLength, fullNameParam, invokedVendorsParam, parentTxnIdParam, reqCityParam, reqCountryParam, reqEmailParam, reqNationalIdParam, reqPhoneParam, reqStateParam, reqZipCodeParam, riskOSIdParam, singleSpace, workflowParam}

import scala.util.control.NonFatal

case class MonitorExecParameters(
                                  transactionId: Option[String],
                                  firstName: Option[String],
                                  lastName: Option[String],
                                  dobAndName: Option[Boolean],
                                  dobTolerance: Option[Boolean],
                                  dobMatchLogic: Option[String],
                                  fuzzinessTolerance: Option[Double],
                                  dob: Option[String],
                                  tier: Option[String],
                                  searchId: Option[Long],
                                  accountId: Option[Long],
                                  environmentId: Option[Long],
                                  isOnlyCategoryAdverseMedia: Option[Boolean],
                                  transactionDate: Option[DateTime],
                                  message: Option[String],
                                  userId: Option[String],
                                  customerUserId: Option[String],
                                  screeningCategory: Option[Set[String]],
                                  limit: Option[Int],
                                  country: Option[Set[String]],
                                  sourceNameSet: Option[Set[String]],
                                  hasWatchlistFilters: Option[Boolean],
                                  entityName: Option[String],
                                  entityType: Option[String],
                                  reqCountry: Option[String],
                                  watchlistTransactionType: Option[String],
                                  parentTxnId: Option[String],
                                  workflow: Option[String],
                                  riskOSId: Option[String],
                                  nationalId: Option[String],
                                  phone: Option[String],
                                  email: Option[String],
                                  state: Option[String],
                                  city: Option[String],
                                  zip: Option[String],
                                  fullName: Option[String],
                                  invokedVendors: Option[Set[String]]
                                )
object MonitorExecParameters {

  import me.socure.transactionauditing.sqs.model.JsonUtils._

  def cacheKey(trxId: String): String = s"ta_wm_$trxId"

  private implicit val formats: DefaultFormats.type = DefaultFormats
  private val logger = LoggerFactory.getLogger(getClass)

  def apply(
             transactionId: Option[String],
             accountId: Option[Long] = None,
             environmentId: Option[Long] = None,
             transactionDate: Option[DateTime] = None
           ): MonitorExecParameters = {
    MonitorExecParameters(
      transactionId = transactionId,
      firstName = None,
      lastName = None,
      dobAndName = None,
      dobTolerance = None,
      dobMatchLogic = None,
      fuzzinessTolerance = None,
      dob = None,
      tier = None,
      searchId = None,
      accountId = accountId,
      environmentId = environmentId,
      isOnlyCategoryAdverseMedia = None,
      transactionDate = transactionDate,
      message = None,
      userId = None,
      customerUserId = None,
      screeningCategory = None,
      limit = None,
      country = None,
      sourceNameSet = None,
      hasWatchlistFilters = None,
      entityName = None,
      entityType = None,
      reqCountry = None,
      watchlistTransactionType = None,
      nationalId = None,
      phone = None,
      email = None,
      state = None,
      city = None,
      zip = None,
      parentTxnId = None,
      workflow = None,
      riskOSId = None,
      fullName = None,
      invokedVendors = None
    )
  }

  case class Watchlist(
                        searchId: Option[Int],
                        dobAndName: Option[Boolean],
                        dobTolerance: Option[Boolean],
                        dobMatchLogic: Option[String],
                        fuzzinessTolerance: Option[Double],
                        screeningCategory: Option[Set[String]],
                        limit: Option[Int],
                        country: Option[Set[String]],
                        sourceNameSet: Option[Set[String]],
                        watchlistTransactionType: Option[String] = None,
                        invokedVendors: Option[Set[String]] = None
                      )

  def apply(
             transactionIdBase: Option[String],
             apiName: Option[String],
             accountIdBase: Option[Long],
             environmentIdBase: Option[Long],
             parameters: JValue,
             debug: JValue,
             transactionDate: Option[DateTime]
           ): MonitorExecParameters = {
    val transactionId = transactionIdBase.clean()
    val accountId = accountIdBase.filter(_ > 0)
    val environmentId = environmentIdBase.filter(_ > 0)
    val default = MonitorExecParameters(
      transactionId = transactionId,
      accountId = accountId,
      environmentId = environmentId,
      transactionDate = transactionDate
    )
    try {
      val cleanedApiName = apiName.clean().map(_.toLowerCase)
      val isEmailAuthScore = cleanedApiName.contains(Constants.emailAuthScoreV3ApiName.toLowerCase) || cleanedApiName.contains(Constants.encryptedEmailAuthScoreV3ApiName.toLowerCase)
      if (isEmailAuthScore) {
        val watchlistModuleOpt = (parameters \ "modules").extractOpt[Set[String]].getOrElse(Set.empty).find(_.contains("watchlist")).clean()
        watchlistModuleOpt match {
          case Some(watchlistModule) =>
            val httpStatus = (debug \ "http_status").extractOpt[Int]
            if (httpStatus.contains(200)) {
              val (firstName, lastName) = resolveFirstAndLastName(parameters)
              val dob = (parameters \ "dob").extractOpt[String].clean()
              val watchlistSettings = (debug \ "watchlist3_0_settings").extractOpt[Watchlist]
              val dobAndName = watchlistSettings.flatMap(_.dobAndName)
              val dobTolerance = watchlistSettings.flatMap(_.dobTolerance)
              val dobMatchLogic = watchlistSettings.flatMap(_.dobMatchLogic)
              val fuzzinessTolerance = watchlistSettings.flatMap(_.fuzzinessTolerance)
              val searchId = watchlistSettings.flatMap(_.searchId).map(_.toLong)
              val isOnlyAdverseMediaCat = watchlistSettings.map { wlSettings =>
                val screeningCategories = wlSettings.screeningCategory.getOrElse(Set.empty)
                screeningCategories.size == 1 && screeningCategories.contains("adverse-media")
              }
              val screeningCategory = watchlistSettings.flatMap(_.screeningCategory)
              val limit = watchlistSettings.flatMap(_.limit)
              val country = watchlistSettings.flatMap(_.country)
              val sourceNameSet = watchlistSettings.flatMap(_.sourceNameSet)
              val watchlistTransactionType = watchlistSettings.flatMap(_.watchlistTransactionType)
              val invokedVendors = watchlistSettings.flatMap(_.invokedVendors)
              val userId = (parameters \ "userid").extractOpt[String].clean()
              val customerUserId = (parameters \ "customeruserid").extractOpt[String].clean()
              val watchlistFilters = (parameters \ "watchlistfilters").extract[JValue]
              val hasWatchlistFilters = watchlistFilters match {
                case JNull | JNothing => false
                case _ => true
              }
              val entityName = (parameters \ entityNameParam).extractOpt[String].clean()
              val fullName = (parameters \ fullNameParam).extractOpt[String].clean()
              val name = fullName match {
                case Some(fname) =>   Some(fname)
                case None =>  entityName
              }
              val entityType = (parameters \ entityTypeParam).extractOpt[String].clean()
              val reqCountry = (parameters \ reqCountryParam).extractOpt[String].clean()
              val parentTxnId = (parameters \ parentTxnIdParam).extractOpt[String].clean()
              val workflow = (parameters \ workflowParam).extractOpt[String].clean()
              val riskOSId = (parameters \ riskOSIdParam).extractOpt[String].clean()
              val nationalId = (parameters \ reqNationalIdParam).extractOpt[String].clean()
              val phone = (parameters \ reqPhoneParam).extractOpt[String].clean()
              val email = (parameters \ reqEmailParam).extractOpt[String].clean()
              val state = (parameters \ reqStateParam).extractOpt[String].clean()
              val city = (parameters \ reqCityParam).extractOpt[String].clean()
              val zip = (parameters \ reqZipCodeParam).extractOpt[String].clean()
              MonitorExecParameters(
                transactionId = transactionId,
                firstName = firstName,
                lastName = lastName,
                dobAndName = dobAndName,
                dobTolerance = dobTolerance,
                dobMatchLogic = dobMatchLogic,
                fuzzinessTolerance = fuzzinessTolerance,
                dob = dob,
                tier = Some(watchlistModule),
                searchId = searchId,
                accountId = accountId,
                environmentId = environmentId,
                isOnlyCategoryAdverseMedia = isOnlyAdverseMediaCat,
                transactionDate = transactionDate,
                message = None,
                userId = userId,
                customerUserId = customerUserId,
                screeningCategory = screeningCategory,
                limit = limit,
                country = country,
                sourceNameSet = sourceNameSet,
                hasWatchlistFilters = Some(hasWatchlistFilters),
                entityName = name,
                entityType = entityType,
                reqCountry = reqCountry,
                watchlistTransactionType = watchlistTransactionType,
                nationalId = nationalId,
                phone = phone,
                email = email,
                state = state,
                city = city,
                zip = zip,
                parentTxnId = parentTxnId,
                workflow = workflow,
                riskOSId = riskOSId,
                fullName = fullName,
                invokedVendors = invokedVendors
              )
            } else default.copy(message = Some("It is an error out watchlist transaction"))
          case None => default.copy(message = Some("It is not a watchlist transaction"))
        }
      } else default.copy(message = Some("It is not an ID+(EmailAuthScore) transaction"))
    } catch {
      case NonFatal(ex) =>
        logger.warn("Exception occurred while fetching parameters required for EMS processing: ", ex)
        default.copy(message = Some("Exception occurred while fetching parameters"))
    }
  }

  private def resolveFirstAndLastName(parameters: JValue): (Option[String], Option[String]) = {
    val firstName = (parameters \ "firstname").extractOpt[String].clean()
    val lastName = (parameters \ "surname").extractOpt[String].clean()
    val fullName = (parameters \ "fullname").extractOpt[String].clean()

    if(firstName.isDefined && lastName.isDefined){
      (firstName, lastName)
    }else if(fullName.isDefined){
      resolveFullNameOrEntityName(fullNameParam, parameters)
    }
    else {
      resolveFullNameOrEntityName(entityNameParam, parameters)
    }
  }

  private def resolveFullNameOrEntityName(name: String, parameters: JValue): (Option[String], Option[String]) = {
    val nameOpt = (parameters \ name).extractOpt[String].clean()
    if (nameOpt.isEmpty) {
      (None, None)
    } else {
      val nameSplit = nameOpt.get.split(singleSpace)
      if (nameSplit.length >= fullNameOrEntityNameSplitLength) {
        (nameSplit.headOption, nameSplit.lastOption)
      } else {
        (nameSplit.headOption, Some(emptyString))
      }
    }
  }

  def apply1(
             transactionIdBase: Option[String],
             apiName: Option[String],
             accountIdBase: Option[Long],
             environmentIdBase: Option[Long],
             parameters: Option[String],
             debug: Option[String],
             transactionDate: Option[DateTime]
           ): MonitorExecParameters = {
    apply(
      transactionIdBase = transactionIdBase,
      apiName = apiName,
      accountIdBase = accountIdBase,
      environmentIdBase = environmentIdBase,
      parameters = parse(parameters.getOrElse("{}")),
      debug = parseSingle(debug.getOrElse("{}")),
      transactionDate = transactionDate
    )
  }
}
