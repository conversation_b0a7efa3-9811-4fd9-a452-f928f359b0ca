package me.socure.transactionauditing.sqs.model

import org.json4s.jackson.{JsonMethods, Serialization}
import org.json4s._

import scala.util.Try

object JsonUtils {
  val emptyObj: JObject = JObject()

  def parse(s: String): JValue = Try(JsonMethods.parse(s)).getOrElse(emptyObj)

  def parseSingle(s: String): JValue = {
    parse(s) match {
      case JArray(values) => values.headOption.getOrElse(emptyObj)
      case jv => jv
    }
  }

  def write[A <: AnyRef](s: A)(implicit formats: Formats): String = Serialization.write[A](s)
}
