package me.socure.transactionauditing.sqs.model

import org.json4s._

case class SqsApiTransactionParsed(
  original: SqsApiTransaction,
  parameters: JValue,
  response: JValue,
  debug: JValue,
  details: JValue
)

object SqsApiTransactionParsed {

  import JsonUtils._

  def apply(original: SqsApiTransaction): SqsApiTransactionParsed = {
    SqsApiTransactionParsed(
      original = original,
      parameters = parse(original.parameters),
      response = parse(original.response),
      debug = parseSingle(original.debug),
      details = parseSingle(original.details)
    )
  }
}
