package me.socure.transactionauditing.sqs

import org.json4s.jackson.JsonMethods
import org.json4s.{DefaultFormats, Formats}
import org.slf4j.LoggerFactory

import scala.util.control.NonFatal

package object model {

  private val logger = LoggerFactory.getLogger(getClass)
  private implicit val jsonFormats: Formats = DefaultFormats

  private def extractModules(trxId: String, parameters: String): Set[String] = {
    try {
      (JsonMethods.parse(parameters) \ "modules").extract[Set[String]]
    } catch {
      case NonFatal(ex) =>
        logger.error(s"Error while extracting tags for $trxId", ex)
        Set.empty[String]
    }
  }

  implicit class RichSqsApiTransaction(val trx: SqsApiTransaction) extends AnyVal {
    def tags(isInternal: Option[Boolean] = None): Seq[String] = {
      val modules = extractModules(trx.transactionId, trx.parameters).map(module => s"module:$module")
      Seq(
        s"account_id:${trx.accountId}",
        s"environment_id:${trx.environmentType}",
        s"status:${!trx.error}",
        s"trx_status:${!trx.error}",
        s"api_name:${trx.apiName}",
        s"api_type:${trx.api}",
        s"run_id:${trx.runId.getOrElse("null")}",
        s"is_internal:${isInternal.map(_.toString).getOrElse("unknown")}"
      ) ++ modules
    }
  }

}
