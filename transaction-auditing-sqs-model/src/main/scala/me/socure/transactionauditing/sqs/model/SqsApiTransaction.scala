package me.socure.transactionauditing.sqs.model

case class SqsApiTransaction(
                              id: Option[Long],
                              transactionId: String,
                              transactionDate: Long,
                              processingTime: Long,
                              api: String,
                              parameters: String,
                              response: String,
                              error: Boolean,
                              errorMsg: Option[String],
                              accountIpAddress: String,
                              originOfInvocation: Option[String],
                              geocode: Option[String],
                              apiKey: String,
                              accountId: Long,
                              apiName: String,
                              UUID: String,
                              cachesUsed: Option[String],
                              debug: String,
                              details: String,
                              authScore: Double,
                              confidence: Double,
                              reasonCodes: String,
                              hasRiskCode: Boolean,
                              customerUserId: Option[String],
                              runId: Option[String],
                              requestURI: String,
                              kyc: Boolean,
                              ofac: Boolean,
                              environmentType: Long,
                              internalWorkLogs: Option[String] = None,
                              httpStatus: Option[Int] = None,
                              actualEndpoint: Option[String] = None,
                              subEnvironment: Option[String] = None,
                              preprocessedPII: Option[String] = None,
                              internalAccount: Boolean = false,
                              modules: Option[Set[String]] = None,
                              parentTxnId: Option[String] = None
                            ) extends SqsMessage
