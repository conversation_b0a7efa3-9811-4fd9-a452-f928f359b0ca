package me.socure.transactionauditing.sqs.model

import me.socure.common.random.Random

object SqsAPITransactionValueGenerator {
  def aAPITransaction(
                       id: Option[Long] = None,
                       transactionId: String = Random.alphaNumeric(36),
                       transactionDate: Long = System.currentTimeMillis(),
                       processingTime: Long = 500,
                       api: String = "REST",
                       parameters: String = Random.alphaNumeric(250),
                       response: String = Random.alphaNumeric(1000),
                       error: Boolean = false,
                       errorMsg: Option[String] = None,
                       accountIpAddress: String = "localhost",
                       originOfInvocation: Option[String] = None,
                       geocode: Option[String] = None,
                       apiKey: String = "test-api-key",
                       accountId: Long = 0,
                       apiName: String = "/api/3.0/EmailAuthScoreTest",
                       UUID: String = Random.alphaNumeric(39),
                       cachesUsed: Option[String] = None,
                       debug: String = Random.alphaNumeric(2000),
                       details: String = Random.alphabetic(500),
                       authScore: Double = 0,
                       confidence: Double = 0,
                       reasonCodes: String = Random.alphaNumeric(250),
                       hasRiskCode: Boolean = false,
                       customerUserId: Option[String] = None,
                       runId: Option[String] = None,
                       requestURI: String = "/api/3.0/EmailAuthScoreTest",
                       kyc: Boolean = false,
                       ofac: Boolean = false,
                       environmentType: Long = 2,
                       internalWorkLogs: Option[String] = None,
                       parentTxnId: Option[String] = Some(Random.alphaNumeric(36))
                     ): SqsApiTransaction = {
    SqsApiTransaction(
      id = id,
      transactionId = transactionId,
      transactionDate = transactionDate,
      processingTime = processingTime,
      api = api,
      parameters = parameters,
      response = response,
      error = error,
      errorMsg = errorMsg,
      accountIpAddress = accountIpAddress,
      originOfInvocation = originOfInvocation,
      geocode = geocode,
      apiKey = apiKey,
      accountId = accountId,
      apiName = apiName,
      UUID = UUID,
      cachesUsed = cachesUsed,
      debug = debug,
      details = details,
      authScore = authScore,
      confidence = confidence,
      reasonCodes = reasonCodes,
      hasRiskCode = hasRiskCode,
      customerUserId = customerUserId,
      runId = runId,
      requestURI = requestURI,
      kyc = kyc,
      ofac = ofac,
      environmentType = environmentType,
      internalWorkLogs = internalWorkLogs
    )
  }
}
