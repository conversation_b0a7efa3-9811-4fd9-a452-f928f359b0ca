package me.socure.transactionauditing.sqs.model

import me.socure.common.sqs.v2.MessageId

case class BatchSqsApiTransaction(
                                   messageId: MessageId,
                                   transaction: SqsApiTransaction,
                                   unencryptedTransaction: SqsApiTransaction,
                                   enqueuedToBYOKFailureQueue: <PERSON><PERSON><PERSON>,
                                   dequeuedFromBYOKFailureQueue: <PERSON><PERSON>an
                                 )
