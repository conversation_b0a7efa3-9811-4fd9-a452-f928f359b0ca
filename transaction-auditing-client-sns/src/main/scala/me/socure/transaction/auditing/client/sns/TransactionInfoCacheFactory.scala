package me.socure.transaction.auditing.client.sns

import me.socure.common.data.core.consumer.{DataConsumer, _}
import me.socure.common.data.memcached.MemcachedDataConsumer
import me.socure.common.metrics.JavaMetricsFactory
import me.socure.transactionauditing.common.model.TransactionInformation
import me.socure.transactionauditing.sqs.model.SqsApiTransactionParsed
import net.spy.memcached.MemcachedClient
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.DefaultFormats
import org.slf4j.LoggerFactory

import scala.concurrent.duration.FiniteDuration

/**
 * <AUTHOR> <PERSON>n
 */
object TransactionInfoCacheFactory {

  import me.socure.transactionauditing.sqs.model.JsonUtils._

  private implicit val formats: DefaultFormats.type = DefaultFormats

  private def toTransactionInfo(apiTransaction: SqsApiTransactionParsed): String = {
    val trxInfo = TransactionInformation(
      transactionIdBase = Option(apiTransaction.original.transactionId),
      apiName = Option(apiTransaction.original.apiName),
      accountIdBase = Option(apiTransaction.original.accountId),
      environmentTypeIdBase = Option(apiTransaction.original.environmentType),
      parameters = apiTransaction.parameters,
      transactionDate = Option(apiTransaction.original.transactionDate).map(millis => new DateTime(millis, DateTimeZone.UTC))
    )
    write(trxInfo)
  }

  private def extractTags(trxInfo: String): Set[String] = {
    val trxInfoJ = parse(trxInfo)
    val accountId = (trxInfoJ \ "accountId").extractOpt[String]
    val environmentId = (trxInfoJ \ "environmentId").extractOpt[String]
    Set(
      accountId.map(accId => s"account_id:$accId"),
      environmentId.map(envId => s"environment_id:$envId"),
      Some("consumer_name:trx_audit_trx_info")
    ).flatten
  }

  def create(
              client: MemcachedClient,
              expiration: FiniteDuration
            ): DataConsumer[SqsApiTransactionParsed] = {
    val memcachedDataConsumer = MemcachedDataConsumer[String](
      client = client,
      keyExtractor = trxInfo => TransactionInformation.cacheKey(trxInfo),
      expiration = expiration
    )

    memcachedDataConsumer
      .withMetrics(
        metrics = JavaMetricsFactory.get("data.consumer"),
        onSuccessTags = extractTags,
        onFailureTags = (params, _) => extractTags(params)
      )
      .withLogging(
        LoggerFactory.getLogger("data.consumer.trx_audit_trx_info")
      )
      .fromInput[SqsApiTransactionParsed](toTransactionInfo)
      .ignoreFailures() //caching should not affect the application
  }

}
