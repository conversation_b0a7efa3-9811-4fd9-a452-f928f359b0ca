package me.socure.transaction.auditing.client.sns


import com.typesafe.config.Config
import me.socure.common.options._
import me.socure.common.sns.fallback.SnsWithFallback
import me.socure.common.transaction.id.TrxId
import me.socure.transactionauditing.sqs.model.SqsApiTransaction
import software.amazon.awssdk.services.s3.model.PutObjectResponse
import software.amazon.awssdk.services.sns.model.PublishResponse

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class TransactionAuditingClientSns(
                                    client: SnsWithFallback,
                                    converter: Converter,
                                    accountInternalChecker: AccountInternalChecker
                                  )(implicit ec: ExecutionContext) {

  def push(
            trx: SqsApiTransaction
          ): Future[Either[PublishResponse, PutObjectResponse]] = {
    implicit val trxId: TrxId = TrxId(Option(trx.transactionId).clean().getOrElse("UNKNOWN"))
    val tags = trx.tags(accountInternalChecker.isInternalSafe(trx.accountId))
    val resultFuture = Try(converter.toSns(trx = trx)) match {
      case Success(req) => client.publish(req)
      case Failure(exception) =>
        Future.failed(new ConversionException(exception))
    }
    resultFuture
  }
}

object TransactionAuditingClientSns {
  /*
    {
      sns.topic = ""
      sns.regions = [ //ordered
        "us-east-1"
        "us-west-2"
        "us-east-2"
      ]
      s3.bucket.large.files {
        name = ""
        region = ""
      }
      s3.bucket.fallback {
        name = ""
        region = ""
      }
      retry {
        initial.backoff = "2 seconds" //default
        max.backoff = "32 seconds" //default
        multiplier = 2 //default
        max.attempts = 3 //default
      }
    }
  */

  def apply(config: Config)(implicit ec: ExecutionContext): TransactionAuditingClientSns = {
    new TransactionAuditingClientSns(
      client = SnsWithFallback(config),
      converter = Converter,
      accountInternalChecker = DefaultAccountInternalChecker
    )
  }
}
