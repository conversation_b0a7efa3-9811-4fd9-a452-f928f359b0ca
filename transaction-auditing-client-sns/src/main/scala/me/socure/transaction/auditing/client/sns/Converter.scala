package me.socure.transaction.auditing.client.sns

import java.nio.charset.StandardCharsets
import java.util.{Base64, UUID}

import me.socure.common.options._
import me.socure.transactionauditing.sqs.model.SqsApiTransaction
import org.json4s.jackson.Serialization.write
import org.json4s.{DefaultFormats, Formats}
import software.amazon.awssdk.services.sns.model.{MessageAttributeValue, PublishRequest}

import scala.collection.JavaConverters._

trait Converter {

  private implicit val jsonFormats: Formats = DefaultFormats

  private def marshall(trx: SqsApiTransaction): String = {
    val jsonBytes = write(trx).getBytes(StandardCharsets.UTF_8)
    val compressedBytes = Gzip.compress(jsonBytes)
    Base64.getEncoder.encodeToString(compressedBytes)
  }

  def toSns(trx: SqsApiTransaction): PublishRequest = {
    val trxId = Option(trx.transactionId).clean()
    val attrs = (strAttrs(trx) ++ strArrayAttrs(trx)).asJava
    PublishRequest
      .builder()
      .subject(trxId.getOrElse(s"unknown-${UUID.randomUUID().toString}"))
      .message(marshall(trx))
      .messageAttributes(attrs)
      .build()
  }

  private def strAttrs(trx: SqsApiTransaction): Map[String, MessageAttributeValue] = {
    Seq(
      Option(trx.transactionId).clean().map("transactionId" -> _),
      Option(trx.apiName).clean().map("apiName" -> _),
      Option(trx.accountId).map("accountId" -> _.toString),
      Option(trx.environmentType).map("environmentType" -> _.toString),
      Option(trx.error).map("error" -> _.toString),
      Option(trx.internalAccount).map("internalAccount" -> _.toString)
      ).flatten.toMap.mapValues {
      value =>
        MessageAttributeValue
            .builder()
            .dataType("String")
            .stringValue(value)
            .build()
    }
  }

  private def strArrayAttrs(trx: SqsApiTransaction): Map[String, MessageAttributeValue] = {
    val modulesOpt = trx.modules.map(write(_)).filter(_.nonEmpty)
    modulesOpt match {
      case Some(modules) => Seq(
        Option(modules).clean().map("modules" -> _)
        ).flatten.toMap.mapValues {
        value =>
          MessageAttributeValue
              .builder()
              .dataType("String.Array")
              .stringValue(value)
              .build()
      }
      case _ => Map.empty[String, MessageAttributeValue]
    }
  }

}

object Converter extends Converter
