package me.socure.transaction.auditing.client.sns

import me.socure.common.data.core.consumer._
import me.socure.common.metrics.JavaMetricsFactory
import me.socure.transactionauditing.sqs.model.{SqsApiTransaction, SqsApiTransactionParsed}
import org.slf4j.LoggerFactory

import scala.concurrent.Future

class PipelinesBasedClient(pipeline: DataConsumer[SqsApiTransactionParsed]) extends TransactionAuditingClient {
  override def audit(trx: SqsApiTransaction): Future[Unit] = pipeline.apply(SqsApiTransactionParsed(trx))
}

object PipelinesBasedClient {

  private def enrich(accountInternalChecker: AccountInternalChecker, aspect: String, consumer: DataConsumer[SqsApiTransactionParsed]): DataConsumer[SqsApiTransactionParsed] = {
    consumer
      .withMetrics(
        JavaMetricsFactory.get(s"trx.client.pipeline"),
        onSuccessTags = trx => trx.original.tags(accountInternalChecker.isInternalSafe(trx.original.accountId)).toSet[String] + s"pipeline:$aspect",
        onFailureTags = (trx, _) => trx.original.tags(accountInternalChecker.isInternalSafe(trx.original.accountId)).toSet[String] + s"pipeline:$aspect"
      )
      .withLogging(
        LoggerFactory.getLogger(s"trx.client.pipeline.$aspect"),
        inputToStr = trx => trx.original.transactionId
      )
  }

  def apply(
             accountInternalChecker: AccountInternalChecker,
             first: (String, DataConsumer[SqsApiTransactionParsed]),
             rest: (String, DataConsumer[SqsApiTransactionParsed])*
           ): TransactionAuditingClient =
    new PipelinesBasedClient(
      DataConsumer.par(
        first._2,
        rest.map(pair => pair._2): _*
      )
    )
}
