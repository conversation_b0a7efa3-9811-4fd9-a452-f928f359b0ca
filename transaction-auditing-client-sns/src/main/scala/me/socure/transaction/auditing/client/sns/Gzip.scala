package me.socure.transaction.auditing.client.sns

import java.io.ByteArrayOutputStream
import java.util.zip.GZIPOutputStream

trait Gzip {
  def compress(data: Array[Byte]): Array[Byte] = {
    val bos = new ByteArrayOutputStream(data.length)
    val gzipOut = new GZIPOutputStream(bos)
    gzipOut.write(data)
    gzipOut.close()
    val res = bos.toByteArray
    res(9) = -1 //Operating System flag. To be compatible with the existing implementation
    res
  }
}

object Gzip extends Gzip
