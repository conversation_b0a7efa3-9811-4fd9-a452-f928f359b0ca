package me.socure.transaction.auditing.client.sns

import me.socure.common.data.core.consumer._
import me.socure.common.sns.fallback.SnsWithFallback
import me.socure.transactionauditing.sqs.model.SqsApiTransactionParsed
import net.spy.memcached.MemcachedClient

import scala.concurrent.ExecutionContext
import scala.concurrent.duration._

object TransactionAuditingClientFactory {

  def create(
              snsClient: SnsWithFallback,
              memcachedClient: MemcachedClient,
              watchlistExpiration: FiniteDuration,
              converter: Converter = Converter,
              accountInternalChecker: AccountInternalChecker = DefaultAccountInternalChecker
            )(implicit ec: ExecutionContext): TransactionAuditingClient = {

    val trxSnsClient = new TransactionAuditingClientSns(
      client = snsClient,
      converter = converter,
      accountInternalChecker
    )

    val main = DataConsumer[SqsApiTransactionParsed] { trx =>
      trxSnsClient.push(trx.original).map(_ => ())
    }

    val watchlistInfo = WatchlistInfoConsumerFactory.create(
      client = memcachedClient,
      expiration = watchlistExpiration
    )

    val transactionInfo = TransactionInfoCacheFactory.create(
      client = memcachedClient,
      expiration = watchlistExpiration
    )

    PipelinesBasedClient(
      accountInternalChecker,
      ("main", main),
      ("watchlist_info", watchlistInfo),
      ("transaction_info", transactionInfo)
    )
  }
}
