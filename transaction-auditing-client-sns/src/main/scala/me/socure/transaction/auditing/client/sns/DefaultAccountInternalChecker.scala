package me.socure.transaction.auditing.client.sns

import java.util.concurrent.ConcurrentHashMap

object DefaultAccountInternalChecker extends AccountInternalChecker {

  private val internalAccounts = ConcurrentHashMap.newKeySet[Long]()

  // I am not proud of this, but this is the easiest way to do in ID+
  def addToInternal(accountId: Long): Unit = internalAccounts.add(accountId)

  override def isInternal(accountId: Long): Boolean =
    internalAccounts.contains(accountId)
}
