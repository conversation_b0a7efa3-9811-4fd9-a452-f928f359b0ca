package me.socure.transaction.auditing.client.sns

import me.socure.common.data.core.consumer._
import me.socure.common.data.memcached.MemcachedDataConsumer
import me.socure.common.metrics.JavaMetricsFactory
import me.socure.transactionauditing.sqs.model.SqsApiTransactionParsed
import me.socure.transactionauditing.sqs.model.summary.MonitorExecParameters
import net.spy.memcached.MemcachedClient
import org.joda.time.{DateTime, DateTimeZone}
import org.slf4j.LoggerFactory

import scala.concurrent.duration.FiniteDuration

object WatchlistInfoConsumerFactory {

  private def toWatchlistDetails(apiTransaction: SqsApiTransactionParsed): MonitorExecParameters = {
    MonitorExecParameters(
      transactionIdBase = Option(apiTransaction.original.transactionId),
      apiName = Option(apiTransaction.original.apiName),
      accountIdBase = Option(apiTransaction.original.accountId),
      environmentIdBase = Option(apiTransaction.original.environmentType),
      parameters = apiTransaction.parameters,
      debug = apiTransaction.debug,
      transactionDate = Option(apiTransaction.original.transactionDate).map(millis => new DateTime(millis, DateTimeZone.UTC))
    )
  }

  private def extractTags(params: MonitorExecParameters): Set[String] = {
    Set(
      params.accountId.map(accId => s"account_id:$accId"),
      params.environmentId.map(envId => s"environment_id:$envId"),
      Some("consumer_name:trx_audit_wl_info")
    ).flatten
  }

  def create(
            client: MemcachedClient,
            expiration: FiniteDuration
            ): DataConsumer[SqsApiTransactionParsed] = {
    val memcachedDataConsumer = MemcachedDataConsumer[MonitorExecParameters](
      client = client,
      keyExtractor = wl => MonitorExecParameters.cacheKey(wl.transactionId.getOrElse("unknown")),
      expiration = expiration
    )

    memcachedDataConsumer
      .withMetrics(
        metrics = JavaMetricsFactory.get("data.consumer"),
        onSuccessTags = extractTags,
        onFailureTags = (params, _) => extractTags(params)
      )
      .withLogging(
        LoggerFactory.getLogger("data.consumer.trx_audit_wl_info"),
        inputToStr = params => String.valueOf(params.copy(
          firstName = params.firstName.map(_ => "***"),
          lastName = params.lastName.map(_ => "***"),
          dob = params.dob.map(_ => "***")
        ))
      )
      .fromInput[SqsApiTransactionParsed](toWatchlistDetails)
      .ignoreFailures() //caching should not affect the application
  }
}
