package me.socure.transaction.auditing.client.sns

import java.io.ByteArrayInputStream
import java.util.Base64
import java.util.zip.GZIPInputStream

import me.socure.transactionauditing.sqs.model.{SqsAPITransactionValueGenerator, SqsApiTransaction}
import org.json4s.jackson.Serialization
import org.json4s.{DefaultFormats, Formats}
import org.scalatest.{FreeSpec, Matchers}
import software.amazon.awssdk.utils.IoUtils

class ConverterTest extends FreeSpec with Matchers {

  private implicit val jsonFormats: Formats = DefaultFormats

  "should convert properly with all props" in {
    val trx = SqsAPITransactionValueGenerator.aAPITransaction(
      transactionId = "trx-id-1",
      apiName = "api-name-1"
    )
    val result = Converter.toSns(trx)
    result
      .messageAttributes()
      .get("transactionId")
      .stringValue() shouldBe "trx-id-1"
    result
      .messageAttributes()
      .get("apiName")
      .stringValue() shouldBe "api-name-1"
    result.subject() shouldBe "trx-id-1"
    read(result.message()) shouldBe trx
  }

  "should handle missing props" in {
    val trx = SqsAPITransactionValueGenerator.aAPITransaction(
      transactionId = null,
      apiName = null
    )
    val result = Converter.toSns(trx)
    result.messageAttributes().containsKey("transactionId") shouldBe false
    result.messageAttributes().containsKey("apiName") shouldBe false
    result.subject().startsWith("unknown-") shouldBe true
    read(result.message()) shouldBe trx
  }

  private def read(s: String): SqsApiTransaction = {
    val decoded = Base64.getDecoder.decode(s)
    val uncompressed = IoUtils.toUtf8String(
      new GZIPInputStream(new ByteArrayInputStream(decoded))
    )
    Serialization.read[SqsApiTransaction](uncompressed)
  }
}
