package me.socure.transaction.auditing.client.sns

import me.socure.common.sns.fallback.SnsWithFallback
import me.socure.transactionauditing.sqs.model.SqsAPITransactionValueGenerator
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Milliseconds, Seconds, Span}
import org.scalatest.{FreeSpec, Matchers}
import software.amazon.awssdk.services.sns.model.{
  PublishRequest,
  PublishResponse
}

import scala.concurrent.{ExecutionContext, Future}

class TransactionAuditingClientSnsTest
    extends FreeSpec
    with Matchers
    with MockFactory
    with ScalaFutures {

  private implicit val ec: ExecutionContext = ExecutionContext.global
  private implicit val patienceConf: PatienceConfig = PatienceConfig(
    timeout = Span(3, Seconds),
    interval = Span(50, Milliseconds)
  )

  private val converter = mock[Converter]
  private class MockableSnsWithFallback
      extends SnsWithFallback(
        IndexedSeq.empty,
        IndexedSeq.empty,
        null,
        null,
        null,
        null,
        null,
        null
      )
  private val snsWithFallback = mock[MockableSnsWithFallback]
  private val accountInternalChecker = mock[AccountInternalChecker]
  private val client = new TransactionAuditingClientSns(
    snsWithFallback,
    converter,
    accountInternalChecker
  )

  "should work properly" in {
    val trx = SqsAPITransactionValueGenerator.aAPITransaction(accountId = 10)
    val result = Left(PublishResponse.builder().messageId("test").build())
    val publishReq = PublishRequest.builder().message("test").build()
    (converter.toSns _).expects(trx).returns(publishReq)
    (accountInternalChecker.isInternalSafe _).expects(10).returns(Some(true))
    (snsWithFallback.publish _)
      .expects(publishReq)
      .returns(Future.successful(result))
    val resultFuture = client.push(trx)
    whenReady(resultFuture) { actualResult =>
      actualResult shouldBe result
    }
  }
}
