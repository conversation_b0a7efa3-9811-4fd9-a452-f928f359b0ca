package me.socure.transaction.auditing.client.sns

import me.socure.common.docker.memcachedservice.MemcachedSupport
import me.socure.common.random.Random
import me.socure.transactionauditing.sqs.model.summary.MonitorExecParameters
import me.socure.transactionauditing.sqs.model.{SqsAPITransactionValueGenerator, SqsApiTransactionParsed}
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Milliseconds, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, FreeSpec, Matchers}

import scala.concurrent.ExecutionContext
import scala.concurrent.duration._

class WatchlistInfoConsumerFactoryTest extends FreeSpec with Matchers with ScalaFutures with MemcachedSupport with BeforeAndAfterAll {

  private implicit val ec: ExecutionContext = ExecutionContext.Implicits.global
  private implicit val patienceConf: PatienceConfig = PatienceConfig(
    timeout = Span(10, Seconds),
    interval = Span(50, Milliseconds)
  )

  private lazy val client = new MemcachedClient(AddrUtil.getAddresses(s"$memcachedHost:$memcachedPort"))
  private lazy val consumer = WatchlistInfoConsumerFactory.create(client, 10.minutes)

  "should persist to memcached as expected" in {
    val parameters = "{\"country\":\"us\",\"ipaddress\":null,\"firstname\":\"abu\",\"addresses\":null,\"mobilenumber\":null,\"impersonatorapikey\":null,\"submissiondate\":null,\"city\":null,\"payments\":null,\"userid\":null,\"nationalid\":null,\"deviceSessionId\":null,\"physicaladdress2\":null,\"surname\":\"bakhar\",\"accountcreationdate\":null,\"orderamount\":null,\"watchlistfilters\":{\"categories\":[\"adverseMedia\"],\"matchingCriteria\":{\"nameDob\":false,\"nameFuzzinessTolerance\":0.5,\"dobFuzzinessTolerance\":true},\"limit\":100,\"countries\":[\"gb\"]},\"nocache\":0,\"prevordercount\":0,\"details\":false,\"state\":null,\"runid\":null,\"isConsenting\":null,\"customeruserid\":\"\",\"vendors\":null,\"email\":\"<EMAIL>\",\"zip\":null,\"debug\":false,\"documentuuid\":null,\"orderchannel\":null,\"datascience\":false,\"lastorderdate\":null,\"modules\":[\"watchlistpremier\"],\"physicaladdress\":null,\"driverlicensestate\":null,\"forcerefresh\":true,\"companyname\":null,\"dob\":\"\",\"geocode\":null,\"socurekey\":\"3f4e62a0-6424-4117-a593-baae2aefd43c\",\"fullname\":null,\"device\":null,\"driverlicense\":null,\"entityName\":null,\"entityType\":null}"
    val debug = "[{\"rule_codes\":[{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100007\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100007\"},{\"rulecode\":\"scoreComponents.COMPLY_WLVAL.100135\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"COMPLY_WLVAL.100135\"},{\"rulecode\":\"scoreComponents.GLOBAL.300003\",\"score\":\"0.0\",\"confidence\":\"0.0\",\"originalScore\":\"0.0\",\"ruleCodeShort\":\"GLOBAL.300003\"},{\"rulecode\":\"scoreComponents.GLOBAL.100143\",\"score\":\"-0.5\",\"confidence\":\"0.05\",\"originalScore\":\"1.0\",\"ruleCodeShort\":\"GLOBAL.100143\"}],\"raw_score\":4.5,\"version_applied\":\"3\",\"instance_id\":\"i-08d515f999dc35904\",\"caches_used\":[],\"categorical_values\":[],\"query_plan\":{\"vendors\":[\"COMPLY_WLVAL\"],\"id_plus_models\":{\"correlation_models\":{},\"risk_models\":{}},\"id_plus_features\":{\"requested_features\":[\"Watchlist3_0\"],\"resolved_features\":[\"Watchlist3_0\"],\"outputted_features\":[\"Watchlist3_0\"]},\"scheduler_timeout\":{\"length\":5000,\"unit\":\"MILLISECONDS\"},\"vendor_timeouts\":{\"COMPLY_WLVAL\":{\"length\":5000,\"unit\":\"MILLISECONDS\"}}},\"http_status\":200,\"reason_codes\":[\"R186\"],\"watchlist3_0_settings\":{\"searchId\":********,\"dobAndName\":false,\"dobTolerance\":true, \"dobMatchLogic\":\"exactDob\",\"fuzzinessTolerance\":0.5,\"screeningCategory\":[\"adverse-media\"],\"limit\":10,\"country\":[],\"monitoring\":false,\"sourceNameSet\":[\"Adverse Media Financial Crime\",\"Adverse Media General\",\"Adverse Media Fraud\"]}}]"
    val transactionDate = DateTime.now(DateTimeZone.UTC)
    val transactionDateMillis = transactionDate.getMillis
    val transactionId = Random.uuids()
    val accountId = 10L + math.abs(Random.nextLong() - 10000)
    val environmentType = Random.shuffle(Set(1, 2, 3)).head
    val trx = SqsAPITransactionValueGenerator.aAPITransaction(
      transactionId = transactionId,
      accountId = accountId,
      environmentType = environmentType,
      transactionDate = transactionDateMillis,
      parameters = parameters,
      debug = debug,
      apiName = "/api/3.0/EmailAuthScore"
    )
    val expectedResult = MonitorExecParameters(
      transactionId = Some(transactionId),
      firstName = Some("abu"),
      lastName = Some("bakhar"),
      dobAndName = Some(false),
      dobTolerance = Some(true),
      dobMatchLogic = Some("exactDob"),
      fuzzinessTolerance = Some(0.5),
      dob = None,
      tier = Some("watchlistpremier"),
      searchId = Some(********),
      accountId = Some(accountId),
      environmentId = Some(environmentType),
      isOnlyCategoryAdverseMedia = Some(true),
      transactionDate = Some(transactionDate),
      message = None,
      userId = None,
      customerUserId = None,
      screeningCategory = Some(Set("adverse-media")),
      limit = Some(10),
      country = Some(Set.empty),
      sourceNameSet = Some(Set("Adverse Media Financial Crime", "Adverse Media General", "Adverse Media Fraud")),
      hasWatchlistFilters = Some(true),
      entityName = None,
      entityType = None,
      reqCountry = Some("us"),
      watchlistTransactionType = Some("Hybrid"),
      parentTxnId = None,
      workflow = None,
      None,
      None,
      None,
      None,
      None,
      None,
      None,
      None,
      None
    )
    val cacheKey = MonitorExecParameters.cacheKey(transactionId)
    whenReady(consumer.apply(SqsApiTransactionParsed(trx))) { _ =>
      val actualResult = client.get(cacheKey)
      actualResult shouldBe expectedResult
    }
  }

  override protected def beforeAll(): Unit = {
    super.beforeAll()
    startupMemcached()
  }

  override protected def afterAll(): Unit = {
    super.afterAll()
    cleanupMemcached()
  }
}
