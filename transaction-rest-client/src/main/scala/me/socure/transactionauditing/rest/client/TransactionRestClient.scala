package me.socure.transactionauditing.rest.client

import dispatch.{Http, Req, url}
import me.socure.common.data.core.provider._
import me.socure.common.metrics.models.MetricTags
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate
import me.socure.transactionauditing.common.domain._
import me.socure.transactionauditing.common.domain.v2.{TransactionSearchRequestV2, TransactionSearchResponseV2}
import me.socure.transactionauditing.common.json.TrxJsonFormats
import me.socure.transactionauditing.common.transaction.TransactionColumns.TransactionColumn
import me.socure.transactionauditing.common.util.SlickDomainConversion.toApiTransaction
import me.socure.transactionauditing.rest.client.util.TAThrottlingUtil
import me.socure.transactionauditing.sqs.model.summary.MonitorExecParameters
import me.socure.transactionauditstorage.mysqlschema.{ApiTransaction => SlickApiTransaction}
import org.apache.http.entity.ContentType
import org.asynchttpclient.Response
import org.json4s.jackson.Serialization
import org.json4s.native.JsonMethods._
import org.json4s.{Formats, _}
import org.slf4j.LoggerFactory

import java.lang.{Integer => JInteger}
import java.nio.charset.StandardCharsets
import java.util.{HashMap => JHashMap, Map => JMap}
import scala.collection.JavaConverters._
import scala.concurrent.duration._
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class TransactionRestClient(
                             http: Http,
                             auditServerEndpoint: String,
                             endpoint2: Option[String] = None,
                             dynamicControlCenterV2Evaluate: Option[DynamicControlCenterV2Evaluate] = None,
                             metricsEnabled: Boolean = true
                           ) {
  implicit def jsonFormats: Formats = TrxJsonFormats.value

  private val whitesourceCodePath: String = "whitesource"
  private val mainCodePath: String = "main"
  private val defaultBlockingTimeout = 60
  private val logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get(getClass)
  private val prefixedMetrics: Metrics = JavaMetricsFactory.get(MetricTags.httpMetricPrefix)
  private val serviceName = "transaction-auditing-service"

  lazy val IsAdminDashboard: Boolean = "admin-dashboard".equals(System.getenv("CONFIGURATION_NAME")) || "admin-dashboard".equals(System.getProperty("CONFIGURATION_NAME"))

  private def getEndpoint()(implicit ec: ExecutionContext): Future[TAThrottlingUtil.TransactionAuditingThrottlingEndpointResponse] = {
    TAThrottlingUtil.getEndpoint(auditServerEndpoint, endpoint2, dynamicControlCenterV2Evaluate).map {
      transactionAuditingThrottlingEndpointResponse => transactionAuditingThrottlingEndpointResponse
    }
  }
  
  private def processRequest(req: Req, apiName: String, codePath: String)(implicit ec: ExecutionContext): Future[Response] = {

    val tags = MetricTags(
      serviceName = Some(serviceName),
      apiName = Some(apiName),
      isInternal = Some(true),
      mSource = Some("client"),
      tags = Set(s"${MetricTags.codePath}:${codePath}")
    )

    if(IsAdminDashboard) {
      logger.debug("withMetricTags is used")
      http(req)
        .withMetricTags(
          metrics = prefixedMetrics,
          baseTags = tags,
          onSuccessTags = res => MetricTags(httpStatus = Some(res.getStatusCode))
        ).apply()
    } else {
      http(req)
        .withOptionalMetricTags(
          metrics = prefixedMetrics,
          baseTags = tags,
          onSuccessTags = res => MetricTags(httpStatus = Some(res.getStatusCode)),
          enableSuccessMetric = metricsEnabled,
          enableErrorMetric = metricsEnabled
        ).apply()
    }
  }

  def searchTransaction(tsr: TransactionSearchRequest)(implicit ec: ExecutionContext): Future[ApiTransactionResult] = {
    getEndpoint().flatMap(transactionAuditingThrottlingEndpointResponse => {
      val auditServerEndpoint: String = transactionAuditingThrottlingEndpointResponse.endpointUrl
      val codePath: String = if(transactionAuditingThrottlingEndpointResponse.isParallel) whitesourceCodePath else mainCodePath
      val apiName = "/transaction/search"
      val jsonBody = Serialization.write(tsr)
      val request = url(auditServerEndpoint + apiName)
        .POST
        .setBody(jsonBody)
        .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)

      processRequest(request, apiName, codePath).map { httpResponse =>
        ApiTransactionResult(httpResponse.getStatusCode, parseTransactionList(httpResponse.getResponseBody).map(toApiTransaction))
      }
    })
  }

  def searchTransactionDynamo(tsr: TransactionSearchRequestDynamo)(implicit ec: ExecutionContext): Future[TransactionSearchResponseData] = {
    getEndpoint().flatMap(transactionAuditingThrottlingEndpointResponse => {
      val auditServerEndpoint: String = transactionAuditingThrottlingEndpointResponse.endpointUrl
      val codePath: String = if(transactionAuditingThrottlingEndpointResponse.isParallel) whitesourceCodePath else mainCodePath
      val apiName = "/transaction/search/dynamo"
      val jsonBody = Serialization.write(tsr)
      val request = url(auditServerEndpoint + apiName)
        .POST
        .setBody(jsonBody)
        .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)
      processRequest(request, apiName, codePath).map { httpResponse =>
        parseTransactionSearchResponseDynamo(httpResponse.getResponseBody)
      }
    })
  }

  def searchTransactionV2(tsrV2: TransactionSearchRequestV2)(implicit ec: ExecutionContext): Future[TransactionSearchResponseV2] = {
    getEndpoint().flatMap(transactionAuditingThrottlingEndpointResponse => {
      val auditServerEndpoint: String = transactionAuditingThrottlingEndpointResponse.endpointUrl
      val codePath: String = if(transactionAuditingThrottlingEndpointResponse.isParallel) whitesourceCodePath else mainCodePath
      val apiName = "/v2/transaction/search"
      val jsonBody = Serialization.write(tsrV2)
      val request = url(auditServerEndpoint + apiName)
        .POST
        .setBody(jsonBody)
        .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)

      processRequest(request, apiName, codePath).map {
        httpResponse => parseTransactionSearchResponseV2(httpResponse.getResponseBody)
      } 
    })
  }

  def searchTransactionBlocking(tsr: TransactionSearchRequest)(implicit ec: ExecutionContext): ApiTransactionResult = {
    Await.result(searchTransaction(tsr), defaultBlockingTimeout.seconds)
  }

  def countTransaction(tsr: TransactionSearchRequest)(implicit ec: ExecutionContext): Future[Int] = {
    getEndpoint().flatMap(transactionAuditingThrottlingEndpointResponse => {
      val auditServerEndpoint: String = transactionAuditingThrottlingEndpointResponse.endpointUrl
      val codePath: String = if(transactionAuditingThrottlingEndpointResponse.isParallel) whitesourceCodePath else mainCodePath
      val apiName = "/transaction/count"

      val jsonBody = Serialization.write(tsr)
      val request = url(auditServerEndpoint + apiName)
        .POST
        .setBody(jsonBody)
        .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)

      processRequest(request, apiName, codePath).map { httpResponse =>
        httpResponse.getResponseBody.toInt
      }
    })
  }

  def countTransactionBlocking(tsr: TransactionSearchRequest)(implicit ec: ExecutionContext): Int = {
    Await.result(countTransaction(tsr), defaultBlockingTimeout.seconds)
  }


  def fetchTransactionCount(tsr: TransactionSearchRequest)(implicit ec: ExecutionContext): Future[Option[Int]] = {
    getEndpoint().flatMap(transactionAuditingThrottlingEndpointResponse => {
      val auditServerEndpoint: String = transactionAuditingThrottlingEndpointResponse.endpointUrl
      val codePath: String = if(transactionAuditingThrottlingEndpointResponse.isParallel) whitesourceCodePath else mainCodePath
      val apiName = "/transaction/count/fetch"

      val jsonBody = Serialization.write(tsr)
      val request = url(auditServerEndpoint + apiName)
        .POST
        .setBody(jsonBody)
        .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)

      processRequest(request, apiName, codePath).map { httpResponse =>
        val json = parse(httpResponse.getResponseBody)
        Try((json \ "data").extractOpt[Int]) match {
          case Success(count) => if(!count.isDefined || count.isEmpty) {
            logger.warn(s"unable to get transactions count from response ${httpResponse.getResponseBody}")
            count
          } else count
          case Failure(error) => {
            logger.warn(s"unable to get transactions count from response ${httpResponse.getResponseBody}, with error ${error.getMessage}")
            None
          }
        }
      }
    })
  }

  def fetchTransactionCountWithBlocking(tsr: TransactionSearchRequest)(implicit ec: ExecutionContext): Option[Int] = {
    Await.result(fetchTransactionCount(tsr), defaultBlockingTimeout.seconds)
  }

  def findTransaction(transactionId: String, columns: Set[TransactionColumn], maskPii: Boolean, skipProdValidation: Boolean = false)(implicit ec: ExecutionContext): Future[ApiTransactionResult] = {
    getEndpoint().flatMap(transactionAuditingThrottlingEndpointResponse => {
      val auditServerEndpoint: String = transactionAuditingThrottlingEndpointResponse.endpointUrl
      val codePath: String = if(transactionAuditingThrottlingEndpointResponse.isParallel) whitesourceCodePath else mainCodePath
      val apiName = "/transaction"
      val columnStr = columns.mkString(",")

      val queryParams: Iterable[(String, String)] = Iterable(
        "columns" -> columnStr,
        "maskPii" -> maskPii.toString
      )

      val req: Req = (url(auditServerEndpoint + apiName) / transactionId.trim <<? queryParams)
        .GET
        .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)

      processRequest(req, apiName, codePath).map {
        case httpResp if httpResp.getStatusCode == 200 =>
          Try(parseTransaction(httpResp.getResponseBody())) match {
            case Success(s) =>
              ApiTransactionResult(httpResp.getStatusCode, s.map(elem => toApiTransaction(elem, skipProdValidation)))
            case Failure(e) =>
              logger.error("Unable to parse the transaction", e)
              ApiTransactionResult(500, Seq.empty)
          }
        case e@_ => ApiTransactionResult(e.getStatusCode, Seq.empty)

      }
    })
  }

  def findTransactionWithInternalData(transactionId: String, columns: Set[TransactionColumn], maskPii: Boolean, skipProdValidation: Boolean = false)(implicit ec: ExecutionContext): Future[ApiTransactionResult] = {
    getEndpoint().flatMap(transactionAuditingThrottlingEndpointResponse => {
      val auditServerEndpoint: String = transactionAuditingThrottlingEndpointResponse.endpointUrl
      val codePath: String = if(transactionAuditingThrottlingEndpointResponse.isParallel) whitesourceCodePath else mainCodePath
      val apiName = "/transaction/internal"
      val columnStr = columns.mkString(",")

      val queryParams: Iterable[(String, String)] = Iterable(
        "columns" -> columnStr,
        "maskPii" -> maskPii.toString
      )

      val req: Req = (url(auditServerEndpoint + apiName) / transactionId.trim <<? queryParams)
        .GET
        .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)

      processRequest(req, apiName, codePath).map {
        case httpResp if httpResp.getStatusCode == 200 =>
          Try(parseTransaction(httpResp.getResponseBody())) match {
            case Success(s) =>
              ApiTransactionResult(httpResp.getStatusCode, s.map(elem => toApiTransaction(elem, skipProdValidation)))
            case Failure(e) =>
              logger.error("Unable to parse the transaction", e)
              ApiTransactionResult(500, Seq.empty)
          }
        case e@_ => ApiTransactionResult(e.getStatusCode, Seq.empty)

      }
    })
  }

  def findTrxByIdOrCuid(accountIds: Set[Long], id: String, columns: Set[TransactionColumn], maskPii: Boolean)(implicit ec: ExecutionContext): Future[ApiTransactionResult] = {
    getEndpoint().flatMap(transactionAuditingThrottlingEndpointResponse => {
      val auditServerEndpoint: String = transactionAuditingThrottlingEndpointResponse.endpointUrl
      val codePath: String = if(transactionAuditingThrottlingEndpointResponse.isParallel) whitesourceCodePath else mainCodePath
      val apiName = "/transaction/search_by_id_or_cuid"
      val request = url(auditServerEndpoint + apiName)
        .POST
        .addParameter("id", id.trim)
        .addParameter("accountIds", accountIds.mkString(","))
        .addParameter("columns", columns.mkString(","))
        .addParameter("maskPii", maskPii.toString)
        .setContentType(ContentType.APPLICATION_FORM_URLENCODED.getMimeType, StandardCharsets.UTF_8)

      processRequest(request, apiName, codePath).map { httpResp =>
        ApiTransactionResult(httpResp.getStatusCode, parseTransactionList(httpResp.getResponseBody).map(toApiTransaction))
      }   
    })
  }

  def fetchTrxList(transactionIds: Set[String], columns: Set[TransactionColumn], maskPii: Boolean)(implicit ec: ExecutionContext): Future[ApiTransactionResult] = {
    getEndpoint().flatMap(transactionAuditingThrottlingEndpointResponse => {
      val auditServerEndpoint: String = transactionAuditingThrottlingEndpointResponse.endpointUrl
      val codePath: String = if(transactionAuditingThrottlingEndpointResponse.isParallel) whitesourceCodePath else mainCodePath
      val apiName = "/transaction/search/ids"

      val request = url(auditServerEndpoint + apiName)
        .POST
        .addParameter("ids", transactionIds.mkString(","))
        .addParameter("columns", columns.mkString(","))
        .addParameter("maskPii", maskPii.toString)
        .setContentType(ContentType.APPLICATION_FORM_URLENCODED.getMimeType, StandardCharsets.UTF_8)

      processRequest(request, apiName, codePath).map { httpResp =>
        ApiTransactionResult(httpResp.getStatusCode, parseTransactionList(httpResp.getResponseBody).map(toApiTransaction))
      }
    })
  }

  def fetchMonitorTrxList(transactionIds: Set[String], maskPii: Boolean)(implicit ec: ExecutionContext): Future[MonitorApiTransactionResult] = {
    getEndpoint().flatMap(transactionAuditingThrottlingEndpointResponse => {
      val auditServerEndpoint: String = transactionAuditingThrottlingEndpointResponse.endpointUrl
      val codePath: String = if(transactionAuditingThrottlingEndpointResponse.isParallel) whitesourceCodePath else mainCodePath
      val apiName = "/transaction/monitor/search/ids"
      val request = url(auditServerEndpoint + apiName)
        .POST
        .addParameter("ids", transactionIds.mkString(","))
        .addParameter("maskPii", maskPii.toString)
        .setContentType(ContentType.APPLICATION_FORM_URLENCODED.getMimeType, StandardCharsets.UTF_8)

      processRequest(request, apiName, codePath).map { httpResp =>
        MonitorApiTransactionResult(httpResp.getStatusText, parseMonitorTransactionList(httpResp.getResponseBody()))
      }
    })
  }

  def countTransactionDynamo(tsr: TransactionSearchRequestDynamo)(implicit ec: ExecutionContext): Future[Int] = {
    getEndpoint().flatMap(transactionAuditingThrottlingEndpointResponse => {
      val auditServerEndpoint: String = transactionAuditingThrottlingEndpointResponse.endpointUrl
      val codePath: String = if(transactionAuditingThrottlingEndpointResponse.isParallel) whitesourceCodePath else mainCodePath
      val apiName = "/transaction/count/dynamo"

      val jsonBody = Serialization.write(tsr)
      val request = url(auditServerEndpoint + apiName)
        .POST
        .setBody(jsonBody)
        .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)

      processRequest(request, apiName, codePath).map { httpResponse =>
        httpResponse.getResponseBody.toInt
      }
    })
  }

  def countTransactionDynamoBlocking(tsr: TransactionSearchRequestDynamo)(implicit ec: ExecutionContext): Int = {
    Await.result(countTransactionDynamo(tsr), defaultBlockingTimeout.seconds)
  }

  private def parseMonitorTransactionList(response: String): List[MonitorExecParameters] = {
    if (logger.isDebugEnabled())
      logger.debug("Parsing returned monitor transaction list data: " + response)
    parseJson(response, tag = "trx_monitor_list")(parsed => parsed.extract[MonitorApiTransactionResult]).data
  }

  def toJava(scoringComponentMap: Map[String, Map[String, Int]]): JMap[String, JMap[String, JInteger]] = {
    new JHashMap[String, JMap[String, JInteger]](
      scoringComponentMap
        .mapValues(v => new JHashMap[String, JInteger](v.mapValues(int2Integer).asJava))
        .asJava
    )
  }


  def findTransactionBlocking(transactionId: String, columns: Set[TransactionColumn], maskPii: Boolean)(implicit ec: ExecutionContext): ApiTransactionResult = {
    findTransactionBlocking(transactionId, columns, maskPii, false)
  }

  def findTransactionBlocking(transactionId: String, columns: Set[TransactionColumn], maskPii: Boolean, skipProdValidation: Boolean = false)(implicit ec: ExecutionContext): ApiTransactionResult = {
    Await.result(findTransaction(transactionId, columns, maskPii, skipProdValidation), defaultBlockingTimeout.seconds)
  }

  def findTransactionWithInternalDataBlocking(transactionId: String, columns: Set[TransactionColumn], maskPii: Boolean, skipProdValidation: Boolean = false)(implicit ec: ExecutionContext): ApiTransactionResult = {
    Await.result(findTransactionWithInternalData(transactionId, columns, maskPii, skipProdValidation), defaultBlockingTimeout.seconds)
  }

  private def parseTransactionList(response: String): Seq[SlickApiTransaction] = {
    if (logger.isDebugEnabled())
      logger.debug("Parsing returned transaction list data: " + response)
    parseJson(response, tag = "trx_list")(parsed => parsed.extract[MultiTransactionResponse]).data
  }

  private def parseTransactionSearchResponseDynamo(response: String): TransactionSearchResponseData = {
    if (logger.isDebugEnabled()) logger.debug("Parsing returned transaction list data: " + response)
    parseJson(response, tag = "trx_search")(parsed => parsed.extract[MultiTransactionResponseDynamo]).data
  }

  private def parseTransactionSearchResponseV2(response: String): TransactionSearchResponseV2 = {
    if (logger.isDebugEnabled())
      logger.debug("Parsing returned transaction list v2 data: " + response)
    parseJson(response, tag = "trx_list_v2")(parsed => parsed.extract[MultiTransactionResponseV2]).data
  }

  private def parseTransaction(response: String): Seq[SlickApiTransaction] = {
    if (logger.isDebugEnabled())
      logger.debug("Parsing returned transaction data: " + response)
    Seq(parseJson(response, tag = "single_trx")(parsed => parsed.extract[SingleTransactionResponse]).data)
  }

  private def parseJson[T](msg: String, tag: String)(parser: JValue => T): T = {
    val json = metrics.time(s"parsing.duration.$tag")(Try(parse(msg)))
    json match {
      case Success(extractedJson: JValue) =>
        metrics.time(s"extract.duration.$tag")(parser(extractedJson))
      case Failure(ex) =>
        throw new Exception("Unable to parse auditing rest response", ex)
    }
  }
}
