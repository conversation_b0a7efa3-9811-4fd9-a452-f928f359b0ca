package me.socure.transactionauditing.rest.client

import com.typesafe.config.Config
import me.socure.common.hmac.algorithm.{HMACEncrypter, SHAHMACEncrypter}
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.dynamic.control.center.v2.factory.DynamicControlCenterV2Factory
import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

object TransactionRestClientFactory {

  private implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  @deprecated(message = "use createClient instead")
  def create(realm: String, version: String,  auditServerEndpoint: String, encrypter: HMACEncrypter, timeoutOpt: Option[Int], endpoint2: Option[String] = None, dynamicControlCenterV2Evaluate: Option[DynamicControlCenterV2Evaluate] = None, metricsEnabled: Boolean = true): TransactionRestClient = {
    val hmacHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpFactory = new NonSecuredHttpFactory()
    timeoutOpt.foreach(timeout => {
      httpFactory.setDefaultHTTPTimeout(timeout)
    })
    val httpClient = httpFactory.getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new TransactionRestClient(httpClient, auditServerEndpoint, endpoint2, dynamicControlCenterV2Evaluate, metricsEnabled)
  }

  @deprecated(message = "use createClient instead")
  def create(config: Config): TransactionRestClient = {

    val realm = config.getString("hmac.realm")
    val version = config.getString("hmac.version")
    val auditServerEndpoint = config.getString("transaction.auditing.endpoint")
    val endpoint2 = if(config.hasPath("transaction.auditing.endpoint2")) Option(config.getString("transaction.auditing.endpoint2")) else None
    val timeout = if(config.hasPath("transaction.auditing.timeout")) Some(config.getInt("transaction.auditing.timeout")) else None
    val secretKey = config.getString("hmac.secret.key")
    val strength = config.getInt("hmac.strength")
    val metricsEnabled = if(config.hasPath("transaction.auditing.metrics.enabled")) config.getBoolean("transaction.auditing.metrics.enabled") else true
    val hmacEncrypter = new SHAHMACEncrypter(
      secretKey = secretKey,
      strength = strength
    )
    val dynamicControlCenterEvaluate = if (config.hasPath("dynamic.control.center")) Option(DynamicControlCenterV2Factory.getEvaluator(config)) else None
    create(realm = realm, version = version, auditServerEndpoint = auditServerEndpoint, encrypter = hmacEncrypter, timeoutOpt = timeout, endpoint2 = endpoint2, dynamicControlCenterEvaluate, metricsEnabled)
  }

  def create(config: Config, isSecretsManagerConfigured: Boolean): TransactionRestClient = {
    if(isSecretsManagerConfigured) {
      val http = HttpWithHmacFactory.createInsecure(config = config.getConfig("hmac"))
      val auditServerEndpoint = config.getString("endpoint")
      val endpoint2 = if(config.hasPath("endpoint2")) Option(config.getString("endpoint2")) else None
      val dynamicControlCenterEvaluate = if (config.hasPath("dynamic.control.center")) Option(DynamicControlCenterV2Factory.getEvaluator(config)) else None
      val metricsEnabled = if(config.hasPath("metrics.enabled")) config.getBoolean("metrics.enabled") else true
      new TransactionRestClient(http, auditServerEndpoint, endpoint2, dynamicControlCenterEvaluate, metricsEnabled)
    } else {
      create(config)
    }
  }

  def createClient(config: Config)(implicit ec: ExecutionContext): TransactionRestClient = {
    val http = HttpWithHmacFactory.createInsecure(config = config.getConfig("hmac"))
    val auditServerEndpoint = config.getString("endpoint")
    val endpoint2 = if(config.hasPath("endpoint2")) Option(config.getString("endpoint2")) else None
    val dynamicControlCenterEvaluate = if (config.hasPath("dynamic.control.center")) Option(DynamicControlCenterV2Factory.getEvaluator(config)) else None
    val metricsEnabled = if(config.hasPath("metrics.enabled")) config.getBoolean("metrics.enabled") else true
    new TransactionRestClient(http, auditServerEndpoint, endpoint2, dynamicControlCenterEvaluate, metricsEnabled)
  }
}
