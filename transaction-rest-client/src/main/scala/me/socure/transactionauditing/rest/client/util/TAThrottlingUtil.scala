package me.socure.transactionauditing.rest.client.util

import me.socure.dynamic.control.center.v2.response.EvaluateResponse
import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate

import scala.concurrent.{ExecutionContext, Future}

object TAThrottlingUtil {
  private val groupName = "IdplusServicesRamp"
  private val flagName = "TransactionAuditingService_Ramp"
  def getEndpoint(
                   endpoint: String,
                   endpoint2: Option[String] = None,
                   dynamicControlCenterV2Evaluate: Option[DynamicControlCenterV2Evaluate] = None)(
                   implicit ec: ExecutionContext)
  : Future[TransactionAuditingThrottlingEndpointResponse] = {
    (dynamicControlCenterV2Evaluate, endpoint2) match {
      case (Some(
      dynamicControlCenterV2Evaluate: DynamicControlCenterV2Evaluate),
      Some(endpoint2: String)) =>
        dynamicControlCenterV2Evaluate.evaluate(groupName, flagName) map {
          case Right(res: EvaluateResponse) =>
            if (res.isFlagActive) TransactionAuditingThrottlingEndpointResponse(endpoint2, isParallel = true)
            else TransactionAuditingThrottlingEndpointResponse(endpoint, isParallel = false)
          case Left(e) =>
            TransactionAuditingThrottlingEndpointResponse(endpoint, isParallel = false)
        }
      case _ =>
        Future.successful(
          TransactionAuditingThrottlingEndpointResponse(endpoint, isParallel = false))
    }
  }

  case class TransactionAuditingThrottlingEndpointResponse(
                                                 endpointUrl: String,
                                                 isParallel: Boolean
                                               )
}
