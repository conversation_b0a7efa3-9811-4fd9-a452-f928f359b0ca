package me.socure.transactionauditing.rest.client

import me.socure.common.data.core.DataNotFoundException
import me.socure.common.data.core.service._
import me.socure.common.data.memcached._
import me.socure.common.metrics.JavaMetricsFactory
import me.socure.common.retry.strategy.RetryStrategy
import me.socure.common.transaction.id.TrxId
import me.socure.transactionauditing.sqs.model.summary.MonitorExecParameters
import net.spy.memcached.MemcachedClient
import org.slf4j.LoggerFactory

import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal

object WatchlistInfoServiceFactory {

  private def createRestService(restClient: TransactionRestClient)(implicit ec: ExecutionContext): DataService[TrxId, MonitorExecParameters] = {
    DataService { trxId =>
      try {
        restClient
          .fetchMonitorTrxList(Set(trxId.value), maskPii = false)
          .map { res =>
            if (Option(res.status).exists(_.equalsIgnoreCase("OK"))) {
              res.data.headOption.getOrElse(throw DataNotFoundException)
            } else throw new IllegalStateException(s"Error while fetching watchlist info. Status = ${res.status}")
          }
      } catch {
        case NonFatal(ex) => Future.failed(ex)
      }
    }
  }

  private def extractTags(params: MonitorExecParameters): Set[String] = {
    Set(
      params.accountId.map(accId => s"account_id:$accId"),
      params.environmentId.map(envId => s"environment_id:$envId"),
      Some("service_name:trx_audit_wl_info")
    ).flatten
  }

  def create(
              restClient: TransactionRestClient,
              memcachedClient: MemcachedClient,
              expiration: FiniteDuration,
              retryStrategy: RetryStrategy = RetryStrategy.constantBackoff(100.milliseconds, maxAttempts = 3) //similar to the existing one
            )(implicit ec: ExecutionContext): DataService[TrxId, Option[MonitorExecParameters]] = {
    val restService = createRestService(restClient)

    restService
      .cached(memcachedClient, expiration)(trxId => MonitorExecParameters.cacheKey(trxId.value))
      .retry(retryStrategy)
      .withMetrics(
        metrics = JavaMetricsFactory.get("data.service"),
        onSuccessTags = (_, params) => extractTags(params),
        onFailureTags = (_, _) => Set.empty
      )
      .withLogging(
        logger = LoggerFactory.getLogger("data.service.trx_audit_wl_info"),
        inToStr = _.value,
        outToStr = params => String.valueOf(params.copy(
          firstName = params.firstName.map(_ => "***"),
          lastName = params.lastName.map(_ => "***"),
          dob = params.dob.map(_ => "***")
        ))
      )
      .opt()
  }
}
