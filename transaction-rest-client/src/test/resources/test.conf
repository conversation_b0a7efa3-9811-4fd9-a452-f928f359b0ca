withDBMigration=true

database {
  audit {
    jdbcUrl="testconnectionstring"
    user=vpcadmin
    password="password"
    driver="com.mysql.cj.jdbc.Driver"

    maxPoolSize=25
    minPoolSize=5
    testConnectionOnCheckIn=true
    testConnectionOnCheckOut=false
    idleConnectionTestPeriod=0
    dataSourceName="socureauditing"
  }
}

account.service {
  endpoint = "https://socure-account-service-stage2.us-east-1.elasticbeanstalk.com"
}

memcached {
  host=stage-vpc-east-cache.ps2mlp.cfg.use1.cache.amazonaws.com
  port=11211
}

client.specific.encryption {
  aws {
    access.key = """ENC(eKApAl/OrHhzDywyzxvFLz08NjSeNoyrwGNc1i4irIW+m2sIBWAWH5U8Ju5WgrFy)"""
    secret.key = """ENC(ri/bduKVOs/5LbfkkCvL7Mrh9KjBAI/UIvO0etoaxxGPhloprM3DhN3kD2KgO+mtdeDatqsX3gAyVI7yJFFneg==)"""
  }
  encryption.context.account_id.key = "socure_account_id"
  kms.ids {

    "us-east-1" = """arn:aws:kms:us-east-1:************:alias/client-specific-encryption-stage"""
    "us-west-1" = """arn:aws:kms:us-west-1:************:alias/client-specific-encryption-stage"""
  }
}

transaction-auditing {
  threadpool {
    poolSize=50
  }

  aws {
    access.key="ENC(JpFgWxoRGE7x2kmmAYusdl1ihkND5Kz5Qk/o7Wa9434BD6f9ie5vd2Nnv7SLAX+z)"
    access.secret="ENC(DpLgZxsL3K/sGmDaiXPQwRNb5XDdTNlNht7fMG4Hju7nH7lZMYdZaLRT25+B3vczg5qqwQuexkhvOfNoqIkGIA==)"
    maxRetries = 10

    primary {
      sqs {
        region=us-east-1
        transaction {
          queueName=transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback0 {
      sqs {
        region=us-west-1
        transaction {
          queueName=transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    fallback1 {
      sqs {
        region=us-east-2
        transaction {
          queueName=transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        third-party {
          queueName=third-party-transaction-auditing-stage
          waitTimeSeconds=20
          maxBatchSize=10
          maxBufferSize=60
        }
        producer {
          #maximum number of messages being processed by AmazonSQSAsync at the same time
          transaction {
            maxInFlight: 15
          }
          third-party {
            maxInFlight: 15
          }
        }
      }
    }

    s3 {
      third-party {
        region=us-east-1
        bucket="thirdparty-stats-stage"
      }
    }

    sqs {
      backoff {
        # seconds
        min: 2
        max: 32
      }
    }
  }
}