package me.socure.transactionauditing.rest.client

import dispatch.Future
import me.socure.common.docker.memcachedservice.MemcachedSupport
import me.socure.common.random.Random
import me.socure.common.transaction.id.TrxId
import me.socure.transactionauditing.common.domain.MonitorApiTransactionResult
import me.socure.transactionauditing.sqs.model.summary.MonitorExecParameters
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.joda.time.{DateTime, DateTimeZone}
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Milliseconds, Seconds, Span}
import org.scalatest.{BeforeAndAfter, BeforeAndAfterAll, FreeSpec, Matchers}

import scala.concurrent.ExecutionContext
import scala.concurrent.duration._

class WatchlistInfoServiceFactoryTest extends FreeSpec with Matchers with ScalaFutures with MemcachedSupport with BeforeAndAfterAll with MockFactory with BeforeAndAfter {
  private implicit val ec: ExecutionContext = ExecutionContext.Implicits.global
  private implicit val patienceConf: PatienceConfig = PatienceConfig(
    timeout = Span(10, Seconds),
    interval = Span(50, Milliseconds)
  )

  private class MockableRestClient extends TransactionRestClient(null, "")

  private lazy val memcachedClient = new MemcachedClient(AddrUtil.getAddresses(s"$memcachedHost:$memcachedPort"))
  private val restClient = mock[MockableRestClient]

  private val expiration = 10.minutes
  private lazy val service = WatchlistInfoServiceFactory.create(
    restClient = restClient,
    memcachedClient = memcachedClient,
    expiration = expiration
  )

  private val transactionId = Random.uuids()
  private val trxId = TrxId(transactionId)
  private val cacheKey = MonitorExecParameters.cacheKey(transactionId)

  private val watchlistInfo = MonitorExecParameters(
    transactionId = Some(transactionId),
    firstName = Some("abu"),
    lastName = Some("bakhar"),
    dobAndName = Some(false),
    dobTolerance = Some(true),
    dobMatchLogic = Some("ExactDob"),
    fuzzinessTolerance = Some(0.5),
    dob = None,
    tier = Some("watchlistpremier"),
    searchId = Some(********),
    accountId = Some(10L + math.abs(Random.nextLong() - 10000)),
    environmentId = Some(Random.shuffle(Set(1, 2, 3)).head),
    isOnlyCategoryAdverseMedia = Some(true),
    transactionDate = Some(DateTime.now(DateTimeZone.UTC)),
    message = None,
    userId = None,
    customerUserId = None,
    screeningCategory = Some(Set("adverse-media")),
    limit = Some(10),
    country = Some(Set.empty),
    sourceNameSet = Some(Set("Adverse Media Financial Crime", "Adverse Media General", "Adverse Media Fraud")),
    hasWatchlistFilters = Some(true),
    entityName = Some("Dolce & Gabbana"),
    entityType = Some("organisation"),
    reqCountry = None,
    watchlistTransactionType = Some("Hybrid"),
    nationalId = Some("*********"),
    phone = Some("**********"),
    email = Some("<EMAIL>"),
    state = Some("ct"),
    city = Some("hartford"),
    zip = Some("06182"),
    parentTxnId = None,
    workflow = None,
    riskOSId = None,
    fullName = None,
    invokedVendors = None
  )


  "should fetch from memcached when available" in {
    memcachedClient.set(cacheKey, expiration.toSeconds.toInt, watchlistInfo)
    whenReady(service(trxId))(_ shouldBe Some(watchlistInfo))
  }

  "should fetch from source when not available in memcached and put it in memcached" in {
    val monitorRestResult = MonitorApiTransactionResult(
      status = "OK",
      data = List(watchlistInfo)
    )

    (restClient.fetchMonitorTrxList(_: Set[String], _:Boolean)(_: ExecutionContext))
      .expects(Set(transactionId), false, ec)
      .returns(Future.successful(monitorRestResult))
      .once()

    whenReady(service(trxId))(_ shouldBe Some(watchlistInfo))
    whenReady(service(trxId))(_ shouldBe Some(watchlistInfo))
  }

  before {
    memcachedClient.delete(cacheKey)
  }

  override protected def beforeAll(): Unit = {
    super.beforeAll()
    startupMemcached()
  }

  override protected def afterAll(): Unit = {
    super.afterAll()
    cleanupMemcached()
  }
}
