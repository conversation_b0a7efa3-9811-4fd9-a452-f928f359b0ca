package me.socure.transactionauditing.rest.client

import java.util.{Date, UUID}

import dispatch.Http
import javax.servlet.http.{HttpServlet, HttpServletRequest, HttpServletResponse}
import me.socure.common.clock.FakeClock
import me.socure.common.random.Random
import me.socure.common.servlettester.ServletTester._
import me.socure.common.servlettester.StringServletWithParamsValidation
import me.socure.common.slick.domain.{Pagination, Sort}
import me.socure.constants.EnvironmentConstants
import me.socure.model.{Response, ResponseStatus}
import me.socure.transactionauditing.common.domain.fixture.APITransactionValueGenerator
import me.socure.transactionauditing.common.domain._
import me.socure.transactionauditing.common.json.TrxJsonFormats
import me.socure.transactionauditing.common.product.Products
import me.socure.transactionauditing.common.summary.watchlist.{WatchlistMonitoringOpsColumns, WatchlistMonitoringOpsRequest}
import me.socure.transactionauditing.common.transaction.{TransactionColumnFixtures, TransactionColumns}
import me.socure.transactionauditing.common.util.SlickDomainConversion
import me.socure.transactionauditing.sqs.model.summary.MonitorExecParameters
import org.apache.commons.io.IOUtils
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.Formats
import org.json4s.jackson.Serialization
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Milliseconds, Seconds, Span}
import org.scalatest.{FunSuite, Matchers}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class TransactionRestClientTest extends FunSuite with Matchers with ScalaFutures with MockitoSugar {
  private implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  private implicit val jsonFormats: Formats = TrxJsonFormats.value
  override implicit val patienceConfig: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(50, Milliseconds))

  private val clock = new FakeClock(new Date().getTime)
  private val apiTransactions = (1 to 10).map(_ => APITransactionValueGenerator.aAPITransaction())
  private val apiTransactionsWithInternalWorkLogs = (1 to 10).map(i => APITransactionValueGenerator.aAPITransaction(internalWorkLogs = Some(s"Test $i")))

  test("test basic request") {
    val id = UUID.randomUUID().toString
    val columns = randCols()
    val columnsStr = columns.mkString(",")

    withServlet(
      servlet = new StringServletWithParamsValidation(
        status = 200,
        content = Serialization.write(Response(
          status = ResponseStatus.Ok,
          data = SlickDomainConversion.toSlickTransaction(apiTransactions.head)
        )),
        expectedParameters = Map(
          "columns" -> columnsStr,
          "maskPii" -> "false"
        )
      ),
      path = s"/transaction/$id"
    ) { port =>
      val transactionClient = new TransactionRestClient(http = Http.default, auditServerEndpoint = s"http://localhost:$port")
      whenReady(transactionClient.findTransaction(
        transactionId = id,
        columns = columns,
        maskPii = false
      )) { result =>
        result shouldBe ApiTransactionResult(
          status = 200,
          response = Seq(apiTransactions.head)
        )
      }
    }
  }

  test("test findTransactionWithInternalData") {
    val id = UUID.randomUUID().toString
    val columns = randCols()
    val columnsStr = columns.mkString(",")

    withServlet(
      servlet = new StringServletWithParamsValidation(
        status = 200,
        content = Serialization.write(Response(
          status = ResponseStatus.Ok,
          data = SlickDomainConversion.toSlickTransaction(apiTransactionsWithInternalWorkLogs.head)
        )),
        expectedParameters = Map(
          "columns" -> columnsStr,
          "maskPii" -> "false"
        )
      ),
      path = s"/transaction/internal/$id"
    ) { port =>
      val transactionClient = new TransactionRestClient(http = Http.default, auditServerEndpoint = s"http://localhost:$port")
      whenReady(transactionClient.findTransactionWithInternalData(
        transactionId = id,
        columns = columns,
        maskPii = false
      )) { result =>
        result shouldBe ApiTransactionResult(
          status = 200,
          response = Seq(apiTransactionsWithInternalWorkLogs.head)
        )
      }
    }
  }

  test("test basic request - with invalid transaction id") {
    val id = UUID.randomUUID().toString
    val columns = randCols()
    val columnsStr = columns.mkString(",")

    withServlet(
      servlet = new StringServletWithParamsValidation(
        status = 400,
        content = Serialization.write(Response(
          status = ResponseStatus.Error,
          data = """{
                   |    "status": "error",
                   |    "data": {
                   |        "code": 11,
                   |        "message": "No Transactions found"
                   |    }
                   |}"""
        )),
        expectedParameters = Map(
          "columns" -> columnsStr,
          "maskPii" -> "false"
        )
      ),
      path = s"/transaction/$id"
    ) { port =>
      val transactionClient = new TransactionRestClient(http = Http.default, auditServerEndpoint = s"http://localhost:$port")
      whenReady(transactionClient.findTransaction(
        transactionId = id,
        columns = columns,
        maskPii = false
      )) { result =>
        result shouldBe ApiTransactionResult(
          status = 400,
          response = Seq.empty
        )
      }
    }
  }

  test("test search") {
    val tsr = TransactionSearchRequest(
      accountId = None,
      accountIds = Some(Set(Random.nextLong())),
      start = Some(DateTime.now(DateTimeZone.UTC)),
      end = Some(DateTime.now(DateTimeZone.UTC)),
      environment = Some(EnvironmentConstants.values),
      pagination = Some(Pagination(Random.nextInt(), Random.nextInt())),
      product = Some(Products.EmailAuthScore.All ++ Products.AuthScore.All ++ Products.Watchlist.All),
      isKyc = Some(Random.nextBoolean()),
      isError = Some(Random.nextBoolean()),
      customerUserId = Some(Random.alphaNumeric()),
      runId = None,
      columns = randCols(),
      startTransactionId = Some(Random.nextLong()),
      sorting = Some(TransactionColumnFixtures.AllTransactionColumns.map { col =>
        Sort(column = col, ascending = Random.nextBoolean())
      }.toSeq),
      maskPii = false
    )

    withServlet(
      servlet = new HttpServlet {
        override def doPost(req: HttpServletRequest, resp: HttpServletResponse): Unit = {
          val actualReq = Serialization.read[TransactionSearchRequest](IOUtils.toString(req.getInputStream))
          actualReq shouldBe tsr
          resp.getWriter.write(Serialization.write(Response(
            status = ResponseStatus.Ok,
            data = apiTransactions.map(SlickDomainConversion.toSlickTransaction)
          )))
        }
      },
      path = s"/transaction/search"
    ) { port =>
      val transactionClient = new TransactionRestClient(http = Http.default, auditServerEndpoint = s"http://localhost:$port")
      whenReady(transactionClient.searchTransaction(
        tsr = tsr
      )) { result =>
        result shouldBe ApiTransactionResult(
          status = 200,
          response = apiTransactions
        )
      }
    }
  }

  test("test search by transaction-id or customer-user-id") {
    val id = Random.alphaNumeric(10)
    val columns = randCols()
    val maskPii = Random.nextBoolean()
    val accountIds = List.fill(3)(Random.nextLong()).toSet[Long]
    withServlet(
      servlet = new HttpServlet {
        override def doPost(req: HttpServletRequest, resp: HttpServletResponse): Unit = {
          req.getParameter("id") shouldBe id
          req.getParameter("columns").split(",").flatMap(TransactionColumns.byColumn).toSet[TransactionColumns.TransactionColumn] shouldBe columns
          req.getParameter("maskPii").toBoolean shouldBe maskPii
          req.getParameter("accountIds").split(",").map(_.toLong).toSet[Long] shouldBe accountIds
          resp.getWriter.write(Serialization.write(Response(
            status = ResponseStatus.Ok,
            data = apiTransactions.map(SlickDomainConversion.toSlickTransaction)
          )))
        }
      },
      path = s"/transaction/search_by_id_or_cuid"
    ) { port =>
      val transactionClient = new TransactionRestClient(http = Http.default, auditServerEndpoint = s"http://localhost:$port")
      whenReady(transactionClient.findTrxByIdOrCuid(
        accountIds = accountIds,
        id = id,
        columns = columns,
        maskPii = maskPii
      )) { result =>
        result shouldBe ApiTransactionResult(
          status = 200,
          response = apiTransactions
        )
      }
    }
  }


  test("test search by list of transactions") {
    val columns = randCols()
    val maskPii = Random.nextBoolean()
    val ids = List.fill(10)(Random.alphaNumeric(10)).toSet[String]
    withServlet(
      servlet = new HttpServlet {
        override def doPost(req: HttpServletRequest, resp: HttpServletResponse): Unit = {
          req.getParameter("ids").split(",").toSet shouldBe ids
          req.getParameter("columns").split(",").flatMap(TransactionColumns.byColumn).toSet[TransactionColumns.TransactionColumn] shouldBe columns
          req.getParameter("maskPii").toBoolean shouldBe maskPii
          resp.getWriter.write(Serialization.write(Response(
            status = ResponseStatus.Ok,
            data = apiTransactions.map(SlickDomainConversion.toSlickTransaction)
          )))
        }
      },
      path = s"/transaction/search/ids"
    ) { port =>
      val transactionClient = new TransactionRestClient(http = Http.default, auditServerEndpoint = s"http://localhost:$port")
      whenReady(transactionClient.fetchTrxList(
        transactionIds = ids,
        columns = columns,
        maskPii = maskPii
      )) { result =>
        result shouldBe ApiTransactionResult(
          status = 200,
          response = apiTransactions
        )
      }
    }
  }

  test("test monitor search by list of transactions") {
    val maskPii = Random.nextBoolean()
    val ids = List.fill(10)(Random.alphaNumeric(10)).toSet[String]
    val data = List(
      MonitorExecParameters(
        Some("d76d6e00-6535-442b-9e66-6a3e72e70c0c"),
        Some("robert"),
        Some("mugabe"),
        Some(false),
        Some(true),
        Some("exact"),
        Some(0.5),
        Some("1965-09-09"),
        Some("watchlistpremier"),
        Some(34510406),
        Some(1786),
        Some(1),
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        Some("Hybrid"),
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        None
      )
    )
    withServlet(
      servlet = new HttpServlet {
        override def doPost(req: HttpServletRequest, resp: HttpServletResponse): Unit = {
          req.getParameter("ids").split(",").toSet shouldBe ids
          req.getParameter("maskPii").toBoolean shouldBe maskPii
          resp.getWriter.write(Serialization.write(Response(
            status = ResponseStatus.Ok,
            data = data
          )))
        }
      },
      path = s"/transaction/monitor/search/ids"
    ) { port =>
      val transactionClient = new TransactionRestClient(http = Http.default, auditServerEndpoint = s"http://localhost:$port")
      whenReady(transactionClient.fetchMonitorTrxList(
        transactionIds = ids,
        maskPii = maskPii
      )) { result =>
        result shouldBe MonitorApiTransactionResult(
          status = "OK",
          data = data
        )
      }
    }
  }

  private def randCols(): Set[TransactionColumns.TransactionColumn] = {
    Random.shuffle(
      TransactionColumns.values.toSet
    ).take(2 + Random.nextInt(TransactionColumns.values.size - 1))
  }
}
