image:
  repository: fips-registry.us-east-1.build.socure.link/idf/transaction-auditing-worker
  tag: "OVERRIDE_ME"

serviceAccount:
  name: "transaction-auditing-worker-dev"
  annotations:
    eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/eks-irsa-1a15eab-dev-eb9f3a09"

application:
  env:
    CONFIGURATION_NAME: "transaction-auditing-worker"
    CONFIGURATION_VERSION: "0.0.006"
    DD_CONSTANT_TAGS: "ddname:socure-transaction-auditing-worker"

istio:
  enabled: true
  hosts:
    - transaction-auditing-worker.webapps.us-east-1.product-dev.socure.link
  svcPort: 80
  private:
    gateway: private-gw
  authorizationPolicy:
    enabled: false
