fullnameOverride: "transaction-auditing-service"

image:
  repository: fips-registry.us-east-1.build.socure.link/idf/transaction-auditing-service
  tag: "OVERRIDE_ME"

serviceAccount:
  name: "transaction-auditing-service-dev"
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/eks-irsa-f14e8f0-dev-95030f07

application:
  env:
    CONFIGURATION_NAME: "transaction-auditing-service"
    CONFIGURATION_VERSION: "0.0.007"
    DD_CONSTANT_TAGS: "ddname:socure-transaction-auditing"

istio:
  enabled: true
  hosts:
  - transaction-auditing-service.webapps.us-east-1.product-dev.socure.link
  svcPort: 80
  private:
    gateway: private-gw
  authorizationPolicy:
    enabled: true
    pa:
      enabled: true
      serviceAccounts:
        - "model-monitoring-dev"
        - "idplus-service-dev"
        - "thirdparty-auditing-worker-dev"
        - "thirdparty-reader-service-dev"
        - "step-up-dev"
        - "admin-dashboard-dev"
        - "batch-job-cip-report-dev-service-account"
        - "batch-job-mvb-report-dev-service-account"
        - "super-admin-dev"
        - "demo-service-dev"
        - "batch-job-export-transaction-dev"
        - "entity-monitoring-service-dev"
        - "watchlist-private-service-dev"
        - "txn-case-workflow-service-dev"
        - "batch-job-transaction-index-dev"
        - "docv-orchestra-dev"
        - "file-upload-service-dev"
        - "txn-case-workflow-manager-dev"
