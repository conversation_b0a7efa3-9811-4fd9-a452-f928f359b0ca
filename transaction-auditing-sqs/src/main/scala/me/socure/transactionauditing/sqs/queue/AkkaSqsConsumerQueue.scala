package me.socure.transactionauditing.sqs.queue

import akka.actor.ActorSystem
import akka.stream.ActorMaterializer
import akka.stream.alpakka.sqs.MessageAction
import akka.stream.scaladsl.Flow
import com.amazonaws.services.sqs.model._
import me.socure.sqs.alpakka.{SqsAckSink, SqsSource}
import me.socure.transactionauditing.common.status.ProcessResults
import me.socure.transactionauditing.common.util.SqsQueueEnums.SqsQueueEnum
import me.socure.transactionauditing.sqs.sqspool.ServiceSqsEndpoint

import scala.concurrent.{ExecutionContext, Future, TimeoutException}
import scala.util.{Failure, Success, Try}

@deprecated
case class QueueName(name: String)

@deprecated
abstract class AkkaSqsConsumerQueue(parallelism: Int)
                                      (implicit ec: ExecutionContext, system: ActorSystem, materializer: ActorMaterializer) extends SqsConsumerQueue {

  //Support handling multiple SQS endpoints as fallbacks in the future, but only one for now
  protected val sqsEndpoints: Seq[ServiceSqsEndpoint]
  val S3_Not_Found = AkkaSqsConsumerQueue.S3_Not_Found

  /**
    * @param msgType a message type for api transaction, 3rd party transaction etc
    * @param processingFailure exception for processing failure
    * @param streamFailure exception for sqs processing failure
    * @return A future signifying that the stream has terminated
    */
  override def startStream(msgType: SqsQueueEnum, processingFailure: (Throwable) => Unit, streamFailure: (Throwable) => Unit): Future[Any] = {
    val endpoint = sqsEndpoints(0)
    val flow = Flow[Message].mapAsyncUnordered(parallelism)((m: Message) => Future{
      Try {
        val processResult = consume(m)
        if(processResult == ProcessResults.FAILURE) throw new Exception("Failed to save message")
        else if(processResult == ProcessResults.TIMEOUT) throw new TimeoutException("timed out")
        else if(processResult == ProcessResults.S3NOTFOUND) throw new S3NotFoundException("missing s3 object")
      } match {
        case Success(_) => (m, MessageAction.Delete)
        case Failure(ex) =>
          Try(processingFailure(ex))
          ex match {
            case e: TimeoutException =>
              (m, MessageAction.ChangeMessageVisibility(0))
              throw e
            case _: S3NotFoundException =>
              (m, MessageAction.ChangeMessageVisibility(300))
            case _ =>
              (m, MessageAction.ChangeMessageVisibility(60))
          }
      }
    })
    SqsSource(endpoint.queue.url, endpoint.client, endpoint.queueSourceSettings)
          .via(flow)
      .recoverWithRetries(-1, {
        case e =>
          streamFailure(e)
          SqsSource(endpoint.queue.url, endpoint.client, endpoint.queueSourceSettings)
            .via(flow)
      })
      .runWith(SqsAckSink(endpoint.queue.url)(ec, endpoint.client))
  }

  def shutDown(): Unit = sqsEndpoints.head.client.shutdown()
}

@deprecated
object AkkaSqsConsumerQueue {
  val S3_Not_Found = "S3_NOT_FOUND"
}

