package me.socure.transactionauditing.sqs.marshaller

import me.socure.transactionauditing.sqs.model.SqsApiTransaction

/*
Truncate the fields that are susceptible to overflow in our current auditing schema
 */
object DataTruncator {
  val MaxLength = 255
  def truncate(txn: SqsApiTransaction): SqsApiTransaction = {
    txn.copy(
      customerUserId = txn.customerUserId.flatMap(truncate),
      apiKey = truncate(txn.apiKey).orNull,
      runId = txn.runId.flatMap(truncate),
      originOfInvocation = txn.originOfInvocation.flatMap(truncate),
      geocode = txn.geocode.flatMap(truncate)
    )
  }

  private def truncate(inputStr: String): Option[String] = {
    Option(inputStr).map(_.trim.take(MaxLength))
  }
}
