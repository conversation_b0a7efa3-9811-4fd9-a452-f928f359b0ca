package me.socure.transactionauditing.sqs.marshaller

import com.fasterxml.jackson.core.JsonParseException
import me.socure.transactionauditing.common.json.TrxJsonFormats
import me.socure.transactionauditing.sqs.model.SqsApiTransaction
import org.json4s._
import org.json4s.jackson.Serialization.write
import org.json4s.native.JsonMethods._

import java.nio.charset.StandardCharsets
import java.util.Base64
import scala.util.{Failure, Success, Try}

object SqsMessageMarshaller {
  private implicit val formats: Formats = TrxJsonFormats.value

  def marshal(msg: SqsApiTransaction): String = {
    val jsonBytes = write(msg).getBytes(StandardCharsets.UTF_8)
    val compressedBytes = Gzip.compress(jsonBytes)
    Base64.getEncoder.encodeToString(compressedBytes)
  }

  def unmarshalApiTransaction(sqsApiTransaction: String): SqsApiTransaction = {
    val decodedMessage = Base64.getDecoder.decode(sqsApiTransaction)
    val uncompressedMessage = Gzip.decompress(decodedMessage)
    val decodedMsg = new String(uncompressedMessage, StandardCharsets.UTF_8)
    val json = Try(parse(decodedMsg))
    json match {
      case Success(extractedJson: JValue) => extractedJson.extract[SqsApiTransaction]
      case Failure(ex) =>
        ex match {
          case jsonEx @ (_: MappingException | _: JsonParseException ) => throw new Exception(s"Unable to unmarshall API Transaction sqs message due to ${jsonEx.getClass}")
          case e => throw new Exception("Unable to unmarshall API Transaction sqs message", e)
        }
    }
  }
}