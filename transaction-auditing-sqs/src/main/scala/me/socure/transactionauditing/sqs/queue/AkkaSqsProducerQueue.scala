package me.socure.transactionauditing.sqs.queue

import akka.actor.ActorSystem
import akka.stream.ActorMaterializer
import akka.stream.scaladsl.Source
import com.amazonaws.handlers.AsyncHandler
import com.amazonaws.services.sqs.model.{SendMessageRequest, SendMessageResult}
import me.socure.sqs.alpakka.SqsSink
import me.socure.transactionauditing.common.config.ClientSqsEndpoint
import me.socure.transactionauditing.sqs.marshaller.SqsMessageMarshaller._
import me.socure.transactionauditing.sqs.model.{SqsApiTransaction, SqsMessage}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

@deprecated
class AkkaSqsProducerQueue(implicit system: ActorSystem, materializer: ActorMaterializer) extends SqsProducerQueue {

  /**
    * Not used at the moment
    * @param msg
    * @param sqsEndpoint
    * @param callback
    * @param ec
    * @return
    */
  override def send(msg: SqsMessage, sqsEndpoint: ClientSqsEndpoint, callback: AsyncHandler[SendMessageRequest, SendMessageResult])(implicit ec: ExecutionContext): Future[SendMessageResult] = {
    if (msg == null) Future.failed(new IllegalArgumentException("SQS Message can not be null"))
    else {
      Source
        .single(msg match {
          case apiTransaction: SqsApiTransaction => marshal(apiTransaction)
        })
        .watchTermination() { (_, done) =>
          done.onComplete {
            case Success(_) =>
              callback.onSuccess(null, null)
            case Failure(ex) =>
              callback.onSuccess(null, null)
          }
        }
        .runWith(SqsSink(sqsEndpoint.queue.url, sqsEndpoint.queueSinkSettings, sqsEndpoint.client))
      //todo: probably overide the SqsSink to provide the SendMessageResult back
      Future.successful(new SendMessageResult())
    }
  }
}
