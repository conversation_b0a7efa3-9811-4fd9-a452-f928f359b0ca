package me.socure.transactionauditing.sqs.marshaller

import java.io._

import org.apache.commons.compress.compressors.{CompressorInputStream, CompressorOutputStream, CompressorStreamFactory}

object Gzip {

  def getGzipOutputStream(outputStream: OutputStream): CompressorOutputStream = {
    new CompressorStreamFactory()
      .createCompressorOutputStream(CompressorStreamFactory.GZIP, outputStream)
  }

  def getGzipInputStream(inputStream: InputStream): CompressorInputStream = {
    new CompressorStreamFactory()
      .createCompressorInputStream(CompressorStreamFactory.GZIP, inputStream)
  }

  def compress(data: Array[Byte]): Array[Byte] = {
    val bos = new ByteArrayOutputStream(data.length)
    val gzipOut = getGzipOutputStream(bos)
    gzipOut.write(data)
    gzipOut.close()
    bos.toByteArray
  }

  def decompress(data: Array[Byte]): Array[Byte] = {
    val bin = new ByteArrayInputStream(data)
    val buffedIn = new BufferedInputStream(bin)
    val gzipIn = getGzipInputStream(buffedIn)
    Stream.continually(gzipIn.read).takeWhile(_ != -1).map(_.toByte).toArray
  }
}
