package me.socure.transactionauditing.sqs.queue

import com.amazonaws.handlers.AsyncHandler
import com.amazonaws.services.sqs.model.{SendMessageRequest, SendMessageResult}
import me.socure.transactionauditing.common.config.ClientSqsEndpoint
import me.socure.transactionauditing.sqs.model.SqsMessage

import scala.concurrent.{ExecutionContext, Future}

@deprecated
trait SqsProducerQueue extends Queue {
  def send(msg: SqsMessage, sqsEndpoint: ClientSqsEndpoint, callback: AsyncHandler[SendMessageRequest, SendMessageResult])
          (implicit ec: ExecutionContext): Future[SendMessageResult]
}
