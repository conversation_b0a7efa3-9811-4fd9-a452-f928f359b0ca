package me.socure.transactionauditing.sqs.queue

import com.amazonaws.handlers.AsyncHandler
import com.amazonaws.services.sqs.model.{MessageAttributeValue, SendMessageRequest, SendMessageResult}
import me.socure.transactionauditing.common.config.ClientSqsEndpoint
import me.socure.transactionauditing.common.util.CustomMessageAttributes
import me.socure.transactionauditing.sqs.marshaller.SqsMessageMarshaller._
import me.socure.transactionauditing.sqs.model.{SqsApiTransaction, SqsMessage}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

@deprecated
class AsyncSqsProducerQueue extends SqsProducerQueue {
  override def send(msg: SqsMessage, sqsEndpoint: ClientSqsEndpoint, callback: AsyncHandler[SendMessageRequest, SendMessageResult])
                   (implicit ec: ExecutionContext): Future[SendMessageResult] = {
    val request = msg match {
      case apiTransactionMsg: SqsApiTransaction =>
        withAttribute(
          new SendMessageRequest(
            sqsEndpoint.queue.url,
            marshal(apiTransactionMsg)
          ),
          CustomMessageAttributes.TRANSACTION_ID.name,
          apiTransactionMsg.transactionId
        )
    }

    val futureResult = Future(sqsEndpoint.client.sendMessage(request))
    futureResult.onComplete {
      case Success(res) => callback.onSuccess(request, res)
      case Failure(ex: Exception) => callback.onError(ex)
    }
    futureResult
  }

  private def withAttribute(request: SendMessageRequest, attributeName: String, attributeValue: String): SendMessageRequest = {
    val messageAttributeValue = new MessageAttributeValue()
    messageAttributeValue.setDataType("String")
    messageAttributeValue.setStringValue(attributeValue)
    request.addMessageAttributesEntry(attributeName, messageAttributeValue)
  }
}
