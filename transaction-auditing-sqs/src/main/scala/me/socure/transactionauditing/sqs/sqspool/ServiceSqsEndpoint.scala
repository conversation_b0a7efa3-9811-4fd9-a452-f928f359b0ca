package me.socure.transactionauditing.sqs.sqspool

import akka.stream.alpakka.sqs.{MessageAttributeName, SqsSourceSettings}
import com.amazon.sqs.javamessaging.AmazonSQSExtendedClient
import me.socure.transactionauditing.common.model.SqsQueue
import me.socure.transactionauditing.common.util.CustomMessageAttributes._
import me.socure.transactionauditing.common.util.SqsQueueEnums
import me.socure.transactionauditing.common.util.SqsQueueEnums.SqsQueueEnum
import me.socure.transactionauditing.sqs.config.{ServiceSqsEndpointConfig, ServiceSqsQueueConfig}
import me.socure.transactionauditing.sqs.queue.AkkaSqsConsumerQueue

@deprecated
class ServiceSqsEndpoint(val client: AmazonSQSExtendedClient, endpointConfig: ServiceSqsEndpointConfig, queueType: SqsQueueEnum) {

  val queue: SqsQueue = getQueueUrl(endpointConfig.transactionQueue.queueName)

  val queueSourceSettings: SqsSourceSettings = SqsSourceSettings(
    getQueue(queueType).waitTimeSeconds,
    getQueue(queueType).maxBufferSize,
    getQueue(queueType).maxBatchSize,
    messageAttributeNames = Seq(
      MessageAttributeName(TRANSACTION_ID.name),
      MessageAttributeName(SERVICE_ID.name),
      MessageAttributeName(AkkaSqsConsumerQueue.S3_Not_Found)
    )
  )

  private def getQueueUrl(queueName: String): SqsQueue = {
    val queueUrl = client.getQueueUrl(getQueue(queueType).queueName).getQueueUrl
    SqsQueue(queueUrl)
  }

  private def getQueue(queueType: SqsQueueEnum): ServiceSqsQueueConfig = {
    queueType match {
      case SqsQueueEnums.TRANSACTION => endpointConfig.transactionQueue
      case SqsQueueEnums.THIRD_PARTY => endpointConfig.thirdPartyQueue
      case _ => throw new Exception("Unknown queue type specified")
    }
  }
}
