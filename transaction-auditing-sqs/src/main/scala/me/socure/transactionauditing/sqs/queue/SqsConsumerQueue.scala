package me.socure.transactionauditing.sqs.queue

import me.socure.transactionauditing.common.util.SqsQueueEnums.SqsQueueEnum
import com.amazonaws.services.sqs.model.Message

import scala.concurrent.Future
import me.socure.transactionauditing.common.status.ProcessResults.ProcessResult

@deprecated
trait SqsConsumerQueue extends Queue {
  /**
    * The consumer function that handles a incoming message from the queue.
    * @param message The incoming message
    * @return A future with the result of this process
    */
  def consume(message: Message): ProcessResult

  def startStream(msgType: SqsQueueEnum, handleFailure: (Throwable) => Unit, streamFailure: (Throwable) => Unit): Future[Any]
}
