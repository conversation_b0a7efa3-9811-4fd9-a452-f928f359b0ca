package me.socure.transactionauditing.sqs.model

import me.socure.transactionauditing.common.domain.ThirdPartyTransaction
import org.joda.time.DateTime

case class SqsThirdPartyTransaction(id: Option[Long],
                                    accountId: Option[Long],
                                    isCache: Boolean,
                                    processingTime: Option[Long],
                                    serviceId: Int,
                                    startTime: Option[DateTime],
                                    transactionId: String,
                                    request: Option[String],
                                    response: Option[String],
                                    request_body: Option[String],
                                    uuid: Option[String],
                                    isError: Option[Boolean],
                                    callInstanceId: Option[String] = None) extends SqsMessage

object SqsThirdPartyTransaction {
  val NoId = 0
  val NoServiceId: Int = -1

  def apply(thirdPartyTransactionAuditing: ThirdPartyTransaction): SqsThirdPartyTransaction = {
    SqsThirdPartyTransaction(
      id = if(thirdPartyTransactionAuditing.id == NoId) None else Some(thirdPartyTransactionAuditing.id),
      transactionId = thirdPartyTransactionAuditing.transactionId,
      accountId = thirdPartyTransactionAuditing.accountId,
      isCache = thirdPartyTransactionAuditing.isCache.getOrElse(false),
      processingTime = thirdPartyTransactionAuditing.processingTime,
      serviceId = thirdPartyTransactionAuditing.serviceId.getOrElse(NoServiceId),
      startTime = thirdPartyTransactionAuditing.startTime,
      request = thirdPartyTransactionAuditing.request,
      response = thirdPartyTransactionAuditing.response,
      request_body = thirdPartyTransactionAuditing.request_body,
      uuid = thirdPartyTransactionAuditing.uuid,
      isError = thirdPartyTransactionAuditing.isError
    )
  }
}