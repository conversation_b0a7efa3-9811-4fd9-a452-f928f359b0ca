package me.socure.transactionauditing.sqs.factory

import com.amazonaws.services.s3.AmazonS3Client
import com.amazonaws.services.sqs.AmazonSQSClientBuilder
import me.socure.s3.ExtendedSQSClientRegionFactory
import me.socure.transactionauditing.common.util.SqsQueueEnums.SqsQueueEnum
import me.socure.transactionauditing.sqs.config.ServiceSqsEndpointConfig
import me.socure.transactionauditing.sqs.sqspool.ServiceSqsEndpoint

@deprecated
object ServiceSqsEndpointFactory {

  def get(s3Client: AmazonS3Client,  s3Bucket: String, baseSqsBuilder: AmazonSQSClientBuilder, endpointConfig: ServiceSqsEndpointConfig, queueType: SqsQueueEnum): ServiceSqsEndpoint = {
    val sqsClient = ExtendedSQSClientRegionFactory.get(
      s3Client = s3Client,
      s3BucketName = s3Bucket,
      amazonSQSClientBuilder = baseSqsBuilder,
      region = endpointConfig.region
    )
    new ServiceSqsEndpoint(client = sqsClient, endpointConfig = endpointConfig, queueType = queueType)
  }
}
