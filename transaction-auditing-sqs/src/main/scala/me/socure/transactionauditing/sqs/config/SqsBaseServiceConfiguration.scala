package me.socure.transactionauditing.sqs.config

import com.typesafe.config.Config
import me.socure.transactionauditing.common.util.SqsQueueEnums
import me.socure.transactionauditing.common.util.SqsQueueEnums.SqsQueueEnum

@deprecated
class SqsBaseServiceConfiguration(config: Config) {

  val endpoints = Seq(
    loadSqsEndpoint()
  )

  val serverPort: Int = config.getInt("transaction-auditing.server.port")

  val transactionParallelism: Int = config.getInt("transaction-auditing.aws.sqs.transaction.parallelism")
  val thirdPartyParallelism: Int = config.getInt("transaction-auditing.aws.sqs.third-party.parallelism")

  private def readQueueConfig(queueType: SqsQueueEnum): ServiceSqsQueueConfig = {
    val queueName = queueType.name
    val transactionQueueName = config.getString("transaction-auditing.aws.sqs.%s.queueName".format(queueName))
    val transactionWaitTimeSeconds = config.getInt("transaction-auditing.aws.sqs.%s.waitTimeSeconds".format(queueName))

    val transactionMaxBatchSize = config.getInt("transaction-auditing.aws.sqs.%s.maxBatchSize".format(queueName))
    val transactionMaxBufferSize = config.getInt("transaction-auditing.aws.sqs.%s.maxBufferSize".format(queueName))
    ServiceSqsQueueConfig(queueName = transactionQueueName,
      waitTimeSeconds = transactionWaitTimeSeconds,
      maxBatchSize = transactionMaxBatchSize,
      maxBufferSize = transactionMaxBufferSize
    )
  }

  private def loadSqsEndpoint(): ServiceSqsEndpointConfig = {
    val transactionQueue = readQueueConfig(SqsQueueEnums.TRANSACTION)
    val thirdPartyQueue = readQueueConfig(SqsQueueEnums.THIRD_PARTY)

    ServiceSqsEndpointConfig(
      config.getString("transaction-auditing.aws.sqs.region"),
      transactionQueue,
      thirdPartyQueue
    )
  }
}
