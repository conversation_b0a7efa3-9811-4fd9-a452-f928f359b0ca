package me.socure.transactionauditing.sqs.queue

import akka.actor.ActorSystem
import akka.stream.ActorMaterializer
import me.socure.transactionauditing.common.util.ProducerTypes
import me.socure.transactionauditing.common.util.ProducerTypes.ProducerType

@deprecated
object ProducerQueueFactory {
  def get(producerType: ProducerType)(implicit system: ActorSystem, materializer: ActorMaterializer): SqsProducerQueue = {
    producerType match {
      case ProducerTypes.AKKA => {
        new AkkaSqsProducerQueue()
      }
      case ProducerTypes.ASYNC => {
        new AsyncSqsProducerQueue()
      }
    }
  }

  def getAsync(): SqsProducerQueue = {
    new AsyncSqsProducerQueue()
  }

  def getAkka()(implicit system: ActorSystem, materializer: ActorMaterializer): SqsProducerQueue = {
    new AkkaSqsProducerQueue()
  }
}
