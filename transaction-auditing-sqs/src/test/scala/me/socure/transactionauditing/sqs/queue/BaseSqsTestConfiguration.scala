package me.socure.transactionauditing.sqs.queue

import akka.actor.ActorSystem
import akka.stream.ActorMaterializer
import com.amazon.sqs.javamessaging.{AmazonSQSExtendedClient, ExtendedClientConfiguration}
import com.amazonaws.auth.{AWSCredentialsProvider, AWSStaticCredentialsProvider, AnonymousAWSCredentials}
import com.amazonaws.client.builder.AwsClientBuilder.EndpointConfiguration
import com.amazonaws.handlers.AsyncHandler
import com.amazonaws.services.s3.AmazonS3Client
import com.amazonaws.services.sqs.model.{SendMessageRequest, SendMessageResult}
import com.amazonaws.services.sqs.{AmazonSQSAsync, AmazonSQSAsyncClientBuilder, AmazonSQSClientBuilder}
import me.socure.common.docker.client.factory.DefaultDockerClientFactory
import me.socure.common.docker.sqsservice.SQSServiceFactory
import me.socure.sqs.SqsServerAwait
import org.json4s.DefaultFormats
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, DoNotDiscover, FunSuite, Matchers}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

@deprecated
@DoNotDiscover
class BaseSqsTestConfiguration extends FunSuite with MockitoSugar with Matchers with ScalaFutures with BeforeAndAfterAll {
  implicit val formats: DefaultFormats.type = DefaultFormats
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  protected val sqsDockerClient = new DefaultDockerClientFactory().get()
  protected val sqsServiceFactory = new SQSServiceFactory(sqsDockerClient)
  protected val sqsService = sqsServiceFactory.get()

  implicit val system: ActorSystem = ActorSystem()
  implicit val mat: ActorMaterializer = ActorMaterializer()
  implicit val patience: PatienceConfig = PatienceConfig(timeout = Span(10, Seconds), interval = Span(5, Seconds))

  val credentialsProvider = new AWSStaticCredentialsProvider(new AnonymousAWSCredentials)

  def createExtendedClient(sqsEndpoint: String, credentialsProvider: AWSCredentialsProvider): AmazonSQSExtendedClient = {

    val sqsClientBuilder: AmazonSQSClientBuilder = AmazonSQSClientBuilder
      .standard()
      .withCredentials(credentialsProvider)
      .withEndpointConfiguration(new EndpointConfiguration(sqsEndpoint, "us-east-1"))

    val  extendedClientConfiguration = new ExtendedClientConfiguration()
      .withLargePayloadSupportEnabled(mock[AmazonS3Client], "test-bucket")
      .withAlwaysThroughS3(false)

    val sqsClient = sqsClientBuilder.build()
    val client = new AmazonSQSExtendedClient(sqsClient, extendedClientConfiguration)

    SqsServerAwait.await(client, 1000, 10)
    client
  }

  def getEndpoint = s"http://${sqsDockerClient.getHost}:${sqsService.getPortMapping(sqsServiceFactory.port)}"

  def handleResponse(result: Any): Unit = {
    println("Sent data to sink processor")
  }

  val asyncHandler =  new AsyncHandler[SendMessageRequest, SendMessageResult] {
    override def onError(exception: Exception): Unit = {
    }

    override def onSuccess(request: SendMessageRequest, result: SendMessageResult): Unit = {
    }
  }

  def asyncResponseCallback: AsyncHandler[SendMessageRequest, SendMessageResult] = {
    new AsyncHandler[SendMessageRequest, SendMessageResult] {
      override def onError(exception: Exception): Unit = {
        println("aws request failed")
      }

      override def onSuccess(request: SendMessageRequest, result: SendMessageResult): Unit = {
        println("aws response succeeded")
      }
    }
  }

  override def beforeAll {
    sqsService.startUp()
  }

  override def afterAll {
    sqsService.shutDown()
  }
}
