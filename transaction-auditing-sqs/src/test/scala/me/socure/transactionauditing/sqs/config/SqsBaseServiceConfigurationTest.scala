package me.socure.transactionauditing.sqs.config

import com.typesafe.config.{Config, ConfigFactory}
import org.scalatest.{<PERSON>NotDiscover, FunSuite, Matchers}

@deprecated
@DoNotDiscover
class SqsBaseServiceConfigurationTest extends FunSuite with Matchers {

  val config: Config = ConfigFactory.load("test.conf")

  test("load the config settings") {
    val settings = new SqsBaseServiceConfiguration(config)
    settings.endpoints.map(_.region) should contain  ("us-east-1")
    settings.endpoints.map(_.region) should not contain ("us-west-1")
    settings.endpoints.map(_.region) should not contain  ("us-east-2")
    settings.endpoints.head.transactionQueue.queueName shouldBe "transaction-auditing-stage"
    settings.endpoints.head.thirdPartyQueue.queueName shouldBe "third-party-transaction-auditing-stage"
    settings.serverPort shouldBe 5000
    settings.transactionParallelism shouldBe 30
    settings.thirdPartyParallelism shouldBe 60
  }

}
