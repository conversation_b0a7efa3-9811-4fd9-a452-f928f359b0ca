package me.socure.transactionauditing.sqs.marshaller


import me.socure.transactionauditing.common.util.SqsQueueEnums
import me.socure.transactionauditing.sqs.model._
import org.scalatest.{FunSuite, Matchers}

class SqsMessageMarshallerTest extends FunSuite with Matchers {

  test("serialize an api transaction with no id") {
    val transaction = SqsAPITransactionValueGenerator.aAPITransaction()
    val json = SqsMessageMarshaller.marshal(transaction)
    SqsMessageMarshaller.unmarshalApiTransaction(json) match {
      case t: SqsApiTransaction => {
        t.transactionId shouldBe transaction.transactionId
        t.requestURI shouldBe transaction.requestURI
        t.transactionDate shouldBe transaction.transactionDate
        t.authScore shouldBe transaction.authScore
        t.error shouldBe transaction.error
        t.response shouldBe transaction.response
        t.accountId shouldBe transaction.accountId
        t.debug shouldBe transaction.debug
        t.environmentType shouldBe transaction.environmentType
        t.runId shouldBe transaction.runId
        t.apiKey shouldBe transaction.apiKey
        t.ofac shouldBe transaction.ofac
      }
    }
  }

  test("serialize an api transaction with an id") {
    val transaction = SqsAPITransactionValueGenerator.aAPITransaction(id = Some(123))
    val json = SqsMessageMarshaller.marshal(transaction)
    SqsMessageMarshaller.unmarshalApiTransaction(json) match {
      case t: SqsApiTransaction => {
        t.accountId shouldBe transaction.accountId
      }
    }
  }

  test("serialize an api transaction with a runId") {
    val transaction = SqsAPITransactionValueGenerator.aAPITransaction(id = Some(123), runId = Some("testrun"))
    val json = SqsMessageMarshaller.marshal(transaction)
    SqsMessageMarshaller.unmarshalApiTransaction(json) match {
      case t: SqsApiTransaction => {
        t.runId shouldBe transaction.runId
      }
    }
  }

  test("unmarshall an api transaction") {
    val transaction = SqsAPITransactionValueGenerator.aAPITransaction(id = Some(123), runId = Some("testrun"))
    val json = SqsMessageMarshaller.marshal(transaction)
    val unmarshalledTransaction = SqsMessageMarshaller.unmarshalApiTransaction(json)
    val isApiTransaction = unmarshalledTransaction match {
      case t: SqsApiTransaction => true
      case _ => false
    }
    isApiTransaction shouldBe true
  }
}
