package me.socure.transactionauditing.sqs.sqspool

import com.amazon.sqs.javamessaging.AmazonSQSExtendedClient
import com.amazonaws.services.sqs.AmazonSQS
import com.amazonaws.services.sqs.model.GetQueueUrlResult
import com.typesafe.config.ConfigFactory
import me.socure.transactionauditing.common.util.SqsQueueEnums
import me.socure.transactionauditing.sqs.config.SqsBaseServiceConfiguration
import org.mockito.Mockito
import org.scalatest.mock.MockitoSugar
import org.scalatest.{FunSuite, Matchers}

class ServiceSqsEndpointTest extends FunSuite with Matchers with MockitoSugar {

  private val config = ConfigFactory.load("test.conf")
  private val awsConfiguration = new SqsBaseServiceConfiguration(config)

  test("successfully load up basic source config information") {
    awsConfiguration.endpoints.head.region shouldBe "us-east-1"
    awsConfiguration.endpoints.length shouldBe 1
  }

  test("Test SqsEndpoint creation") {

    val transactionQueue = awsConfiguration.endpoints.head.transactionQueue
    val queueUrl = "trx_queue_url"
    val baseSqsClient = mock[AmazonSQS]
    val sqsClient = new AmazonSQSExtendedClient(baseSqsClient)

    val queueUrlResult = mock[GetQueueUrlResult]
    Mockito.when(queueUrlResult.getQueueUrl).thenReturn(queueUrl)
    Mockito.when(baseSqsClient.getQueueUrl(transactionQueue.queueName)).thenReturn(queueUrlResult)

    val endpoint = new ServiceSqsEndpoint(sqsClient, awsConfiguration.endpoints.head, SqsQueueEnums.TRANSACTION)
    endpoint.queueSourceSettings.maxBatchSize shouldBe 10
    endpoint.queueSourceSettings.maxBufferSize shouldBe 60
    endpoint.queueSourceSettings.waitTimeSeconds shouldBe 20

    Mockito.verify(queueUrlResult).getQueueUrl
    Mockito.verify(baseSqsClient).getQueueUrl(transactionQueue.queueName)
  }
}
