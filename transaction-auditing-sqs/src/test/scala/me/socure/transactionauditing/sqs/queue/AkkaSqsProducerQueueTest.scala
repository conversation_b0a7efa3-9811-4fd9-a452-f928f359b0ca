package me.socure.transactionauditing.sqs.queue

import akka.stream.alpakka.sqs.SqsSinkSettings
import me.socure.sqs.SqsServerAwait
import me.socure.transactionauditing.common.config.ClientSqsEndpoint
import me.socure.transactionauditing.common.model.SqsQueue
import me.socure.transactionauditing.sqs.model.{SqsAPITransactionValueGenerator, SqsApiTransaction}
import org.mockito.Mockito
import org.scalatest.DoNotDiscover
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Seconds, Span}

@deprecated
@DoNotDiscover
class AkkaSqsProducerQueueTest extends BaseSqsTestConfiguration with MockitoSugar {

  implicit override val patience: PatienceConfig = PatienceConfig(timeout = Span(60, Seconds), interval = Span(30, Seconds))

  test("Sqs Producer should produce a single message") {
    val sqsClient = createExtendedClient(getEndpoint, credentialsProvider)
    SqsServerAwait.await(sqsClient, 1000, 10)
    val createQueueRequest = sqsClient.createQueue("Test-Queue")
    val sqsEndpoint = mock[ClientSqsEndpoint]
    Mockito.when(sqsEndpoint.client).thenReturn(sqsClient)
    Mockito.when(sqsEndpoint.queue).thenReturn(SqsQueue(createQueueRequest.getQueueUrl))
    Mockito.when(sqsEndpoint.queueSinkSettings).thenReturn(SqsSinkSettings(
      5
    ))
    val sqsProducer = new AkkaSqsProducerQueue()
    val sqsMessage = SqsAPITransactionValueGenerator.aAPITransaction()
    whenReady(sqsProducer.send(sqsMessage, sqsEndpoint,  asyncHandler)) { res =>
      println(res)
      res should not be null
    }
  }

  test("Sqs Producer should fail when we try to pass in null") {
    println("Connecting to : "  + getEndpoint)
    val sqsClient = createExtendedClient(getEndpoint, credentialsProvider)
    SqsServerAwait.await(sqsClient, 1000, 10)
    val createQueueRequest = sqsClient.createQueue("Test-Queue")
    val sqsEndpoint = mock[ClientSqsEndpoint]
    Mockito.when(sqsEndpoint.client).thenReturn(sqsClient)
    Mockito.when(sqsEndpoint.queue).thenReturn(SqsQueue(createQueueRequest.getQueueUrl))
    Mockito.when(sqsEndpoint.queueSinkSettings).thenReturn(SqsSinkSettings(
      5
    ))
    val sqsProducer = new AkkaSqsProducerQueue()
    val sqsMessage: SqsApiTransaction = null
    whenReady(sqsProducer.send(sqsMessage, sqsEndpoint, asyncHandler).failed) { res =>
      res shouldBe a [IllegalArgumentException]
    }
  }
}
