package me.socure.transactionauditing.sqs.marshaller

import me.socure.common.random.Random
import me.socure.transactionauditing.sqs.model.SqsAPITransactionValueGenerator
import org.scalatest.{DoNotDiscover, FunSuite, Matchers}

@deprecated
@DoNotDiscover
class DataTruncatorTest extends FunSuite with Matchers {

  test("should do nothing") {
    val transaction = SqsAPITransactionValueGenerator.aAPITransaction(
      originOfInvocation = Some("test")
    )
    val truncated = DataTruncator.truncate(transaction)
    truncated shouldBe transaction
  }

  test("should handle null API key") {
    val transaction = SqsAPITransactionValueGenerator.aAPITransaction(
      originOfInvocation = Some("test"),
      apiKey = null
    )
    val truncated = DataTruncator.truncate(transaction)
    truncated shouldBe transaction
  }

  test("should truncate") {
    val transaction = SqsAPITransactionValueGenerator.aAPITransaction(
      originOfInvocation = Some(Random.alphaNumeric(1000)),
      runId = Some(Random.alphaNumeric(1000)),
      apiKey = Random.alphaNumeric(1000),
      customerUserId = Some(Random.alphaNumeric(10000)),
      geocode = Some(Random.alphaNumeric(1000))
    )

    val expected = transaction.copy(
      originOfInvocation = transaction.originOfInvocation.map(_.substring(0,255)),
      runId = transaction.runId.map(_.substring(0, 255)),
      apiKey = transaction.apiKey.substring(0, 255),
      customerUserId = transaction.customerUserId.map(_.substring(0, 255)),
      geocode = transaction.geocode.map(_.substring(0, 255))
    )
    val truncated = DataTruncator.truncate(transaction)
    truncated shouldBe expected
  }
}
