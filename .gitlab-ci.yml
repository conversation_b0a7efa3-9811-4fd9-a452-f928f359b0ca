include:
  - project: 'plt/gitlab-pipeline-templates'
    file: socure-services.gitlab-ci.yml

variables:
  PROJECT_NAME: idf
  SERVICE_NAME: "transaction-auditing-service, transaction-auditing-worker"
  SVC_NAME: transaction-auditing-service
  SVC_WORKER: transaction-auditing-worker
  SERVICE_VERSION: 0.1.0
  MAVEN_EXTRA_ARGS: -Dbuild-environment=gitlab-ci -DskipTests=true
  MAVEN_FIPS_ARGS: -Dbuild-environment=gitlab-ci-fips -DskipTests=true
  DEV_ENVIRONMENT: "yes"
  MEND_PRODUCT_NAME: socure-saas
  APP_MSCV_MULTI_DIR: "true"
  GOV_STAGE_MSCV_UPDATE: "true"