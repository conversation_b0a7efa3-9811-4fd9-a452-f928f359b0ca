package me.socure.transactionauditing.writer.service.engine

import me.socure.common.sqs.v2.extended.ExtendedSqsAsyncClient
import me.socure.common.sqs.v2.{MessagesProcessor, SqsMessagesDeleter}
import me.socure.transaction.audit.common.model.CompositeStoppableServices
import me.socure.transactionauditing.writer.service.StoppablePipelineServiceWithPeriodicRestart
import me.socure.transactionauditing.writer.service.provider.StoppablePipelineServiceProvider
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.{FunSuite, Matchers}
import software.amazon.awssdk.services.sqs.model.{Message, ReceiveMessageRequest}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class StoppablePipelineServiceProviderTest extends FunSuite
  with Matchers with MockitoSugar with ScalaFutures {

  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  private val sqsClient = mock[ExtendedSqsAsyncClient]
  private val messageProcessor = mock[MessagesProcessor[Message]]
  private val sqsDeleter = mock[SqsMessagesDeleter]

  private val request = ReceiveMessageRequest
    .builder()
    .queueUrl("https://dummyurl.com")
    .maxNumberOfMessages(10)
    .build()


  test("when parallelism = 1 should return StoppablePipelineService") {
    val serviceProvider = new StoppablePipelineServiceProvider(
      parallelism = 1,
      pipelineRestartIntervalInMs = 2000L,
      client = sqsClient,
      batchConfOpt = None,
      receiveMessageRequest = request,
      messageProcessor = messageProcessor,
      sqsMessageDeleter = sqsDeleter
    )

    serviceProvider.get() shouldBe a[StoppablePipelineServiceWithPeriodicRestart]
  }

  test("when parallelism > 1 should return CompositeStoppableServices") {
    val serviceProvider = new StoppablePipelineServiceProvider(
      parallelism = 2,
      pipelineRestartIntervalInMs = 2000L,
      client = sqsClient,
      batchConfOpt = None,
      receiveMessageRequest = request,
      messageProcessor = messageProcessor,
      sqsMessageDeleter = sqsDeleter
    )

    serviceProvider.get() shouldBe a[CompositeStoppableServices]
  }

}
