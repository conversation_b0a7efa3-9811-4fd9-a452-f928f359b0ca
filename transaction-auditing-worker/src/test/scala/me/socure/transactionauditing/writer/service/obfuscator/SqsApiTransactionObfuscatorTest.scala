package me.socure.transactionauditing.writer.service.obfuscator

import me.socure.account.client.superadmin.AccountInfoClient
import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import me.socure.model.encryption.ApiKeyString
import me.socure.transactionauditing.sqs.model.SqsAPITransactionValueGenerator
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.{FunSuite, Matchers}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

class SqsApiTransactionObfuscatorTest extends FunSuite with Matchers with MockitoSugar with ScalaFutures {

  implicit val ec = ExecutionContext.global

  val accountInfoClient: AccountInfoClient = mock[AccountInfoClient]

  val obfuscator = PiiObfuscatorFactory.createTransactionObfuscator(Seq(1), accountInfoClient)

  test("should obfuscate input when account is marked") {
    val trx = SqsAPITransactionValueGenerator.aAPITransaction().copy(
      parameters = """{"nationalid":"abc"}""",
      debug = "{}",
      response = "{}",
      accountId = 1
    )

    Mockito.when(accountInfoClient.hasRole(ApiKeyString(trx.apiKey), BusinessUserRoles.MASK_PII.id))
      .thenReturn(Future.successful(Right(false)))

    val ret = obfuscator.mask(trx)
    ret.parameters shouldBe """{"nationalid":"[PROVIDED]"}"""
  }

  test("should obfuscate output parameter when account is marked") {
    val trx = SqsAPITransactionValueGenerator.aAPITransaction().copy(
      parameters = "{}",
      debug = "{}",
      response = """{"name_address":"somevalue"}""",
      accountId = 1
    )

    Mockito.when(accountInfoClient.hasRole(ApiKeyString(trx.apiKey), BusinessUserRoles.MASK_PII.id))
      .thenReturn(Future.successful(Right(false)))

    val ret = obfuscator.mask(trx)
    ret.response shouldBe """{"name_address":"[PROVIDED]"}"""
  }

  test("should not obfuscate when account is not marked") {
    val trx = SqsAPITransactionValueGenerator.aAPITransaction().copy(
      parameters = """{"nationalid":"abcd"}""",
      debug = "{}",
      response = "{}",
      accountId = 2
    )

    Mockito.when(accountInfoClient.hasRole(ApiKeyString(trx.apiKey), BusinessUserRoles.MASK_PII.id))
      .thenReturn(Future.successful(Right(false)))

    val ret = obfuscator.mask(trx)
    ret.parameters shouldBe """{"nationalid":"abcd"}"""
  }

  test("should obfuscate input when account is marked with maskPii turned on") {
    val trx = SqsAPITransactionValueGenerator.aAPITransaction().copy(
      parameters = """{"nationalid":"abc"}""",
      debug = "{}",
      response = "{}",
      accountId = 2
    )

    Mockito.when(accountInfoClient.hasRole(ApiKeyString(trx.apiKey), BusinessUserRoles.MASK_PII.id))
      .thenReturn(Future.successful(Right(true)))

    val ret = obfuscator.mask(trx)
    ret.parameters shouldBe """{"nationalid":"[PROVIDED]"}"""
  }

  test("should obfuscate output parameter when account is marked with maskPii turned on") {
    val trx = SqsAPITransactionValueGenerator.aAPITransaction().copy(
      parameters = "{}",
      debug = "{}",
      response = """{"name_address":"somevalue"}""",
      accountId = 2
    )

    Mockito.when(accountInfoClient.hasRole(ApiKeyString(trx.apiKey), BusinessUserRoles.MASK_PII.id))
      .thenReturn(Future.successful(Right(true)))

    val ret = obfuscator.mask(trx)
    ret.response shouldBe """{"name_address":"[PROVIDED]"}"""
  }

  test("should obfuscate output parameter when account is marked with maskPii turned on failure 1") {
    val trx = SqsAPITransactionValueGenerator.aAPITransaction().copy(
      parameters = "{}",
      debug = "{}",
      response = """{"name_address":"somevalue"}""",
      accountId = 2
    )

    Mockito.when(accountInfoClient.hasRole(ApiKeyString(trx.apiKey), BusinessUserRoles.MASK_PII.id))
      .thenReturn(Future.failed(new RuntimeException("dummy")))

    val ret = Try(obfuscator.mask(trx))
    ret.isSuccess shouldBe false
  }

  test("should obfuscate output parameter when account is marked with maskPii turned on failure 2") {
    val trx = SqsAPITransactionValueGenerator.aAPITransaction().copy(
      parameters = "{}",
      debug = "{}",
      response = """{"name_address":"somevalue"}""",
      accountId = 2
    )

    Mockito.when(accountInfoClient.hasRole(ApiKeyString(trx.apiKey), BusinessUserRoles.MASK_PII.id))
      .thenReturn(Future.successful(Left(ErrorResponse(111, "dummy"))))

    val ret = Try(obfuscator.mask(trx))
    ret.isSuccess shouldBe false
  }

  test("should obfuscate output parameter when account is marked with maskPii turned on account not found case") {
    val trx = SqsAPITransactionValueGenerator.aAPITransaction().copy(
      parameters = "{}",
      debug = "{}",
      response = """{"name_address":"somevalue"}""",
      accountId = 2
    )

    Mockito.when(accountInfoClient.hasRole(ApiKeyString(trx.apiKey), BusinessUserRoles.MASK_PII.id))
      .thenReturn(Future.successful(Left(ErrorResponse(ExceptionCodes.AccountNotFound.id, "dummy"))))

    val ret = obfuscator.mask(trx)
    ret.response shouldBe """{"name_address":"somevalue"}"""
  }

}
