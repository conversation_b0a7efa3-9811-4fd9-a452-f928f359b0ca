package me.socure.transactionauditing.writer.service

import com.google.common.util.concurrent.AbstractService
import javax.inject.Inject
import me.socure.transaction.audit.common.model.StoppableService
import org.slf4j.LoggerFactory

import scala.concurrent.ExecutionContext
import scala.util.{Failure, Success}

class PipelineService @Inject()(pipeline: StoppableService)(implicit ec: ExecutionContext) extends AbstractService {

  private val logger = LoggerFactory.getLogger(getClass)

  override def doStart(): Unit = {
    try {
      val future = pipeline.start()
      future.onComplete {
        case Failure(ex) =>
          logger.error("Pipeline stopped unexpectedly with an error", ex)
          notifyFailed(ex)
        case _ =>
      }
      notifyStarted()
    } catch {
      case e: Throwable =>
        logger.error(s"Error while running pipeline ", e)
        notifyFailed(e)
    }
  }

  override def doStop(): Unit = {
    pipeline.stop()
  }
}
