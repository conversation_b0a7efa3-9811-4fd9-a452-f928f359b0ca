package me.socure.transactionauditing.writer.service

import javax.sql.DataSource
import org.flywaydb.core.Flyway
import org.slf4j.LoggerFactory

class DBMigrator (
                   auditDataSource: DataSource,
                   migrate: Boolean
                ) {

  private val logger = LoggerFactory.getLogger(getClass)

  def migrate: Unit = {
    if (migrate) {
      val flyway = new Flyway()
      logger.info("Applying SocureAudit DB Migration scripts")
      flyway.setDataSource(auditDataSource)
      flyway.setLocations("classpath:/db/migration/socureaudit")
      flyway.setBaselineOnMigrate(true)
      flyway.migrate()
      logger.info(s"Applied SocureAudit DB Migrations")
    } else {
      logger.info(s"DB Migration was not chosen")
    }

  }

}
