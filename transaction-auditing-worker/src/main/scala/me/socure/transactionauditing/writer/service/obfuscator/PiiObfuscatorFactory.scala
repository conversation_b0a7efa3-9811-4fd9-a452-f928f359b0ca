package me.socure.transactionauditing.writer.service.obfuscator

import com.typesafe.config.ConfigFactory
import me.socure.account.client.superadmin.{AccountInfoClient, AccountInfoClientFactory}
import net.spy.memcached.MemcachedClient

import java.net.InetSocketAddress
import scala.collection.JavaConverters._
import scala.concurrent.ExecutionContext
import scala.concurrent.duration.DurationInt

object PiiObfuscatorFactory {

  private val config = ConfigFactory.parseResources("obfuscator.conf")

  def createTransactionObfuscator(
                                   accounts: Seq[Long],
                                   memcachedHost: String,
                                   memcachedPort: Int,
                                   accountServiceEndpoint: String
                                 )(implicit ec: ExecutionContext): SqsApiTransactionObfuscator = {
    val memcachedClient = new MemcachedClient(new InetSocketAddress(memcachedHost, memcachedPort))

    val accountInfoClient: AccountInfoClient = AccountInfoClientFactory.createCached(
      accountServiceEndpoint = accountServiceEndpoint,
      memcachedClient = memcachedClient,
      ttl = Some(60.minutes)
    )

    createTransactionObfuscator(accounts, accountInfoClient)
  }

  def createTransactionObfuscator(
                                   accounts: Seq[Long],
                                   accountInfoClient: AccountInfoClient
                                 )(implicit ec: ExecutionContext): SqsApiTransactionObfuscator = {
    val inputParameters = config.getStringList("pii.transaction.inputInfo").asScala
    val outputParameters = config.getStringList("pii.transaction.processedInfo").asScala
    new SqsApiTransactionObfuscator(accounts, inputParameters, outputParameters, accountInfoClient)
  }

}
