package me.socure.transactionauditing.writer.service.module

import java.nio.file.Paths
import java.util.concurrent.TimeUnit

import com.google.inject.{AbstractModule, Provides}
import com.google.inject.name.Names
import com.typesafe.config.Config
import javax.inject.{Named, Singleton}
import me.socure.common.metrics.JavaMetricsFactory
import me.socure.common.sqs.sns.v2.extended.common.{Configuration, Constants, S3Wrapper}
import me.socure.common.sqs.v2.SqsMessagesDeleter
import me.socure.common.sqs.v2.extended.ExtendedSqsAsyncClient
import me.socure.common.uuid.RandomUuidProvider
import me.socure.transaction.audit.common.model.StoppableService
import me.socure.transactionauditing.writer.service.PipelineService
import me.socure.transactionauditing.writer.service.provider.StoppableBYOKFailurePipelineProvider
import me.socure.types.scala.batch.BatchConf
import net.codingwell.scalaguice.ScalaModule
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.s3.S3AsyncClient
import software.amazon.awssdk.services.sqs.SqsAsyncClient
import software.amazon.awssdk.services.sqs.model.{GetQueueUrlRequest, ReceiveMessageRequest}

import scala.concurrent.ExecutionContext
import scala.concurrent.duration.Duration

class StoppableBYOKFailurePipelineModule extends AbstractModule with ScalaModule {

  override def configure(): Unit = {
    bind(classOf[StoppableService])
      .annotatedWith(Names.named("byokFailure"))
      .toProvider(classOf[StoppableBYOKFailurePipelineProvider]).asEagerSingleton()
  }

  @Provides
  @Singleton
  @Named("byokFailurePipeline")
  def getTransactionPipeline(@Named("byokFailure") pipeline: StoppableService)(implicit ec: ExecutionContext): PipelineService = {
    new PipelineService(pipeline)
  }

  @Provides
  @Named("byokFailureConsumerCount")
  def getParallelConsumers(config: Config): Int = {
    if (config.hasPath("pipeline.byok-failure.parallelism"))
      config.getInt("pipeline.byok-failure.parallelism")
    else
      config.getInt("pipeline.parallelism")
  }

  @Provides
  @Singleton
  @Named("byokFailureExtendedSqsClient")
  def getExtendedSqsAsyncClient(@Named("byokFailureBaseSqsClient") baseClient: SqsAsyncClient,
                                @Named("byokFailureS3Wrapper") s3Client: S3Wrapper,
                                @Named("byokFailureSqsConfig") extendedSqsConf: Configuration,
                                @Named("byokFailureIgnoreS3NoSuchKey") ignoreS3NoSuchKey: Boolean): ExtendedSqsAsyncClient = {
    new ExtendedSqsAsyncClient(baseClient, s3Client, extendedSqsConf, ignoreS3NoSuchKey, uuidProvider = RandomUuidProvider)
  }

  @Provides
  @Named("byokFailureSqsBatchConfig")
  def getTransactionBatchConf(config: Config): Option[BatchConf] = {
    if (config.hasPath("pipeline.buffering")) {
      Some(BatchConf(
        targetCount = config.getInt("pipeline.buffering.targetCount"),
        maxDuration = Duration(config.getString("pipeline.buffering.maxDuration"))
      ))
    } else None
  }

  @Provides
  @Named("byokFailureRestartIntervalInMs")
  def getTransactionRestartIntervalInMs(config: Config): Long = {
    if (config.hasPath("pipeline.byok-failure.restart.interval")) {
      val restartCfg = config.getString("pipeline.byok-failure.restart.interval")
      Duration(restartCfg).toMillis
    } else {
      Duration("24 hours").toMillis
    }
  }

  @Provides
  @Named("byokFailureReceiveMessageRequest")
  @Singleton
  def getTransactionReceiveMessage(config: Config,
                                   @Named("byokFailureQueueUrl") queueUrl: String,
                                   @Named("byokFailureSqsReceiveMsgCount") receiveMessageCount: Int): ReceiveMessageRequest = {
    ReceiveMessageRequest
      .builder()
      .queueUrl(queueUrl)
      .maxNumberOfMessages(receiveMessageCount)
      .visibilityTimeout(config.getInt("pipeline.byok-failure.sqs.visibility.timeout.seconds"))
      .messageAttributeNames("All")
      .build()
  }

  @Provides
  @Named("byokFailureSqsMsgDeleter")
  @Singleton
  def getTransactionMessageDeleter(
                                    @Named("byokFailureExtendedSqsClient") client: ExtendedSqsAsyncClient,
                                    @Named("byokFailureQueueUrl") queueUrl: String,
                                    @Named("byokFailureSqsDeleteMsgMaxRetry") deleteMaxRetries: Int)
                                  (implicit ec: ExecutionContext): SqsMessagesDeleter = {

    new SqsMessagesDeleter(
      client = client,
      queueUrl = queueUrl,
      maxRetries = deleteMaxRetries,
      metrics = JavaMetricsFactory.get("transaction.auditing.writer.byok-failure.sqs.delete.messages"),
      logger = LoggerFactory.getLogger("transaction.auditing.writer.byok-failure.sqs.delete.messages")
    )
  }

  @Provides
  @Singleton
  @Named("byokFailureBaseSqsClient")
  def getBaseClient: SqsAsyncClient = SqsAsyncClient.create()

  @Provides
  @Singleton
  @Named("byokFailureS3Wrapper")
  def getWrappedS3Client: S3Wrapper = S3Wrapper(S3AsyncClient.create())

  @Provides
  @Named("byokFailureSqsConfig")
  def getExtendedSqsConfig(config: Config): Configuration = {
    val s3Details = config.getConfig("pipeline.byok-failure.sqs")
    Configuration(
      s3BucketName = s3Details.getString("large.files.s3.bucket.name"),
      basePath = if (s3Details.hasPath("base.path")) Option(s3Details.getString("base.path")).map(Paths.get(_)) else None,
      messageSizeThreshold = if (s3Details.hasPath("message.size.threshold")) s3Details.getInt("message.size.threshold") else Constants.DefaultMessageSizeThreshold
    )
  }

  @Provides
  @Named("byokFailureIgnoreS3NoSuchKey")
  def getIgnoreS3NoSuchKeyFlag: Boolean = true

  @Provides
  @Named("byokFailureQueueUrl")
  def getBYOKFailureQueueUrl(config: Config,
                          @Named("byokFailureExtendedSqsClient") client: ExtendedSqsAsyncClient): String = {
    val queueName = config.getString("pipeline.byok-failure.sqs.queue.name")
    val getQueueUrlRequest = GetQueueUrlRequest.builder().queueName(queueName).build()
    client.getQueueUrl(getQueueUrlRequest).get(5, TimeUnit.SECONDS).queueUrl()
  }

  @Provides
  @Named("byokFailureSqsReceiveMsgCount")
  def getReceiveMesageCount(config: Config): Int = {
    val receiveMessageCount = getInt(config, "pipeline.byok-failure.sqs.receive.message.count").getOrElse(10)
    if (receiveMessageCount > 10) {
      throw new IllegalStateException("No more than 10 messages can be requested at a time")
    }
    receiveMessageCount
  }

  @Provides
  @Named("byokFailureSqsDeleteMsgMaxRetry")
  def getDeleteRetries(config: Config): Int = {
    getInt(config, "pipeline.byok-failure.sqs.delete.messages.max.retries").getOrElse(3)
  }

  private def getInt(conf: Config, path: String): Option[Int] = {
    if (conf.hasPath(path)) Option(conf.getInt(path)) else None
  }

}
