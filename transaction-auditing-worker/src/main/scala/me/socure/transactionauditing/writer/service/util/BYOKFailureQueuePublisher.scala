package me.socure.transactionauditing.writer.service.util

import java.util.concurrent.TimeUnit
import com.amazonaws.services.kms.model.AWSKMSException
import com.typesafe.config.Config
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.sqs.sns.v2.extended.common.{Configuration, Constants, S3Wrapper}
import me.socure.common.sqs.v2.extended.ExtendedSqsAsyncClient
import me.socure.common.uuid.RandomUuidProvider
import me.socure.transaction.auditing.encryption.common.BYOKException
import me.socure.transactionauditing.sqs.marshaller.SqsMessageMarshaller
import me.socure.transactionauditing.sqs.model.SqsApiTransaction
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.DefaultFormats
import org.json4s.jackson.Serialization
import software.amazon.awssdk.services.s3.S3AsyncClient
import software.amazon.awssdk.services.sqs.SqsAsyncClient
import software.amazon.awssdk.services.sqs.model.{GetQueueUrlRequest, MessageAttributeValue, SendMessageRequest, SendMessageResponse}

import scala.collection.JavaConversions
import scala.compat.java8.FutureConverters
import scala.concurrent.{ExecutionContext, Future}

class BYOKFailureQueuePublisher(config: Config)(implicit ec: ExecutionContext) {
  private val metrics: Metrics = JavaMetricsFactory.get("transaction.auditing." + this.getClass.getSimpleName)
  private val sqsClient: SqsAsyncClient = getSQSClient()
  private val queueUrl: String = getQueueUrl()

  def sendToQueue(txn: SqsApiTransaction, ex: BYOKException): Future[SendMessageResponse] = {
    val sendMessageRequest = buildSendMessageRequest(txn, ex)
    FutureConverters.toScala(this.sqsClient.sendMessage(sendMessageRequest)) recover {
      case ex: Exception =>
        metrics.increment("insert.exception",
          "accountId:" + txn.accountId, "class:" + ex.getClass.getSimpleName)
        throw ex
    }
  }

  def buildSendMessageRequest(txn: SqsApiTransaction, ex: BYOKException): SendMessageRequest = {
    val messageBody = SqsMessageMarshaller.marshal(txn)
    val currentTimeInMillis = DateTime.now(DateTimeZone.UTC).getMillis
    val timestampMsgAttrVal = MessageAttributeValue.builder()
      .dataType("Number")
      .stringValue(currentTimeInMillis.toString)
      .build()
    val reasonAttrVal = MessageAttributeValue.builder()
      .dataType("String")
      .stringValue(ex.getMessage)
      .build()
    val publishedToBYOKQueueVal = MessageAttributeValue.builder()
      .dataType("String")
      .stringValue("true")
      .build()
    val msgAttribute = JavaConversions.mapAsJavaMap(Map(
      "timestamp" -> timestampMsgAttrVal,
      "reason" -> reasonAttrVal,
      "publishedToBYOKQueue" -> publishedToBYOKQueueVal
    ))
    SendMessageRequest.builder()
      .queueUrl(this.queueUrl)
      .messageBody(messageBody)
      .messageAttributes(msgAttribute)
      .build()
  }

  private def getSQSClient(): SqsAsyncClient = {
    val baseClient = SqsAsyncClient.create()
    val extendedSqsConf = Configuration(
      s3BucketName = config.getString("pipeline.byok-failure.sqs.large.files.s3.bucket.name"),
      basePath = None,
      messageSizeThreshold = Constants.DefaultMessageSizeThreshold
    )
    val s3Client = S3Wrapper(S3AsyncClient.create())
    new ExtendedSqsAsyncClient(baseClient, s3Client, extendedSqsConf,
      ignoreS3NoSuchKey = true, uuidProvider = RandomUuidProvider)
  }

  private def getQueueUrl(): String = {
    val queueName = config.getString("pipeline.byok-failure.sqs.queue.name")
    val getQueueUrlRequest = GetQueueUrlRequest.builder().queueName(queueName).build()
    this.sqsClient.getQueueUrl(getQueueUrlRequest).get(5, TimeUnit.SECONDS).queueUrl()
  }

}
