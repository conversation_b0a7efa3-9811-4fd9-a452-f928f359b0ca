package me.socure.transactionauditing.writer.service.module

import com.amazonaws.auth.AWSCredentialsProviderChain
import com.amazonaws.services.s3.AmazonS3Client
import com.google.inject.name.Names
import com.google.inject.{AbstractModule, Provides}
import com.typesafe.config.Config
import me.socure.account.client.CachedAccountDetailsClient
import me.socure.account.client.superadmin.AccountInfoClientImpl
import me.socure.common.clock.RealClock
import me.socure.common.error.reporter.ErrorReporterMail
import me.socure.common.executorservice.MetricsThreadPoolExecutor
import me.socure.common.metrics.JavaMetricsFactory
import me.socure.common.rds.impl.HikariDataSourceProvider
import me.socure.common.reliable.metrics.datadog.CompositeMetricsProvider
import me.socure.common.retry.strategy.RetryStrategy
import me.socure.common.s3.client.AmazonS3ClientFactory
import me.socure.common.s3.encryption.KMSManaged
import me.socure.common.s3.v2.S3AsyncFiles
import me.socure.common.s3.v2.encryption.KMSManagedSSE
import me.socure.common.s3.v2.factory.S3AsyncClientFactory
import me.socure.common.session.marshaller.JWTMarshaller
import me.socure.common.sqs.sns.v2.extended.common.{Configuration, Constants, S3Wrapper}
import me.socure.common.sqs.v2.extended.ExtendedSqsAsyncClient
import me.socure.common.sqs.v2.{MessagesProcessor, SqsMessagesDeleter}
import me.socure.common.uuid.RandomUuidProvider
import me.socure.dynamic.control.center.v2.factory.DynamicControlCenterV2Factory
import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate
import me.socure.model.monitoring.metrics.{ModelMetricsProvider, PIIMetricsProvider, TransactionCounterMetricsProvider}
import me.socure.transaction.audit.common.model.StoppableService
import me.socure.transaction.auditing.encryption.common.AccountIdResolver
import me.socure.transaction.auditing.encryption.encryptor.v2.AuditDataEncryptorV2
import me.socure.transaction.search.client.indexer.TrxIndexerClientFactory
import me.socure.transactionauditing.common.config.{S3Config, S3ConfigFactory}
import me.socure.transactionauditing.common.factory.AccountIdResolverFactory
import me.socure.transactionauditing.encryption.SqsAuditDataEncryptionService
import me.socure.transactionauditing.metrics.TransactionMetricsService
import me.socure.transactionauditing.pii.mask.{PiiMaskService, PiiMaskServiceFactory}
import me.socure.transactionauditing.s3.storage.{S3AsyncTransactionStorage, S3TransactionStorage}
import me.socure.transactionauditing.s3.thirdparty.S3AsyncThirdPartyStorageService
import me.socure.transactionauditing.sqs.processor.queues.{CustomerUserIdFixer, TrxSearchIndexer, VelocitySearchIndexer}
import me.socure.transactionauditing.storage.dynamo.dao.DynamoTransactionDao
import me.socure.transactionauditing.storage.dynamo.factory.DynamoTransactionDaoFactory
import me.socure.transactionauditing.storage.dynamo.model.DynamoTableConfig
import me.socure.transactionauditing.storage.factory.QueueTransactionDaoFactory
import me.socure.transactionauditing.storage.mysql.slick.sqs.MysqlSlickQueueUnifiedTransactionDao
import me.socure.transactionauditing.storage.util.LongTextColumnsReader
import me.socure.transactionauditing.writer.service.engine.{TransactionProcessor, VelocitySearchProcessor}
import me.socure.transactionauditing.writer.service.obfuscator.{PiiObfuscatorFactory, SqsApiTransactionObfuscator}
import me.socure.transactionauditing.writer.service.provider.{StoppablePipelineServiceProvider, TAWriterHealthServiceProvider}
import me.socure.transactionauditing.writer.service.util.{BYOKFailureQueuePublisher, TransactionAuditDataStorage}
import me.socure.transactionauditing.writer.service.{DBMigrator, PipelineService, TAWriterHealthService}
import me.socure.types.scala.batch.BatchConf
import me.socure.velocity.search.client.VelocitySearchClientFactory
import net.codingwell.scalaguice.ScalaModule
import org.slf4j.LoggerFactory
import software.amazon.awssdk.http.nio.netty.NettyNioAsyncHttpClient
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.{DynamoDbAsyncClient, DynamoDbClient}
import software.amazon.awssdk.services.dynamodb.model.DynamoDbException
import software.amazon.awssdk.services.s3.S3AsyncClient
import software.amazon.awssdk.services.sqs.SqsAsyncClient
import software.amazon.awssdk.services.sqs.model.{GetQueueUrlRequest, Message, ReceiveMessageRequest}

import java.net.URI
import java.nio.file.Paths
import java.util.concurrent.TimeUnit
import javax.inject.{Named, Singleton}
import javax.sql.DataSource
import scala.collection.JavaConverters._
import scala.concurrent.ExecutionContext
import scala.concurrent.duration._
import scala.util.Try

class StoppablePipelineServiceModule extends AbstractModule with ScalaModule {

  override def configure(): Unit = {
    bind(classOf[StoppableService])
      .annotatedWith(Names.named("transaction"))
      .toProvider(classOf[StoppablePipelineServiceProvider]).asEagerSingleton()

    bind(classOf[TAWriterHealthService]).toProvider(classOf[TAWriterHealthServiceProvider]).asEagerSingleton()
  }

  @Provides
  @Singleton
  @Named("txBaseSqsClient")
  def getBaseClient(config: Config): SqsAsyncClient = {
    val region = config.getString("pipeline.region")
    SqsAsyncClient.builder().region(Region.of(region)).build()
  }

  @Provides
  @Named("transactionSqsConfig")
  def getExtendedSqsConfig(config: Config): Configuration = {
    val s3Details = config.getConfig("pipeline.transaction.sqs")
    Configuration(
      s3BucketName = s3Details.getString("large.files.s3.bucket.name"),
      basePath = if (s3Details.hasPath("base.path")) Option(s3Details.getString("base.path")).map(Paths.get(_)) else None,
      messageSizeThreshold = if (s3Details.hasPath("message.size.threshold")) s3Details.getInt("message.size.threshold") else Constants.DefaultMessageSizeThreshold
    )
  }

  @Provides
  @Singleton
  @Named("txS3Wrapper")
  def getWrappedS3Client: S3Wrapper = S3Wrapper(S3AsyncClient.create())

  @Provides
  @Named("ignoreS3NoSuchKey")
  def getIgnoreS3NoSuchKeyFlag: Boolean = true


  @Provides
  @Singleton
  @Named("transactionExtendedSqsClient")
  def getExtendedSqsAsyncClient(
                                 @Named("txBaseSqsClient") baseClient: SqsAsyncClient,
                                 @Named("txS3Wrapper") s3Client: S3Wrapper,
                                 @Named("transactionSqsConfig") extendedSqsConf: Configuration,
                                 @Named("ignoreS3NoSuchKey") ignoreS3NoSuchKey: Boolean): ExtendedSqsAsyncClient = {
    new ExtendedSqsAsyncClient(baseClient, s3Client, extendedSqsConf, ignoreS3NoSuchKey, uuidProvider = RandomUuidProvider)
  }

  @Provides
  @Named("transactionQueueUrl")
  def getTransactionQueue(config: Config, @Named("transactionExtendedSqsClient") client: ExtendedSqsAsyncClient): String = {
    val queueName = config.getString("pipeline.transaction.sqs.queue.name")
    val getQueueUrlRequest = GetQueueUrlRequest.builder().queueName(queueName).build()
    client.getQueueUrl(getQueueUrlRequest).get(15, TimeUnit.SECONDS).queueUrl()
  }

  @Provides
  @Named("sqsReadBatch")
  def getReceiveMesageCount(config: Config): Int = {
    val receiveMessageCount = getInt(config, "pipeline.transaction.sqs.receive.message.count").getOrElse(10)
    if (receiveMessageCount > 10) {
      throw new IllegalStateException("No more than 10 messages can be requested at a time")
    }
    receiveMessageCount
  }

  @Provides
  @Named("sqsDeleteRetries")
  def getDeleteRetries(config: Config): Int = {
    getInt(config, "pipeline.transaction.sqs.delete.messages.max.retries").getOrElse(3)
  }

  @Provides
  @Named("transactionSqsMsgDeleter")
  @Singleton
  def getTransactionMessageDeleter(@Named("transactionExtendedSqsClient") client: ExtendedSqsAsyncClient,
                                   @Named("transactionQueueUrl") queueUrl: String,
                                   @Named("sqsDeleteRetries") deleteMaxRetries: Int)
                                  (implicit ec: ExecutionContext): SqsMessagesDeleter = {

    new SqsMessagesDeleter(
      client = client,
      queueUrl = queueUrl,
      maxRetries = deleteMaxRetries,
      metrics = JavaMetricsFactory.get("transaction.auditing.writer.sqs.delete.messages"),
      logger = LoggerFactory.getLogger("transaction.auditing.writer.sqs.delete.messages")
    )
  }

  @Provides
  @Named("transactionReceiveMessage")
  @Singleton
  def getTransactionReceiveMessage(config: Config,
                                   @Named("transactionQueueUrl") queueUrl: String,
                                   @Named("sqsReadBatch") receiveMessageCount: Int): ReceiveMessageRequest = {
    ReceiveMessageRequest
      .builder()
      .queueUrl(queueUrl)
      .maxNumberOfMessages(receiveMessageCount)
      .visibilityTimeout(config.getInt("pipeline.transaction.sqs.visibility.timeout.seconds"))
      .messageAttributeNames("All")
      .build()
  }

  @Provides
  @Named("trxAuditDataStorage")
  @Singleton
  def getTrxAuditDataStorage(
                              s3Config: S3Config
                            )(implicit ec: ExecutionContext): TransactionAuditDataStorage = {
    new TransactionAuditDataStorage(
      S3AsyncClient.create(),
      bucketName = s3Config.auditDataBucket.get,
      RetryStrategy.constantBackoff(100.milliseconds, 2)
    )
  }

  @Provides
  @Named("transactionMessageProcessor")
  @Singleton
  def getTransactionMessageProcessor(
                                      unifiedTransactionDao: MysqlSlickQueueUnifiedTransactionDao,
                                      sqsAuditDataEncryptionService: SqsAuditDataEncryptionService,
                                      piiMaskService: PiiMaskService,
                                      transactionMetricsService: TransactionMetricsService,
                                      accountIdResolver: AccountIdResolver,
                                      trxSearchIndexer: Option[TrxSearchIndexer],
                                      velocitySearchProcessor: VelocitySearchProcessor,
                                      byokFailureQueuePublisher: BYOKFailureQueuePublisher,
                                      @Named("parseErrorStore") errorStore: S3AsyncTransactionStorage,
                                      @Named("trxEsPiiDeleter") obfuscator: SqsApiTransactionObfuscator,
                                      @Named("trxAuditDataStorage") transactionAuditDataStorage: TransactionAuditDataStorage,
                                      @Named("txnSearchIndexingEnabled") txnSearchIndexingEnabled: Boolean,
                                      @Named("dynamoInsertEnabled") dynamoInsertEnabled: Boolean,
                                      @Named("dynamoTransactionDao") dynamoTransactionDao: DynamoTransactionDao
                                    )(implicit ec: ExecutionContext): MessagesProcessor[Message] = {
    new TransactionProcessor(
      unifiedTransactionDao,
      dynamoTransactionDao,
      sqsAuditDataEncryptionService,
      piiMaskService,
      transactionMetricsService,
      CustomerUserIdFixer,
      accountIdResolver,
      trxSearchIndexer,
      velocitySearchProcessor,
      byokFailureQueuePublisher,
      errorStore,
      obfuscator,
      transactionAuditDataStorage,
      txnSearchIndexingEnabled,
      dynamoInsertEnabled
    )
  }

  @Provides
  @Singleton
  def getVelocitySearchProcessor(
                                  velocitySearchIndexer: Option[VelocitySearchIndexer],
                                  @Named("parseErrorStore") errorStore: S3AsyncTransactionStorage,
                                  dynamicControlCenterV2Evaluate: DynamicControlCenterV2Evaluate
                                )(implicit ec: ExecutionContext): VelocitySearchProcessor = {
    new VelocitySearchProcessor(velocitySearchIndexer, errorStore, dynamicControlCenterV2Evaluate)
  }

  @Provides
  @Named("trxEsPiiDeleter")
  def getTrxEsPiiDeleter(config: Config)(implicit ec: ExecutionContext): SqsApiTransactionObfuscator = {
    val accounts = if (config.hasPath("obfuscate.pii.accounts")) {
      config.getLongList("obfuscate.pii.accounts").asScala.map(_.longValue())
    } else Seq.empty[Long]
    PiiObfuscatorFactory.createTransactionObfuscator(
      accounts,
      config.getConfig("memcached").getString("host"),
      config.getConfig("memcached").getInt("port"),
      config.getConfig("account.service").getString("endpoint")
    )
  }

  @Provides
  @Named("transactionConsumerCount")
  def getParallelConsumers(config: Config): Int = {
    if (config.hasPath("pipeline.transaction.parallelism"))
      config.getInt("pipeline.transaction.parallelism")
    else
      config.getInt("pipeline.parallelism")
  }

  @Provides
  @Named("txnSearchIndexingEnabled")
  def getTxnSearchIndexingEnabledFlag(config: Config): Boolean = {
     config.hasPath("transaction.search.indexing.enabled") match {
       case true => config.getBoolean("transaction.search.indexing.enabled")
       case _ => true
     }
  }

  @Provides
  @Named("transactionSqsBatchConfig")
  def getTransactionBatchConf(config: Config): Option[BatchConf] = {
    if (config.hasPath("pipeline.buffering")) {
      Some(BatchConf(
        targetCount = config.getInt("pipeline.buffering.targetCount"),
        maxDuration = Duration(config.getString("pipeline.buffering.maxDuration"))
      ))
    } else None
  }

  @Provides
  @Named("transactionRestartIntervalInMs")
  def getTransactionRestartIntervalInMs(config: Config): Long = {
    if (config.hasPath("pipeline.transaction.restart.interval")) {
      val restartCfg = config.getString("pipeline.transaction.restart.interval")
      Duration(restartCfg).toMillis
    } else {
      Duration("24 hours").toMillis
    }
  }

  @Provides
  @Singleton
  def getSqsAuditDataEncryptionService(auditDataEncryptor: AuditDataEncryptorV2): SqsAuditDataEncryptionService = {
    new SqsAuditDataEncryptionService(auditDataEncryptor)
  }

  @Provides
  def getS3Config(config: Config): S3Config = {
    S3ConfigFactory.get(config, pipelineBasedConfig = true)
  }

  @Provides
  @Named("legacyS3Client")
  @Singleton
  def getLegacyS3Client(@Named("awsCredentialChainProvider") credChain: AWSCredentialsProviderChain): AmazonS3Client = {
    AmazonS3ClientFactory.get(credChain)
  }


  @Provides
  @Singleton
  def getPiiMaskService(s3Config: S3Config,
                        config: Config,
                        @Named("legacyS3Client") amazonS3Client: AmazonS3Client)
                       (implicit ec: ExecutionContext): PiiMaskService = {
    val kmsKeyARN = config.getString("pipeline.transaction.sqs.s3.failures.bucket.key-arn")
    val s3SSE = KMSManaged(Some(kmsKeyARN))

    val s3PiiService: S3TransactionStorage = s3Config.maskingErrorBucket match {
      case Some(bucket) => new S3TransactionStorage(amazonS3Client, bucket, s3SSE)
      case None => throw new Exception("Did not provide config for masking error bucket")
    }

    PiiMaskServiceFactory.get(
      config.getConfig("memcached").getString("host"),
      config.getConfig("memcached").getInt("port"),
      config.getConfig("account.service").getString("endpoint"),
      "encryption",
      s3PiiService
    )
  }

  @Provides
  @Singleton
  def getAccountIdResolver(config: Config)(implicit ec: ExecutionContext): AccountIdResolver = {
    AccountIdResolverFactory.create(config)
  }

  @Provides
  @Singleton
  def getTransactionSearchIndexer(config: Config)(implicit ec: ExecutionContext): Option[TrxSearchIndexer] = {
    if (config.hasPath("transaction.search")) {
      val trxIndexerClient = TrxIndexerClientFactory.create(config.getConfig("transaction.search"))
      Some(new TrxSearchIndexer(trxIndexerClient))
    } else None
  }

  @Provides
  @Singleton
  def getVelocitySearchIndexer(config: Config)(implicit ec: ExecutionContext): Option[VelocitySearchIndexer] = {
    if (config.hasPath("velocity.search") && config.hasPath("device.jwt.token.key")) {
      val jwtMarshaller = new JWTMarshaller(config.getString("device.jwt.token.key"))
      val client = new VelocitySearchIndexer(VelocitySearchClientFactory.createV2(config.getConfig("velocity.search")), jwtMarshaller)
      Some(client)
    } else None
  }

  @Provides
  @Named("auditClusterUnifiedDS")
  @Singleton
  def auditClusterUnifiedDataSource(config: Config): DataSource = {
    new HikariDataSourceProvider(config.getConfig("database.audit.unified-cluster")).provide()
  }

  @Provides
  @Singleton
  def getLongTextColumnsReader(config: Config): LongTextColumnsReader = {
    val totalWorkers = if (config.hasPath("longtext.column.reader.num.threads")) {
      config.getInt("longtext.column.reader.num.threads")
    } else 30
    val batchSize = if (config.hasPath("longtext.column.reader.batch.size")) {
      config.getInt("longtext.column.reader.batch.size")
    } else 20
    implicit val ec = ExecutionContext.fromExecutor(new MetricsThreadPoolExecutor(totalWorkers, JavaMetricsFactory.get("ta.long.text.column.reader")))
    val dataBucketName = if (config.hasPath("transaction.audit.bucket.name")) config.getString("transaction.audit.bucket.name") else ""
    val s3AsyncFiles = new S3AsyncFiles(S3AsyncClientFactory.get())
    new LongTextColumnsReader(dataBucketName, s3AsyncFiles, batchSize)
  }

  @Provides
  @Singleton
  def getTransactionMetrics(config: Config, clock: RealClock)
                           (implicit ec: ExecutionContext): TransactionMetricsService = {
    val transactionMetricsEnabled = Try(config.getBoolean("txn.metrics.enabled")).getOrElse(true)
    new TransactionMetricsService(
      errorReporterMail = ErrorReporterMail(
        config = config.getConfig("mailgun"),
        clock = clock
      ),
      metricsProvider = CompositeMetricsProvider(
        new TransactionCounterMetricsProvider,
        new ModelMetricsProvider,
        new PIIMetricsProvider
      ),
      accountDetailsClient = new CachedAccountDetailsClient(
        accountInfoClient = new AccountInfoClientImpl(config.getConfig("account.service").getString("endpoint"))
      ),
      transactionMetricsEnabled = transactionMetricsEnabled
    )
  }

  @Provides
  @Singleton
  def getDBMigrator(@Named("auditClusterUnifiedDS") auditClusterUnifiedDS: DataSource, config: Config): DBMigrator = {
    val flag = config.getBoolean("withDBMigration")
    new DBMigrator(auditClusterUnifiedDS, flag)
  }

  @Provides
  @Singleton
  @Named("transactionPipeline")
  def getTransactionPipeline(@Named("transaction") pipeline: StoppableService)(implicit ec: ExecutionContext): PipelineService = {
    new PipelineService(pipeline)
  }

  @Provides
  @Singleton
  def getBYOKFailureQueuePublisher(config: Config)(implicit ec: ExecutionContext): BYOKFailureQueuePublisher = {
    new BYOKFailureQueuePublisher(config)
  }


  @Provides
  @Singleton
  @Named("parseErrorStore")
  def getS3AsyncTPStorageService(
                                  s3Config: S3Config,
                                  config: Config,
                                  clock: RealClock)(implicit ec: ExecutionContext): S3AsyncTransactionStorage = {
    val bucketName = s3Config.parsingErrorBucket.getOrElse(throw new Exception("S3 bucket for parsing error is required"))

    val (bucket, nested) = S3AsyncThirdPartyStorageService.parseBucketName(bucketName)

    val kmsKeyARN = config.getString("pipeline.transaction.sqs.s3.failures.bucket.key-arn")
    val s3SSE = KMSManagedSSE(kmsKeyARN)

    new S3AsyncTransactionStorage(
      S3AsyncClient.create(),
      bucket,
      nested,
      clock,
      RetryStrategy.exponentialBackoff(100.milliseconds, 20.seconds, 2, 3),
      s3SSE
    )
  }

  private def getInt(conf: Config, path: String): Option[Int] = {
    if (conf.hasPath(path)) Option(conf.getInt(path)) else None
  }



  @Provides
  @Singleton
  def getQueueTransactionUnifiedDao(@Named("auditClusterUnifiedDS") auditClusterUnifiedDS: DataSource)
                                   (implicit ec: ExecutionContext): MysqlSlickQueueUnifiedTransactionDao = {
    QueueTransactionDaoFactory.create(auditClusterUnifiedDS)
  }

  @Provides
  @Singleton
  def evaluate(config: Config)(implicit ec: ExecutionContext): DynamicControlCenterV2Evaluate = {
    DynamicControlCenterV2Factory.getEvaluator(config)
  }

  @Provides
  @Singleton
  @Named("dynamoInsertEnabled")
  def getDynamoInsertEnabled(conf: Config): Boolean = conf.getBoolean("dynamodb.enabled")

  @Provides
  @Singleton
  @Named("dynamoDbAsyncClient")
  def getDynamoDbAsyncClient(conf: Config): DynamoDbAsyncClient = {
    try {

      val endpointOpt: Option[String] = if(conf.hasPath("dynamodb.endpoint")) Some(conf.getString("dynamodb.endpoint")) else None
      val maxConcurrency = conf.getInt("dynamodb.client.config.maxConcurrency")
      val writeTimeout = conf.getInt("dynamodb.client.config.writeTimeout")
      val asyncHttpClient = NettyNioAsyncHttpClient.builder()
        .maxConcurrency(maxConcurrency)
        .writeTimeout(java.time.Duration.ofSeconds(writeTimeout))
        .build()
      endpointOpt match {
        case Some(endpoint) =>
          DynamoDbAsyncClient.builder()
            .endpointOverride(URI.create(endpoint))
            .httpClient(asyncHttpClient)
            .build()
        case _ => DynamoDbAsyncClient.builder().httpClient(asyncHttpClient).build()
      }

    } catch {
      case e: DynamoDbException => throw new RuntimeException("Unable to create DynamoDB async client", e)
    }
  }

  @Provides
  @Singleton
  @Named("dynamoTransactionDao")
  def getDynamoTransactionDao(conf: Config,
                               @Named("dynamoDbAsyncClient") dynamoDbAsyncClient: DynamoDbAsyncClient): DynamoTransactionDao = {
    val tableName = conf.getString("dynamodb.tableName")
    DynamoTransactionDaoFactory.create(dynamoDbAsyncClient, DynamoTableConfig(tableName = tableName))
  }
}
