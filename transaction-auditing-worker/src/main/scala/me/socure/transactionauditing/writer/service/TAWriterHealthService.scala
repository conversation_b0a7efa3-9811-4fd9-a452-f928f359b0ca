package me.socure.transactionauditing.writer.service

import me.socure.common.healthcheck.checker.HealthCheckProcessorBasedHealthChecker
import me.socure.common.healthcheck.servlet.HealthCheckServlet
import me.socure.common.jetty.AbstractJettyService
import me.socure.support.timeout.ApiTimeout
import org.eclipse.jetty.servlet.ServletHolder
import org.eclipse.jetty.util.thread.ThreadPool

import scala.concurrent.ExecutionContext

class TAWriterHealthService(
                             threadPool: ThreadPool, port: Int, healthChecker: HealthCheckProcessorBasedHealthChecker
                           )(implicit ec: ExecutionContext, apiTimeout: ApiTimeout)
  extends AbstractJettyService(
    threadPool = threadPool,
    port = port, apiTimeout = Some(apiTimeout.value)
  ) {

  override def beforeStartUp(): Unit = {
    val healthCheckServletHolder = new ServletHolder(new HealthCheckServlet(healthChecker))
    getHandler.addServlet(healthCheckServletHolder, "/healthcheck/*")
  }

}
