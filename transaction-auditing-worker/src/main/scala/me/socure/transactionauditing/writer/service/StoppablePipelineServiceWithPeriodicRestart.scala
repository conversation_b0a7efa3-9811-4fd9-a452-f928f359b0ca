package me.socure.transactionauditing.writer.service

import me.socure.common.sqs.v2.PeriodicRestartPipelineService
import me.socure.transaction.audit.common.model.StoppableService

import scala.concurrent.{ExecutionContext, Future}

class StoppablePipelineServiceWithPeriodicRestart(pipeline: PeriodicRestartPipelineService)
                                                 (implicit ec: ExecutionContext) extends StoppableService {

  override def start(): Future[Unit] = Future(pipeline.start())

  override def stop(): Unit = pipeline.stop()
}
