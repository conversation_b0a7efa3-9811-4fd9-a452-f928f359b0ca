package me.socure.transactionauditing.writer.service.util

import me.socure.common.compression.gzip.Gzip
import me.socure.common.data.core.consumer.DataConsumer
import me.socure.common.data.core.service._
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.retry.strategy.RetryStrategy
import me.socure.common.s3.v2.S3AsyncFiles
import me.socure.transactionauditing.common.domain.ApiTransactionAuditData
import me.socure.transactionauditing.common.transaction.TransactionColumns
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.s3.S3AsyncClient

import java.nio.charset.StandardCharsets
import scala.concurrent.{ExecutionContext, Future}

class TransactionAuditDataStorage(
                                   s3AsyncClient: S3AsyncClient,
                                   bucketName: String,
                                   retryStrategy: RetryStrategy
                                 )(implicit ec: ExecutionContext) {

  private val logger = LoggerFactory.getLogger(getClass)
  private val s3Files = new S3AsyncFiles(s3AsyncClient)
  private val metrics: Metrics = JavaMetricsFactory.get("transaction.audit.data.storage")

  def storeTransactionAudit(transactionAuditData: ApiTransactionAuditData): Future[Seq[Unit]] = {
    val prefix = constructS3Prefix(transactionAuditData.accountId.toString, transactionAuditData.transactionId)

    val errorMsgRes: Future[Unit] = if(transactionAuditData.errorMsg.isDefined && transactionAuditData.errorMsg.nonEmpty) uploadWithLogging(transactionAuditData.errorMsg.get, prefix + TransactionColumns.ERROR_MSG.name) else Future.successful()
    val parametersRes: Future[Unit] = if(null != transactionAuditData.parameters && transactionAuditData.parameters.nonEmpty) uploadWithLogging(transactionAuditData.parameters, prefix + TransactionColumns.PARAMETERS.name) else Future.successful()
    val responseRes = if(null != transactionAuditData.response && transactionAuditData.response.nonEmpty) uploadWithLogging(transactionAuditData.response, prefix + TransactionColumns.API_RESPONSE.name) else Future.successful()
    val debugRes = if(null != transactionAuditData.debug && transactionAuditData.debug.nonEmpty) uploadWithLogging(transactionAuditData.debug, prefix + TransactionColumns.DEBUG.name) else Future.successful()
    val detailsRes = if(null != transactionAuditData.details && transactionAuditData.details.nonEmpty) uploadWithLogging(transactionAuditData.details, prefix + TransactionColumns.DETAILS.name) else Future.successful()
    val reasonCodesRes = if(null != transactionAuditData.reasonCodes && transactionAuditData.reasonCodes.nonEmpty) uploadWithLogging(transactionAuditData.reasonCodes, prefix + TransactionColumns.REASON_CODES.name) else Future.successful()
    val requestURIRes = if(null != transactionAuditData.requestURI && transactionAuditData.requestURI.nonEmpty) uploadWithLogging(transactionAuditData.requestURI, prefix + TransactionColumns.API_REQUEST) else Future.successful()
    val internalWorkLogsRes = if(transactionAuditData.internalWorkLogs.isDefined && transactionAuditData.internalWorkLogs.nonEmpty) uploadWithLogging(transactionAuditData.internalWorkLogs.get, prefix + "internalWorkLogs") else Future.successful()

    Future.sequence(Seq(errorMsgRes, parametersRes, responseRes, debugRes, detailsRes, reasonCodesRes, requestURIRes, internalWorkLogsRes))
  }

  private def uploadWithLogging(rawMessage: String, key: String) = {
    upload()
      .retry(retryStrategy)
      .withLogging(
        logger = logger,
        inToStr = op => s"uploading transaction audit data - ${op._1}"
      ).apply((key, rawMessage))
  }

  private def constructS3Prefix(accountId: String, transactionId: String): String = {
    val bucketUrl = new StringBuffer
    bucketUrl.append(if (null != accountId  && accountId.nonEmpty) accountId else "UnknownAccount")
    bucketUrl.append("/")
    bucketUrl.append(if (null != transactionId && transactionId.nonEmpty) transactionId else "UnknownTransaction")
    bucketUrl.append("/")
    bucketUrl.toString
  }

  private def upload(): DataConsumer[(String, String)] = {
    DataConsumer { case (key, content) =>
      metrics.timeFuture("s3.upload.time") {
        s3Files.upload(
          Gzip.compress(content.getBytes(StandardCharsets.UTF_8)),
          bucketName,
          key.concat(".gz")
        )
      }.map(_ => Unit)
    }
  }
}