package me.socure.transactionauditing.writer.service.obfuscator

import me.socure.account.client.superadmin.AccountInfoClient
import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.common.logger.TransactionAwareLoggerFactory
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.transaction.id.TrxId
import me.socure.model.encryption.ApiKeyString
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import me.socure.transactionauditing.pii.mask.PiiMaskService.PiiMaskException
import me.socure.transactionauditing.sqs.model.SqsApiTransaction
import org.json4s.JsonAST.JValue
import org.json4s.jackson.JsonMethods.parse
import org.json4s.jackson.Serialization

import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext, Future}

class SqsApiTransactionObfuscator(accounts: Seq[Long], inputParameters: Seq[String],
                                  outputParameters: Seq[String], accountInfoClient: AccountInfoClient)(implicit ec: ExecutionContext)
  extends PiiObfuscator[SqsApiTransaction](inputParameters, outputParameters) {

  private val logger = TransactionAwareLoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get("transaction.auditing.pii.obfuscation")

  override def mask(trx: SqsApiTransaction): SqsApiTransaction = {
    val maskPii = isPiiMaskEnabled(trx)
    if (accounts.contains(trx.accountId) || maskPii) {
      val params = Option(trx.parameters).filter(_.trim.nonEmpty).map(maskParameters(_, isDbValue = false))
      val request = Option(trx.requestURI).filter(_.trim.nonEmpty).map(maskRequestUri)
      val response = Option(trx.response).flatMap(safeParse).map(ds => outputParameters.foldLeft(ds)(updateJson)).map(Serialization.write(_))
      val details = Option(trx.details).flatMap(safeParse).map(ds => outputParameters.foldLeft(ds)(updateJson)).map(Serialization.write(_))
      val debug = Option(trx.debug).flatMap(safeParse).map(ds => debugUpdateJson(ds)).map(Serialization.write(_))
      metrics.increment("trx.es", s"account:${trx.accountId}")

      trx.copy(
        parameters = params.getOrElse(""),
        requestURI = request.getOrElse(""),
        response = response.getOrElse(""),
        details = details.getOrElse(""),
        debug = debug.getOrElse("")
      )
    } else trx
  }

  private def safeParse(data: String): Option[JValue] = {
    val str = data.trim
    if (str.isEmpty || (!str.startsWith("{") && !str.startsWith("["))) None else Some(parse(str))
  }

  private def isPiiMaskEnabled(transaction: SqsApiTransaction) : Boolean = {
    def isPiiMaskEnabled0(transaction: SqsApiTransaction): Future[Boolean] = {
      implicit val trxId: TrxId = TrxId(transaction.transactionId)
      accountInfoClient.hasRole(apiKeyString = ApiKeyString(Option(transaction.apiKey).getOrElse("")), role = BusinessUserRoles.MASK_PII.id)
        .map {
          case Left(ErrorResponse(code, _)) if code == ExceptionCodes.AccountNotFound.id => false // Do not mask
          case Left(errorResponse) => throw new PiiMaskException(s"Unable to check whether api key [${transaction.apiKey}] has PII_MASK permission due to : $errorResponse")
          case Right(flagValue) => flagValue
        }
        .recoverWith {
          case ex =>
            logger.error("[PII_MASK_ERROR]", ex)
            metrics.increment("exception", s"class:${ex.getClass.getSimpleName}")
            Future.failed(ex)
        }
    }
    Await.result(isPiiMaskEnabled0(transaction), 10.seconds)
  }

}
