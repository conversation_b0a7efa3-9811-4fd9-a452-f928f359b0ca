package me.socure.transactionauditing.writer.service.engine

import com.amazonaws.services.kms.model.AWSKMSException
import me.socure.common.logger.{TransactionAwareLogger, TransactionAwareLoggerFactory}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.sqs.v2.{FailedMessageIds, MessageId, MessagesProcessor}
import me.socure.common.transaction.id.TrxId
import me.socure.model.encryption.{AccountId, ApiKeyString}
import me.socure.transaction.auditing.encryption.common.{AccountIdResolver, BYOKException, BYOKExceptionReason}
import me.socure.transaction.search.index.model.{BulkIndexFailures => TransactionSearchFailures}
import me.socure.transactionauditing.common.domain.ApiTransactionAuditData
import me.socure.transactionauditing.encryption.SqsAuditDataEncryptionService
import me.socure.transactionauditing.metrics.TransactionMetricsService
import me.socure.transactionauditing.pii.mask.PiiMaskService
import me.socure.transactionauditing.s3.storage.S3AsyncTransactionStorage
import me.socure.transactionauditing.sqs.marshaller.DataTruncator.truncate
import me.socure.transactionauditing.sqs.marshaller.SqsMessageMarshaller.unmarshalApiTransaction
import me.socure.transactionauditing.sqs.model.{BatchSqsApiTransaction, SqsApiTransaction}
import me.socure.transactionauditing.sqs.processor.queues.{CustomerUserIdFixer, TrxSearchIndexer}
import me.socure.transactionauditing.storage.QueueTransactionDao.InsertResultVerbose
import me.socure.transactionauditing.storage.dynamo.dao.DynamoTransactionDao
import me.socure.transactionauditing.storage.mysql.slick.sqs.MysqlSlickQueueUnifiedTransactionDao
import me.socure.transactionauditing.storage.util.ValueEncoder
import me.socure.transactionauditing.writer.service.obfuscator.SqsApiTransactionObfuscator
import me.socure.transactionauditing.writer.service.util.{BYOKFailureQueuePublisher, TransactionAuditDataStorage}
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.{Message, MessageAttributeValue}

import java.util.{Map => JMap}
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class TransactionProcessor(
                            unifiedTransactionDao: MysqlSlickQueueUnifiedTransactionDao,
                            dynamoTransactionDao: DynamoTransactionDao,
                            sqsAuditDataEncryptionService: SqsAuditDataEncryptionService,
                            piiMaskService: PiiMaskService,
                            transactionMetricsService: TransactionMetricsService,
                            customerUserIdFixer: CustomerUserIdFixer,
                            accountIdResolver: AccountIdResolver,
                            trxSearchIndexerOpt: Option[TrxSearchIndexer],
                            velocitySearchProcessor: VelocitySearchProcessor,
                            byokFailureQueuePublisher: BYOKFailureQueuePublisher,
                            parseFailStore: S3AsyncTransactionStorage,
                            obfuscator: SqsApiTransactionObfuscator,
                            transactionAuditDataStorage: TransactionAuditDataStorage,
                            transactionSearchIndexingEnabled: Boolean,
                            dynamoInsertEnabled: Boolean
                          )(implicit ec: ExecutionContext) extends MessagesProcessor[Message] {

  private val transactionLogger: TransactionAwareLogger = TransactionAwareLoggerFactory.getLogger(getClass)
  private val logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get("transaction.auditing." + this.getClass.getSimpleName)
  private val nullApiName: String = "NullApiName"
  private val Dao = unifiedTransactionDao

  override def process(messages: Iterable[Message]): Future[FailedMessageIds] = {

    val seqTransactions = messages map {
      message =>
        val messageId = MessageId(message.messageId(), message.receiptHandle())
        Try(unmarshalApiTransaction(message.body())) match {
          case Success(apiTransactionBase) =>
            val apiTransaction: SqsApiTransaction = customerUserIdFixer.fix(apiTransactionBase)
            implicit val trxId: TrxId = TrxId(apiTransaction.transactionId)
            fixAccountId(apiTransaction).map(txn => buildBatchSQSAPITransaction(txn, messageId, message.messageAttributes()))
              .map(Right(_)).map(Some(_))
              .recover {
                case e: Exception =>
                  transactionLogger.error(s"error occurred while trying to resolve account info", e)
                  Some(Left(messageId))
              }
          case Failure(ex) =>
            logger.error(s"error occurred while parsing sqs message", ex)
            parseFailStore.storeInvalidTransaction(message.body())
            Future.successful(None)
        }
    }

    val fatFut = Future.sequence(seqTransactions).map(itrOpts => itrOpts.flatten)

    val cleanedTransactionsFut = fatFut.flatMap {
      itrTransaction =>
        val ops = itrTransaction map {
          case Right(bTrx) =>
            implicit val trxId: TrxId = TrxId(bTrx.transaction.transactionId)
            val fatMetrics = if (bTrx.transaction.accountId != 0) {
              metrics.timeFuture("metrics_call.duration") {
                transactionMetricsService.handle(bTrx.transaction).map(_ => Right())
                  .recover {
                    case e: Exception =>
                      transactionLogger.error("Uncaught exception inserting model monitoring statistics", e)
                      metrics.increment("model.monitoring.error")
                      Left(bTrx.messageId)
                  }
              }
            } else {
              Future.successful(Right())
            }
            fatMetrics flatMap {
              case Right(_) =>
                val encrypted: Future[Either[MessageId, BatchSqsApiTransaction]] = maskPii(bTrx.transaction).flatMap {
                  transactionWithPii =>
                    val transactionWithPiiFiltered = truncate(stripUtf(transactionWithPii))
                    encryptTransaction(transactionWithPiiFiltered).map {
                      f => Right(bTrx.copy(transaction = f))
                    }
                }.recoverWith {
                  case ex: BYOKException => handleBYOKException(bTrx, ex)
                  case ex: AWSKMSException if ex.getStatusCode == 400 =>
                    val byokEx = new BYOKException(BYOKExceptionReason.DataKeyDecryptionError.msg)
                    handleBYOKException(bTrx, byokEx)
                  case e: Exception =>
                    transactionLogger.error(s"Error while encrypting transaction", e)
                    metrics.increment("encryption.error")
                    Future.successful(Left(bTrx.messageId))
                }
                encrypted
              case Left(msg) => Future.successful(Left(msg))
            }
          case Left(msgId) => Future.successful(Left(msgId))
        }
        Future.sequence(ops)
    }

    val insertionOp = metrics.timeFuture("save.batch_method_call.duration") {
      cleanedTransactionsFut.map(itr => itr.toSeq).flatMap {
        batch =>
          val (failed, success) = batch.foldRight[(Seq[MessageId], Seq[BatchSqsApiTransaction])](Nil, Nil) {
            case (Left(msg), (err, valid)) => (err :+ msg, valid)
            case (Right(b), (err, valid)) => (err, valid :+ b)
          }

          if (success.nonEmpty) {
            val nonBYOKFailedTxns: Seq[BatchSqsApiTransaction] = success.filter(batchTxn => !batchTxn.enqueuedToBYOKFailureQueue)
            val encryptedBatch0: Seq[SqsApiTransaction] = nonBYOKFailedTxns.map(e => e.transaction)
            val unencryptedBatch = nonBYOKFailedTxns.map(e => e.unencryptedTransaction.transactionId -> e.unencryptedTransaction).toMap
            val apiTransactionAuditSeq: Seq[ApiTransactionAuditData] = getApiTransactionAuditSeq(encryptedBatch0)
            val encryptedBatch: Seq[SqsApiTransaction] = removeLongTextFields(encryptedBatch0)

            val saveTransactionAuditResults = saveTransactionAuditData(apiTransactionAuditSeq)

            val saveTransactionAuditResultsEither = saveTransactionAuditResults.map(Right(_)) recover {
              case e: Exception =>
                logger.error(s"batch S3 insertion failed", e)
                Left(nonBYOKFailedTxns.map(_.messageId))
            }

            saveTransactionAuditResultsEither flatMap {
              case Left(f) => Future.successful(FailedMessageIds(f))
              case Right(_) =>
                val insertResults = metrics.timeFuture("save.batch_mysql") {
                  saveBatch(encryptedBatch)
                }

                val insertResultsEither = insertResults.map(Right(_)).recover {
                  case e: Exception =>
                    logger.error(s"batch mysql insertion failed", e)
                    Left(nonBYOKFailedTxns.map(_.messageId))
                }
                insertResultsEither.flatMap {
                  case Left(failures) => Future.successful(FailedMessageIds(failures))
                  case Right(res) =>
                    if(dynamoInsertEnabled) {
                      saveBatchDynamo(encryptedBatch) map { encryptionResults =>
                        val failures: Seq[DynamoTransactionDao.InsertResult] = encryptionResults.filterNot(_.success)
                        if(failures.nonEmpty) {
                          Left(FailedMessageIds(nonBYOKFailedTxns.map(_.messageId)))
                        } else {
                          Right(encryptionResults)
                        }
                      } recover {
                        case e: Exception =>
                          logger.error(s"Dynamo batch insertion failed", e)
                          Left(FailedMessageIds(nonBYOKFailedTxns.map(_.messageId)))
                      } flatMap {
                        case Left(failures) =>
                          Future.successful(failures)
                        case Right(_) =>
                          processIndexing(res, nonBYOKFailedTxns, unencryptedBatch, failed, messages)
                      }
                    } else {
                      processIndexing(res, nonBYOKFailedTxns, unencryptedBatch, failed, messages)
                    }
                }
            }
          } else {
            Future.successful(FailedMessageIds(failed))
          }
      }
    }

    insertionOp

  }

  private def processIndexing(
                               res: Seq[InsertResultVerbose],
                               nonBYOKFailedTxns: Seq[BatchSqsApiTransaction],
                               unencryptedBatch: Map[String, SqsApiTransaction],
                               failed: Seq[MessageId],
                               messages: Iterable[Message]
                             ): Future[FailedMessageIds] = {
    val unencryptedBatchUpdated = res.flatMap {
      i =>
        unencryptedBatch.get(i.trxId).map(_.copy(id = i.id))
    }

    //transaction-search
    val trxSearchIndexerResult = transactionSearchIndexingEnabled match {
      case true => trxSearchIndexerOpt.map {
        trxSearchIndexer =>
          //obfuscate TS ES data
          val obfuscatedTrxForTsEs = unencryptedBatchUpdated.map(t => obfuscator.mask(t))
          val (trxIndices, failedTrx) = trxSearchIndexer.processTransactions(obfuscatedTrxForTsEs)
          failedTrx.map(parseFailStore.storeTransaction)
          trxSearchIndexer.indices(trxIndices)
      }.getOrElse(Future.successful(Seq.empty[TransactionSearchFailures])).map {
        case Seq() =>
          logger.debug(s"bulk transaction indexing was successful")
          Seq.empty[MessageId]
        case failedIds =>
          failedIds.foreach { fail =>
            logger.error(s"bulk transaction indexing failed for ${fail.transactionId} due to ${fail.reason}")
          }
          val (toS3, toSqs) = failedIds.partition(bif => bif.reason.contains("MaxBytesLengthExceededException"))
          toS3.foreach { bif =>
            unencryptedBatch.get(bif.transactionId).map {
              trx => parseFailStore.storeTransaction(trx)
            }
          }

          val messageIds = nonBYOKFailedTxns.map(e => e.unencryptedTransaction.transactionId -> e.messageId).toMap
          toSqs.flatMap(id => messageIds.get(id.transactionId))
      }.recover {
        case e: Exception =>
          logger.error(s"Uncaught exception processing transaction-search indices", e)
          nonBYOKFailedTxns.map(_.messageId) //marking all successfully parsed sqs messages as failed
      }
      case false => Future.successful(Seq.empty[MessageId])
    }

    //velocity
    val velocityIndexerResult = velocitySearchProcessor.process(unencryptedBatchUpdated).map {
      case Seq() =>
        logger.debug(s"bulk velocity indexing was successful")
        Seq.empty[MessageId]
      case failedIds =>
        failedIds.foreach { fail =>
          logger.error(s"bulk transaction indexing failed for ${fail.transactionId} due to ${fail.reason}")
        }

        val (toS3, toSqs) = failedIds.partition(bif => bif.reason.contains("MaxBytesLengthExceededException"))
        toS3.foreach { bif =>
          unencryptedBatch.get(bif.transactionId).map {
            trx => parseFailStore.storeTransaction(trx)
          }
        }
        val messageIds = nonBYOKFailedTxns.map(e => e.unencryptedTransaction.transactionId -> e.messageId).toMap
        toSqs.flatMap(id => messageIds.get(id.transactionId))
    }.recover {
      case e: Exception =>
        logger.error(s"Uncaught exception processing velocity-search indices", e)
        nonBYOKFailedTxns.map(_.messageId) //marking all successfully parsed sqs messages as failed
    }

    Future.reduce(Array(trxSearchIndexerResult, velocityIndexerResult))(_ ++ _).map {
      rs =>
        rs ++ failed
    }.map { a =>
      if (a.isEmpty)
        logger.debug(s"Successfully processed a batch ${messages.size}")
      else {
        logger.info(s"returning ${a.size} messages back to sqs")
        metrics.count("transactions.failed.messages", a.size)
      }
      FailedMessageIds(a)
    }
  }

  private def fixAccountId(txn: SqsApiTransaction)(implicit trxId: TrxId): Future[SqsApiTransaction] = {
    if (txn.accountId == 0 && !Option(txn.response).exists(_.contains(TransactionProcessor.noAccountAssociatedMessage))) {
      accountIdResolver.resolve(accountId = Some(AccountId(txn.accountId)),
          apiKeyString = Some(ApiKeyString(txn.apiKey)))
        .map {
          case Some(accountId) =>
            transactionLogger.info(s"Resolved account id: ${accountId.value}")
            txn.copy(
              accountId = accountId.value
            )
          case None => txn
        }
        .recoverWith {
          case e: Throwable =>
            transactionLogger.error("Unable to lookup account id for transaction", e)
            Future.failed(e)
        }
    } else
      Future.successful(txn)
  }


  private def maskPii(apiTransaction: SqsApiTransaction): Future[SqsApiTransaction] = {
    metrics.timeFuture("mask_call.duration") {
      piiMaskService.mask(apiTransaction)
    }
  }

  private def encryptTransaction(apiTransaction: SqsApiTransaction)(implicit trxId: TrxId): Future[SqsApiTransaction] = {
    apiTransaction match {
      case txn if txn.accountId == 0 && Option(txn.response).exists(_.contains(TransactionProcessor.noAccountAssociatedMessage)) =>
        if (transactionLogger.isDebugEnabled) transactionLogger.debug("Not encrypting message since it's not associated to any account")
        Future.successful(apiTransaction)
      case _ => metrics.timeFuture("encrypt_call.duration") {
        sqsAuditDataEncryptionService.encryptTransaction(apiTransaction)
      }
    }
  }

  private def stripUtf(txn: SqsApiTransaction): SqsApiTransaction = {
    txn.copy(
      parameters = if (txn.parameters != null) txn.parameters.replaceAll("[^\\u0000-\\uFFFF]", "\uFFFD") else txn.parameters,
      debug = if (txn.debug != null) txn.debug.replaceAll("[^\\u0000-\\uFFFF]", "\uFFFD") else txn.debug,
      response = if (txn.response != null) txn.response.replaceAll("[^\\u0000-\\uFFFF]", "\uFFFD") else txn.response
    )
  }

  private def saveBatch(apiTransactions: Seq[SqsApiTransaction]): Future[Seq[InsertResultVerbose]] = {
    if (transactionLogger.isDebugEnabled)
      apiTransactions.foreach {
        tx =>
          implicit val trxId: TrxId = TrxId(tx.transactionId)
          transactionLogger.debug("Saving transaction %s".format(tx.transactionId))
      }
    Dao.insertAPITransactions(apiTransactions)
  }

  private def saveBatchDynamo(apiTransactions: Seq[SqsApiTransaction]): Future[Seq[DynamoTransactionDao.InsertResult]] = {
    if (transactionLogger.isDebugEnabled)
      apiTransactions.foreach { tx =>
        implicit val trxId: TrxId = TrxId(tx.transactionId)
        transactionLogger.debug("Saving transaction %s to dynamo".format(tx.transactionId))
      }
    dynamoTransactionDao.insertAPITransactions(apiTransactions)
  }

  private def buildBatchSQSAPITransaction(txn: SqsApiTransaction,
                                          messageId: MessageId,
                                          messageAttributes: JMap[String, MessageAttributeValue]
                                         ): BatchSqsApiTransaction = {
    val modifiedTxn = Option(txn.apiName) match {
      case Some(_) => txn
      case None =>
        metrics.increment("msg.apiName.null")
        txn.copy(apiName = nullApiName)
    }
    val dequeuedFromBYOKFailureQueue = messageAttributes.containsKey("publishedToBYOKQueue")
    BatchSqsApiTransaction(
      messageId = messageId,
      transaction = modifiedTxn,
      unencryptedTransaction = modifiedTxn,
      enqueuedToBYOKFailureQueue = false,
      dequeuedFromBYOKFailureQueue = dequeuedFromBYOKFailureQueue)
  }

  private def handleBYOKException(bTrx: BatchSqsApiTransaction,
                                  ex: BYOKException
                                 ): Future[Either[MessageId, BatchSqsApiTransaction]] = {
    if (bTrx.dequeuedFromBYOKFailureQueue) { // Dequeued from BYOK failure queue, do not delete.
      Future.successful(Left(bTrx.messageId))
    } else {
      byokFailureQueuePublisher.sendToQueue(bTrx.transaction, ex)
        .map(_ => Right(bTrx.copy(enqueuedToBYOKFailureQueue = true))) // Published to BYOK failure queue, can delete from txn-audit queue.
        .recover {
          case _ => Left(bTrx.messageId) // Failed to publish to BYOK failure queue, do not delete from txn-audit queue.
        }
    }
  }

  private def getApiTransactionAuditSeq(encryptedBatch: Seq[SqsApiTransaction]): Seq[ApiTransactionAuditData] = {
    encryptedBatch map { sqsApiTransaction =>
      ApiTransactionAuditData(
        accountId = sqsApiTransaction.accountId,
        transactionId = sqsApiTransaction.transactionId,
        errorMsg = sqsApiTransaction.errorMsg,
        parameters = sqsApiTransaction.parameters,
        response = sqsApiTransaction.response,
        debug = if (null != sqsApiTransaction.debug) ValueEncoder.encodeAsIso88591(sqsApiTransaction.debug) else null,
        details = sqsApiTransaction.details,
        reasonCodes = sqsApiTransaction.reasonCodes,
        requestURI = sqsApiTransaction.requestURI,
        internalWorkLogs = sqsApiTransaction.internalWorkLogs,
        parentTxnId = sqsApiTransaction.parentTxnId
      )
    }
  }

  private def removeLongTextFields(encryptedBatch0: Seq[SqsApiTransaction]): Seq[SqsApiTransaction] = {
    encryptedBatch0.map { sqsApiTransaction =>
      sqsApiTransaction.copy(
        errorMsg = None,
        parameters = null,
        response = null,
        debug = null,
        details = null,
        reasonCodes = null,
        requestURI = null
      )
    }
  }

  private def saveTransactionAuditData(apiTransactionAuditSeq: Seq[ApiTransactionAuditData]) = {
    Future.sequence(apiTransactionAuditSeq.map(transactionAuditDataStorage.storeTransactionAudit))
  }
}

object TransactionProcessor {
  val noAccountAssociatedMessage: String = "NoAccountAssociatedException"
}
