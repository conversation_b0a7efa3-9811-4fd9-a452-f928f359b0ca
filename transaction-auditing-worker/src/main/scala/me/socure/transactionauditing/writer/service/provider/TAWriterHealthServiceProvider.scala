package me.socure.transactionauditing.writer.service.provider

import com.typesafe.config.Config
import me.socure.common.healthcheck.checker.HealthCheckProcessorBasedHealthChecker
import me.socure.common.healthcheck.jvm.JvmHealthCheckProcessorFactory
import me.socure.common.jettythreadpool.factory.JettyThreadPoolFactory
import me.socure.support.timeout.ApiTimeout
import me.socure.transactionauditing.writer.service.TAWriterHealthService

import java.util.concurrent.{ThreadPoolExecutor, TimeUnit}
import javax.inject.{Inject, Provider}
import scala.collection.JavaConverters._
import scala.concurrent.ExecutionContext
import scala.concurrent.duration.{Duration, FiniteDuration}

class TAWriterHealthServiceProvider @Inject()(
                                               config: Config,
                                               executor: ThreadPoolExecutor
                                             )(implicit ec: ExecutionContext) extends Provider[TAWriterHealthService] {
  override def get(): TAWriterHealthService = {
    val port = config.getInt("server.port")
    val healthCheckSettings = config.getConfig("healthcheck.thresholds")
      .entrySet()
      .asScala.map(r => r.getKey -> r.getValue.unwrapped().toString)
      .toMap

    val threadPool = JettyThreadPoolFactory.get(executor)

    val healthCheckProcessor = JvmHealthCheckProcessorFactory
      .create(
        thresholds = healthCheckSettings
      )

    val healthChecker = new HealthCheckProcessorBasedHealthChecker(
      healthCheckProcessor = healthCheckProcessor
    )
    val timeoutConfig = config.getString("server.apiTimeout")
    implicit val apiTimeout: ApiTimeout = ApiTimeout(Duration(timeoutConfig) match {
      case d if d.isFinite() => FiniteDuration(d.toMillis, TimeUnit.MILLISECONDS)
      case d => throw new IllegalStateException(s"Expected a finite duration for server.apiTimeout but found $d")
    })

    new TAWriterHealthService(
      threadPool = threadPool,
      port = port,
      healthChecker = healthChecker
    )
  }
}
