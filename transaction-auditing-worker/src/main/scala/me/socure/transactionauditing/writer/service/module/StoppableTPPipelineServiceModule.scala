package me.socure.transactionauditing.writer.service.module

import com.google.inject.name.Names
import com.google.inject.{AbstractModule, Provides}
import com.typesafe.config.Config
import me.socure.common.metrics.JavaMetricsFactory
import me.socure.common.sqs.sns.v2.extended.common.{Configuration, Constants, S3Wrapper}
import me.socure.common.sqs.v2.SqsMessagesDeleter
import me.socure.common.sqs.v2.extended.ExtendedSqsAsyncClient
import me.socure.common.uuid.RandomUuidProvider
import me.socure.thirdpartyaudit.worker.client.{ThirdPartyAuditingWorkerClient, ThirdPartyAuditingWorkerClientFactory}
import me.socure.transaction.audit.common.model.StoppableService
import me.socure.transactionauditing.writer.service.PipelineService
import me.socure.transactionauditing.writer.service.provider.StoppableTPPipelineServiceProvider
import me.socure.types.scala.batch.BatchConf
import net.codingwell.scalaguice.ScalaModule
import org.slf4j.LoggerFactory
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3AsyncClient
import software.amazon.awssdk.services.sqs.SqsAsyncClient
import software.amazon.awssdk.services.sqs.model.{GetQueueUrlRequest, ReceiveMessageRequest}

import java.nio.file.Paths
import java.util.concurrent.TimeUnit
import javax.inject.{Named, Singleton}
import scala.concurrent.ExecutionContext
import scala.concurrent.duration.Duration

class StoppableTPPipelineServiceModule extends AbstractModule with ScalaModule {
  override def configure(): Unit = {
    bind(classOf[StoppableService])
      .annotatedWith(Names.named("thirdparty"))
      .toProvider(classOf[StoppableTPPipelineServiceProvider]).asEagerSingleton()

  }

  @Provides
  @Singleton
  @Named("tpBaseSqsClient")
  def getBaseClient(config: Config): SqsAsyncClient = {
    val region = config.getString("pipeline.region")
    SqsAsyncClient.builder().region(Region.of(region)).build()
  }

  @Provides
  @Named("thirdpartySqsConfig")
  def getExtendedSqsConfig(config: Config): Configuration = {
    val s3Details = config.getConfig("pipeline.thirdparty.sqs")
    Configuration(
      s3BucketName = s3Details.getString("large.files.s3.bucket.name"),
      basePath = if (s3Details.hasPath("base.path")) Option(s3Details.getString("base.path")).map(Paths.get(_)) else None,
      messageSizeThreshold = if (s3Details.hasPath("message.size.threshold")) s3Details.getInt("message.size.threshold") else Constants.DefaultMessageSizeThreshold
    )
  }

  @Provides
  @Singleton
  @Named("tpS3Wrapper")
  def getWrappedS3Client: S3Wrapper = S3Wrapper(S3AsyncClient.create())

  @Provides
  @Named("tpIgnoreS3NoSuchKey")
  def getIgnoreS3NoSuchKeyFlag: Boolean = true

  @Provides
  @Singleton
  @Named("thirdpartyExtendedSqsClient")
  def getExtendedSqsAsyncClient(@Named("tpBaseSqsClient") baseClient: SqsAsyncClient,
                                @Named("tpS3Wrapper") s3Client: S3Wrapper,
                                @Named("thirdpartySqsConfig") extendedSqsConf: Configuration,
                                @Named("tpIgnoreS3NoSuchKey") ignoreS3NoSuchKey: Boolean): ExtendedSqsAsyncClient = {
    new ExtendedSqsAsyncClient(baseClient, s3Client, extendedSqsConf, ignoreS3NoSuchKey, uuidProvider = RandomUuidProvider)
  }

  @Provides
  @Named("thirdpartyQueueUrl")
  def getTransactionQueue(config: Config, @Named("thirdpartyExtendedSqsClient") client: ExtendedSqsAsyncClient): String = {
    val queueName = config.getString("pipeline.thirdparty.sqs.queue.name")
    val getQueueUrlRequest = GetQueueUrlRequest.builder().queueName(queueName).build()
    client.getQueueUrl(getQueueUrlRequest).get(5, TimeUnit.SECONDS).queueUrl()
  }

  @Provides
  @Named("tpSqsReadBatch")
  def getReceiveMesageCount(config: Config): Int = {
    val receiveMessageCount = getInt(config, "pipeline.thirdparty.sqs.receive.message.count").getOrElse(10)
    if (receiveMessageCount > 10) {
      throw new IllegalStateException("No more than 10 messages can be requested at a time")
    }
    receiveMessageCount
  }

  @Provides
  @Named("tpSqsDeleteRetries")
  def getDeleteRetries(config: Config): Int = {
    getInt(config, "pipeline.thirdparty.sqs.delete.messages.max.retries").getOrElse(3)
  }

  @Provides
  @Named("thirdpartySqsMsgDeleter")
  @Singleton
  def getTransactionMessageDeleter(
                                    @Named("thirdpartyExtendedSqsClient") client: ExtendedSqsAsyncClient,
                                    @Named("thirdpartyQueueUrl") queueUrl: String,
                                    @Named("tpSqsDeleteRetries") deleteMaxRetries: Int)
                                  (implicit ec: ExecutionContext): SqsMessagesDeleter = {

    new SqsMessagesDeleter(
      client = client,
      queueUrl = queueUrl,
      maxRetries = deleteMaxRetries,
      metrics = JavaMetricsFactory.get("transaction.auditing.writer.tp.sqs.delete.messages"),
      logger = LoggerFactory.getLogger("transaction.auditing.writer.tp.sqs.delete.messages")
    )
  }

  @Provides
  @Named("thirdpartyReceiveMessage")
  @Singleton
  def getTransactionReceiveMessage(config: Config,
                                   @Named("thirdpartyQueueUrl") queueUrl: String,
                                   @Named("tpSqsReadBatch") receiveMessageCount: Int): ReceiveMessageRequest = {
    ReceiveMessageRequest
      .builder()
      .queueUrl(queueUrl)
      .maxNumberOfMessages(receiveMessageCount)
      .visibilityTimeout(config.getInt("pipeline.thirdparty.sqs.visibility.timeout.seconds"))
      .build()
  }

  @Provides
  @Named("thirdpartyConsumerCount")
  def getParallelConsumers(config: Config): Int = {
    if (config.hasPath("pipeline.thirdparty.parallelism"))
      config.getInt("pipeline.thirdparty.parallelism")
    else
      config.getInt("pipeline.parallelism")
  }

  @Provides
  @Named("thirdpartySqsBatchConfig")
  def getTransactionBatchConf(config: Config): Option[BatchConf] = {
    if (config.hasPath("pipeline.buffering")) {
      Some(BatchConf(
        targetCount = config.getInt("pipeline.buffering.targetCount"),
        maxDuration = Duration(config.getString("pipeline.buffering.maxDuration"))
      ))
    } else None
  }

  @Provides
  @Named("thirdpartyRestartIntervalInMs")
  def getThirdpartyRestartIntervalInMs(config: Config): Long = {
    if (config.hasPath("pipeline.thirdparty.restart.interval")) {
      val restartCfg = config.getString("pipeline.thirdparty.restart.interval")
      Duration(restartCfg).toMillis
    } else {
      Duration("24 hours").toMillis
    }
  }

  @Provides
  @Singleton
  @Named("thirdpartyPipeline")
  def getTransactionPipeline(@Named("thirdparty") pipeline: StoppableService)(implicit ec: ExecutionContext): PipelineService = {
    new PipelineService(pipeline)
  }

  @Provides
  @Singleton
  def tpAuditingWorkerClient(config: Config)
                            (implicit ec: ExecutionContext): ThirdPartyAuditingWorkerClient = {
    val auditDataSqsConfig = config.getConfig("pipeline.thirdparty.sqs.auditdata.sqs")
    ThirdPartyAuditingWorkerClientFactory.create(auditDataSqsConfig)
  }

  private def getInt(conf: Config, path: String): Option[Int] = {
    if (conf.hasPath(path)) Option(conf.getInt(path)) else None
  }
}
