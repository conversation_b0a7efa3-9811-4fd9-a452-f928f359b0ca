package me.socure.transactionauditing.writer.service.obfuscator

import me.socure.transactionauditing.writer.service.obfuscator.RichImplementations._
import org.json4s.Formats
import org.json4s.JsonAST.{JArray, JDouble, JString, JValue}
import org.json4s.jackson.JsonMethods.parse
import org.json4s.jackson.Serialization
import org.slf4j.LoggerFactory

import java.net.{URI, URLDecoder, URLEncoder}
import java.nio.charset.StandardCharsets
import scala.collection.mutable
import scala.util.{Failure, Success, Try}
import scala.xml.transform.{RewriteRule, RuleTransformer}
import scala.xml._

abstract class PiiObfuscator[T](inputParameters: Seq[String], outputParameters: Seq[String]) {

  private val logger = LoggerFactory.getLogger(getClass)

  implicit val formats: Formats = org.json4s.DefaultFormats

  val providedPlaceholder: String = "[PROVIDED]"

  val xmlTransformer = new RuleTransformer(new RewriteRule {
    override def transform(n: Node): Seq[Node] = n match {
      case elem@Elem(prefix, label, attribs, scope, _) if inputParameters.contains(elem.label) =>
        val updatedAttr = if (attribs.nonEmpty) {
          attribs match {
            case PrefixedAttribute(str, str1, _, data) =>
              Attribute(str, str1, Text(providedPlaceholder), data)
            case UnprefixedAttribute(str, _, data) =>
              Attribute(str, Text(providedPlaceholder), data)
          }
        } else {
          attribs
        }
        Elem(prefix, label, updatedAttr, scope, false, Text(providedPlaceholder))
      case other => other
    }
  })

  private def jsonStrToMap(jsonStr: String): Map[String, Any] = {
    parse(jsonStr).extractOpt[Map[String, Any]].getOrElse(Map.empty)
  }

  private def mapToJsonStr(map: Map[String, Any]): String = {
    Serialization.write(map)
  }

  def maskParameters(parametersJson: String, isDbValue: Boolean): String = {
    val parametersMap = jsonStrToMap(parametersJson)
    val maskedResult = parametersMap.foldLeft[Map[String, Any]](Map.empty) {
      case (map, (paramName, paramValue)) =>
        if (paramName != null && paramName.toString.trim.nonEmpty &&
          inputParameters.contains(paramName.trim()) && paramValue != null
          && tryUrlDecode(paramValue.toString).trim.nonEmpty) {
          if (isDbValue) {
            val maskedValue = paramValue match {
              case _: Long => -1
              case _ => providedPlaceholder
            }
            map + (paramName -> maskedValue)
          } else {
            map + (paramName -> providedPlaceholder)
          }
        } else {
          map + (paramName -> paramValue)
        }
    }

    mapToJsonStr(maskedResult)
  }

  private def tryUrlDecode(s: String): String = {
    Try {
      //replace non-encoded '%' and '+' to encoded values
      val cleanS = s.replaceAll("%(?![0-9a-fA-F]{2})", "%25").replaceAll("\\+", "%2B")
      URLDecoder.decode(cleanS, StandardCharsets.UTF_8.name())
    } match {
      case Success(decodedStr) => decodedStr
      case Failure(ex) => throw ex
    }
  }

  def maskRequestUri(requestUri: String): String = {
    val cleanedUri = requestUri.replace(" ", "")
    val uri = URI.create(cleanedUri)
    val rawQuery = uri.getRawQuery
    if(null != rawQuery) {
      val queryParams: Seq[(String, Option[String])] = uri.getRawQuery.split("&").toList.map(_.split("=")).map(arr => arr(0) -> arr.lift(1))
      val maskedQueryParams = queryParams.map { case (queryParam, paramValueOpt) =>
        val str = paramValueOpt.map(URLDecoder.decode(_, StandardCharsets.UTF_8.name())).getOrElse("")
        if (inputParameters.contains(queryParam.trim.toLowerCase) && str.trim.nonEmpty) {
          queryParam -> providedPlaceholder
        } else {
          val maskedValue = if (str.startsWith("{") || str.startsWith("[\"")) {
            URLEncoder.encode(internalJson(str), StandardCharsets.UTF_8.name())
          } else if (str.startsWith("<")) {
            URLEncoder.encode(updateXml(str), StandardCharsets.UTF_8.name())
          } else {
            if (inputParameters.contains(queryParam)) providedPlaceholder else str
          }
          queryParam -> maskedValue
        }
      }

      val maskedQueryStr = maskedQueryParams.map { case (paramKey, paramValue) =>
        s"$paramKey=$paramValue"
      }.mkString("&")

      val maskedUriSb = new mutable.StringBuilder()
      if (null != uri.getScheme) maskedUriSb.append(uri.getScheme + "://")
      if (null != uri.getHost) maskedUriSb.append(uri.getHost)
      if (uri.getPort > 0) maskedUriSb.append(uri.getPort)
      if (maskedQueryParams.nonEmpty) maskedUriSb.append("?").append(maskedQueryStr)

      URI.create(maskedUriSb.toString).toString
    } else requestUri
  }


  def updateJson(json: JValue, piiKey: String): JValue = {
    json.mapField {
      case (key, value) if key == piiKey =>
        value match {
          case a: JString =>
            val str = a.extract[String]
            if (str.startsWith("<") && str.endsWith(">"))
              (key, JString(updateXml(str)))
            else
              (key, JString(providedPlaceholder))
          case _: JArray =>
            val res = value match {
              case JArray(xs) => JArray(xs.map(_ => JString(providedPlaceholder)))
              case x => x
            }
            (key, res)
          case _: JDouble =>
            (key, JString(providedPlaceholder))
          case _ =>
            (key, value)
        }
      case (k, v) =>
        v match {
          case a: JString =>
            val str = a.extract[String]
            if (str.startsWith("<") && str.endsWith(">"))
              (k, JString(updateXml(str)))
            else
              (k, a)
          case _ => (k, v)
        }
    }
  }

  def updateXml(xmlStr: String): String = {
    try {
      val xmlElem = scala.xml.XML.loadString(xmlStr.replace("\\\"", "\""))
      xmlTransformer(xmlElem).buildString(false)
    } catch {
      case e: SAXParseException =>
        logger.error(s"XML error for $xmlStr, $e")
        xmlStr
    }
  }

  def debugUpdateJson(jsonData: JValue): JValue = {
    if (jsonData.has("rule_codes")) {
      val ruleCodes = jsonData \\ "rule_codes"

      val masked = outputParameters.foldLeft(jsonData)(updateJson)

      masked.transformField {
        case ("rule_codes", _) => ("rule_codes", ruleCodes)
        case (k, v) => (k, v)
      }
    } else {
      jsonData
    }
  }

  private def internalJson(str: String): String = {
    val json = parse(str)
    val opStr = inputParameters.foldLeft(json)(updateJson)
    Serialization.write(opStr)
  }

  def mask(trx: T) : T

}
