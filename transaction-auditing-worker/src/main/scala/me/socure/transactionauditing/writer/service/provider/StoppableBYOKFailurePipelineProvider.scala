package me.socure.transactionauditing.writer.service.provider

import java.util.concurrent.atomic.AtomicBoolean
import com.google.inject.Provider

import javax.inject.{Inject, Named}
import me.socure.common.metrics.JavaMetricsFactory
import me.socure.common.sqs.v2.{MessagesProcessor, PeriodicRestartPipelineService, Pipeline, SqsMessagesDeleter}
import me.socure.common.sqs.v2.extended.ExtendedSqsAsyncClient
import me.socure.transaction.audit.common.model.{CompositeStoppableServices, StoppableService}
import me.socure.transactionauditing.writer.service.{StoppablePipelineService, StoppablePipelineServiceWithPeriodicRestart}
import me.socure.types.scala.batch.BatchConf
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.{Message, ReceiveMessageRequest}

import scala.concurrent.ExecutionContext

class StoppableBYOKFailurePipelineProvider @Inject()(
                                                      @Named("byokFailureConsumerCount") parallelism: Int ,
                                                      @Named("byokFailureRestartIntervalInMs") pipelineRestartIntervalInMs: Long,
                                                      @Named("byokFailureExtendedSqsClient") client: ExtendedSqsAsyncClient,
                                                      @Named("byokFailureSqsBatchConfig") batchConfOpt: Option[BatchConf],
                                                      @Named("byokFailureReceiveMessageRequest") receiveMessageRequest: ReceiveMessageRequest,
                                                      @Named("transactionMessageProcessor") messageProcessor: MessagesProcessor[Message],
                                                      @Named("byokFailureSqsMsgDeleter") sqsMessageDeleter: SqsMessagesDeleter
                                                    )(implicit ec: ExecutionContext)
  extends Provider[StoppableService] {

  private val logger = LoggerFactory.getLogger(getClass)

  override def get(): StoppableService = {
    val pipeline = Pipeline(
      client = client,
      req = receiveMessageRequest,
      messagesProcessor = messageProcessor,
      batchConf = batchConfOpt,
      sqsMessagesDeleter = sqsMessageDeleter,
      autoDeleteMessages = true,
      proceed = { ex: Throwable =>
        logger.error("Pipeline failed unexpectedly", ex)
        true // proceed on unknown failures
      },
      ignoreEmpty = true,
      metrics = JavaMetricsFactory.get("transaction.auditing.writer.byok-failure.sqs.pipeline"),
      logger = LoggerFactory.getLogger("transaction.auditing.writer.byok-failure.sqs.pipeline")
    )

    if (parallelism == 1) {
      val periodicRestartPipeline = new PeriodicRestartPipelineService(pipeline, pipelineRestartIntervalInMs)
      new StoppablePipelineServiceWithPeriodicRestart(periodicRestartPipeline)
    } else {
      new CompositeStoppableServices(
        (1 to parallelism).toSet[Int].map { _ =>
          val newPipeline = pipeline.copy(isRunningState = new AtomicBoolean())
          val periodicRestartPipeline = new PeriodicRestartPipelineService(newPipeline, pipelineRestartIntervalInMs)
          new StoppablePipelineServiceWithPeriodicRestart(periodicRestartPipeline)
        }
      )
    }
  }

}
