package me.socure.transactionauditing.writer.service

import com.google.inject.Guice
import me.socure.common.guice.module.CommonModule
import me.socure.common.microservice.defaults.DefaultMicroservice
import me.socure.transaction.audit.common.modules.TransactionAuditCommonModules
import me.socure.transactionauditing.writer.service.module.{StoppableBYOKFailurePipelineModule, StoppablePipelineServiceModule}
import net.codingwell.scalaguice.InjectorExtensions._
import org.slf4j.LoggerFactory

import scala.util.{Failure, Success, Try}

object Main extends DefaultMicroservice {

  override def startUp(args: Array[String]): Unit = {
    val logger = LoggerFactory.getLogger(getClass)

    Try {
      val injector = Guice.createInjector(
        new CommonModule,
        new TransactionAuditCommonModules,
        new StoppablePipelineServiceModule,
        new StoppableBYOKFailurePipelineModule
      )

      val service = injector.instance[TAWriterService]
      service.startService

    } match {
      case Success(_) =>
      case Failure(ex) =>
        logger.error("Unable to start transaction-auditing-writer-service", ex)
        ex.printStackTrace()
        System.exit(1)
    }

  }
}
