package me.socure.transactionauditing.writer.service

import com.google.common.util.concurrent.ServiceManager.Listener
import com.google.common.util.concurrent.{MoreExecutors, Service, ServiceManager}
import com.typesafe.config.Config
import org.slf4j.LoggerFactory

import java.util.concurrent.TimeUnit
import javax.inject.{Inject, Named}
import scala.collection.JavaConverters._

class TAWriterService @Inject()(
                                 config: Config,
                                 @Named("transactionPipeline") pipelineService: PipelineService,
                                 @Named("byokFailurePipeline") pipelineServiceByokFailure: PipelineService,
                                 healthService: TAWriterHealthService,
                                 dbMigration: DBMigrator
                               ) {

  private val logger = LoggerFactory.getLogger(getClass)

  def startService: ServiceManager = {
    dbMigration.migrate

    val services: Set[Service] = Set(pipelineService, pipelineServiceByokFailure, healthService)
    val manager: ServiceManager = new ServiceManager(services.asJava)

    manager.addListener(new Listener() {
      override def healthy(): Unit = {
        logger.info(s"services are healthy.")
      }

      override def stopped(): Unit = {
        logger.info(s"services have stopped.")
      }

      override def failure(service: Service): Unit = {
        logger.info(s"service ${service.getClass} is failed.")
      }
    }, MoreExecutors.directExecutor())

    sys.addShutdownHook {
      manager.stopAsync().awaitStopped(20, TimeUnit.SECONDS)
    }

    manager.startAsync()

  }

}
