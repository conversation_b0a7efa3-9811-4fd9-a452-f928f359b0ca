withDBMigration=false

threadpool {
  poolSize=60
}

pipeline {

  region = "us-east-1"

  //how many workers will be processing in parallel
  parallelism = 3

#  buffering {
#    //try to buffer upto this count. It could go upto targetCount + (receive.message.count)
#    targetCount = 10 //waits upto maxDuration to receive targetCount, it could be less, exact or more
#    //how long to wait for the targetCount
#    maxDuration = "1 minutes"
#  }

  transaction {
    parallelism = 2
    restart.interval = "30 minutes"
    sqs {
      queue.name = "transaction-auditing-dev"
      receive.message.count = 10
      visibility.timeout.seconds = "900" //15 minutes
      large.files.s3.bucket.name = "sqs-storage-dev-************-us-east-1"
      s3.masking-failures.bucket = "dev-audit-errors-************-us-east-1/pii_mask_failures"
      s3.parsing-failures.bucket = "dev-audit-errors-************-us-east-1/parse_failures"
      s3.failures.bucket.key-arn = "arn:aws:kms:us-east-1:************:key/mrk-f7533e358fbe4b778086164272468e47"
      s3.audit-data.bucket = "transaction-audit-data-************-us-east-1"
    }
  }

  thirdparty {
    parallelism = 20
    restart.interval = "30 minutes"
    sqs {
      queue.name = "third-party-transaction-auditing-dev"
      receive.message.count = 10
      visibility.timeout.seconds = "900" //15 minutes
      large.files.s3.bucket.name = "thirdparty-stats-dev-************-us-east-1"
      large.files.s3.bucket.key-arn = "arn:aws:kms:us-east-1:************:key/mrk-ee5d7d90b06549bea1ddfe793962125f"

      auditdata.sqs {
        queue.name = "thirdparty-audit-data-dev"
        largepayload.s3.bucket.name = "thirdparty-stats-dev-************-us-east-1"
        largepayload.s3.bucket.key-arn = "arn:aws:kms:us-east-1:************:key/mrk-ee5d7d90b06549bea1ddfe793962125f"
      }
    }
  }

  byok-failure {
    parallelism = 1
    restart.interval = "30 minutes"
    sqs {
      queue.name = "transaction-auditing-byok-failures-dev"
      receive.message.count = 10
      visibility.timeout.seconds = "900" // 15 minutes
      large.files.s3.bucket.name = "transaction-auditing-byok-failures-dev-************-us-east-1"
    }
  }
}

server {
  port=5000
  apiTimeout = "10 minutes"
}

cors.allowedDomains = ["http://swagger.us-east-vpc.socure.be/"]

healthcheck.thresholds {
  memory {
    heap.used_max_percentage = "<80.0" //should be less than 90%
    non_heap.used_max_percentage = "<80.0" //should be less than 90%
  }

  thread {
    deadlock.count = "<=0.0"
    monitor.deadlock.count = "<=0.0"
  }
}

database {
  audit {
    unified-cluster {
      jdbcUrl="***********************************************************************************************************************************************************************************************************************************************************************************************"
      dataSource.user="socureaudit"
      dataSource.password="lS@0*DMbXiLKdSCG8zdN"
      driverClassName="com.mysql.cj.jdbc.Driver"
      poolName="ta_r_u_trx"
      readOnly=false
      maximumPoolSize=25
    }
  }
}

account.service {
  endpoint = "https://account-service.webapps.us-east-1.product-dev.socure.link"
  endpoint2 = "https://account-service.webapps.us-east-1.product-dev.socure.link"
  groupName = "UXServicesRamp"
  flagName = "AccountService_Ramp"

  dynamic.control.center{
    s3 {
      bucketName="globalconfig-************-us-east-1"
    }
    memcached {
      host="localhost"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }

  hmac {
    realm = Socure
    version = "1.0"
    secret.key="""ENC(0D+7rTd1tvMY19QuV+pXfuLd5We+Rb9kFtIdh/QuV2UrkPORFU3MbV+3DbKqdaIAgnBEFheof+Hr4PpCP8ukww==)"""
    ttl=5
    time.interval=5
    strength=512
  }
}

memcached {
  host=localhost
  port=11211
  ttl=86400
}

client.specific.encryption {
  encryption.context.account_id.key = "socure_account_id"
  kms.ids {
    "us-east-1" = """arn:aws:kms:us-east-1:************:alias/client-specific-encryption-stage"""
    "us-west-1" = """arn:aws:kms:us-west-1:************:alias/client-specific-encryption-stage"""
  }
  kms.cache.time.seconds=604800
}

#============= Mailgun config =============#
mailgun {
  endpoint = "https://api.mailgun.net/v2/socure.com/messages"
  key = """ENC(ibSiwI/xh3BYUTxuV1A8DrltfPfAx3SgHhcwQjcH2wYuQ8vMvR6xRdxBYFy5iw+FMNRFFdYiskBLqvRnzn0gSA==)"""
  domain = "socure.com"
  subject = "Model Monitoring Metrics Reporting Failure - Stage"
  from = "<EMAIL>"
  to = ["<EMAIL>"]
}
#============= Mailgun config =============#

hmac {
  secret.key="""ENC(uFkhQj790R7bJVxuA9DM/R4loXYHkWblvNfLTWMflvB9slccns5nZQAeYW3HZYCH)"""
  ttl=5
  time.interval=5
  strength=512
}

dynamic.control.center{
  s3 {
    bucketName="globalconfig-************-us-east-1"
  }
  memcached {
    host="localhost"
    port=11211
    ttl=86400
  }
  local.cache {
    timeout.minutes=2
  }
}

transaction.search {
  endpoint = "https://transaction-search-service.webapps.us-east-1.product-dev.socure.link/"
  endpoint2="https://transaction-search-service.webapps.us-east-1.product-dev.socure.link/"
  groupName="UXServicesRamp"
  flagName="TransactionSearchService_Ramp"
  hmac {
    realm = Socure
    version = "1.0"
    strength = 512
    aws.secrets.manager.id = "transaction-search-service/dev/hmac/indexing-3b4caf7f0d"
    secret.refresh.interval = 5000
  }
}

velocity.search {
  endpoint = "https://velocity-search-service.webapps.us-east-1.product-dev.socure.link"
  endpoint2 = "https://velocity-search-service.webapps.us-east-1.product-dev.socure.link"
  hmac {
    realm = Socure
    version = "1.0"
    strength = 512
    aws.secrets.manager.id = "velocity-search-service/dev/hmac/indexing"
    secret.refresh.interval = 5000
  }

  hmac2 {
    realm = Socure
    version = "1.0"
    strength = 512
    aws.secrets.manager.id = "velocity-search-service/dev/hmac/indexing"
    secret.refresh.interval = 5000
  }

  dynamic.control.center{
    s3 {
      bucketName="globalconfig-************-us-east-1"
    }
    memcached {
      host="localhost"
      port=11211
      ttl=86400
    }
    local.cache {
      timeout.minutes=2
    }
  }
}

device.jwt.token {
  key = "ENC(dmQGk9hm8v4hjg4HbPs41+nhKZBTjpwnyOZeinanCBQx2Fgdw2e4pfqa9C46aY4+)"
}

obfuscate.pii {
  accounts = [1552,3954,7425,1506,4316,16325]
}

dynamodb {
  enabled = true
  endpoint = "http://localhost:7777"
  tableName = "tbl_api_transaction_dev"
  client.config {
    maxConcurrency = 100
    writeTimeout = 60 # seconds
  }
}



txn.metrics.enabled = false