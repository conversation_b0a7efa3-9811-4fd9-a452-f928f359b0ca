{
  pii {
    transaction {
      inputInfo = ["firstname", "surname", "fullname", "dob", "email", "country", "ipaddress", "mobilenumber",
        "city", "zip", "state", "nationalid", "physicaladdress", "physicaladdress2", "driverlicensestate",
        "companyname", "geocode", "driverlicense", "orderamount", "lastorderdate", "prevordercount"]
      processedInfo = ["firstName", "surName", "fullName", "streetAddress", "city", "score", "address",
        "state", "zip", "dob", "ssn", "firstname", "surname", "streetaddress",
        "name", "email", "riskScores", "name_address_email", "name_phone",
        "name_phone_email", "name_address_phone_email", "name_email", "name_address",
        "name_address_phone", "quantile", "originalScore", "documentNumber", "issueDate", "expirationDate", "mobileNumber"],
    }
    thirdparty {
      inputInfo = ["Address", "Address2", "AddressCMRA", "AddressPOBox", "Age", "AptNbr", "AptType",
        "City", "ComprehensiveVerificationIndex", "DL", "DLValid", "DOB", "DOBMatchLevel", "Day",
        "Description", "DriverLicense", "FName", "First", "FirstName", "FirstNameAllowNickname",
        "HomePhone", "House", "IPAddress", "LName", "Last", "LastName", "Month", "PassportValid",
        "Phone", "PostDir", "Postal", "RiskCode", "SSN", "SSNDeceased", "SSNFoundForLexID", "SSNValid", "ST",
        "State", "StrType", "Street", "StreetAddress1", "StreetAddress2", "Summary", "Year", "Zip", "Zip5",
        "address", "address.city", "address.country_code", "address.postal_code", "address.state_code",
        "address.street_line_1", "addressLine1", "addressLine2", "areaCode", "centralCode", "city",
        "city_name", "code", "companyname", "congressional_district", "consentStatus", "country",
        "countryCode", "country_code", "county", "county_fips", "deducedLocation", "default_city_name",
        "delivery_line_1", "delivery_point", "dob", "eMail", "email", "emailAddress", "email_address",
        "extendedAddress", "familyName", "firstName", "first_name", "fullName", "givenName", "ipaddress",
        "lastName", "last_line", "last_name", "lastorderdate", "latitude", "lineType", "locationGeneral",
        "longitude", "mobileNumber", "mobilenumber", "name", "nationalId", "nationalid", "orderamount",
        "payfoneAlias", "phone", "phoneNumber", "phone_number", "plus4_code", "postalCode", "prevordercount",
        "primary_number", "registered_name", "resident_name", "secondary_designator", "secondary_number",
        "ssn", "state", "state_abbreviation", "std_address", "street", "street2", "streetAddress",
        "street_name", "street_suffix", "subscriber_age_range", "subscriber_name", "surName", "zip",
        "zipCode", "zipareacode", "zipcode"]
      processedInfo = []
    }
  }
}