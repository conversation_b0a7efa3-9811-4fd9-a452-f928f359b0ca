<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>transaction-auditing</artifactId>
        <groupId>me.socure</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>transaction-auditing-worker</artifactId>

    <properties>
        <netty.nio.client.version>${aws.java.sdk.v2.version}</netty.nio.client.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_${sc.ver}</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- SOCURE COMMON -->
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-jmx-service</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-docker-sqs-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-docker-mysql-service</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-docker-client-factory</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-sql</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- AUDIT STORAGE -->
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>transaction-auditing-storage</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>transaction-auditing-queueproc</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>transaction-auditing-services-modules</artifactId>
            <version>${revision}</version>
        </dependency>
        <!-- SOCURE COMMON LIBRARY-->
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-environment</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-c3p0-factory</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-microservice</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-ms-dependencies</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-sqs-v2-extended</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-rds</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-jetty</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-dynamic-control-center-v2</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <!-- Thirdparty Auditing Worker Client -->
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>thirdparty-auditing-worker-client</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
	    <artifactId>mysql-connector-j</artifactId>
	    <version>${mysql.connector.j.version}</version> 
	    <exclusions>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>protobuf-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-dynamic-control-center-v2</artifactId>
            <version>${socure.common.version}</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>netty-nio-client</artifactId>
            <version>${netty.nio.client.version}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>properties-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>test</id>
                        <goals>
                            <goal>test</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>jib-fips</id>
            <activation>
                <property>
                    <name>build-environment</name>
                    <value>gitlab-ci-fips</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>3.2.1</version>
                        <configuration>
                            <allowInsecureRegistries>true</allowInsecureRegistries>
                            <from>
                                <image>${env.JIB_JDK8_FIPS_IMAGE}</image>
                                <platforms>
                                    <platform>
                                        <architecture>amd64</architecture>
                                        <os>linux</os>
                                    </platform>
                                    <platform>
                                        <architecture>arm64</architecture>
                                        <os>linux</os>
                                    </platform>
                                </platforms>
                            </from>
                            <to>
                                <image>fips-registry.us-east-1.build.socure.link/${env.PROJECT_NAME}/${env.SVC_WORKER}</image>
                                <auth>
                                    <username>${env.REGISTRY_USER}</username>
                                    <password>${env.REGISTRY_PASS}</password>
                                </auth>
                                <tags>
                                    <tag>latest-fips</tag>
                                    <tag>${env.FIPS_DOCKER_IMAGE_TAG}</tag>
                                </tags>
                            </to>
                            <container>
                                <mainClass>me.socure.transactionauditing.writer.service.Main</mainClass>
                                <ports>
                                    <port>5000</port>
                                </ports>
                            </container>
                        </configuration>
                        <executions>
                            <execution>
                                <id>build-and-push-docker-image</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>build</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>jib</id>
            <activation>
                <property>
                    <name>build-environment</name>
                    <value>gitlab-ci</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>3.2.1</version>
                        <configuration>
                            <from>
                                <image>${env.JIB_JDK8_IMAGE}</image>
                                <platforms>
                                    <platform>
                                        <architecture>amd64</architecture>
                                        <os>linux</os>
                                    </platform>
                                    <platform>
                                        <architecture>arm64</architecture>
                                        <os>linux</os>
                                    </platform>
                                </platforms>
                            </from>
                            <to>
                                <image>registry.us-east-1.build.socure.link/${env.PROJECT_NAME}/${env.SVC_WORKER}</image>
                                <auth>
                                    <username>${env.REGISTRY_USER}</username>
                                    <password>${env.REGISTRY_PASS}</password>
                                </auth>
                                <tags>
                                    <tag>latest</tag>
                                    <tag>${env.NON_FIPS_DOCKER_IMAGE_TAG}</tag>
                                </tags>
                            </to>
                            <container>
                                <mainClass>me.socure.transactionauditing.writer.service.Main</mainClass>
                                <ports>
                                    <port>5000</port>
                                </ports>
                            </container>
                        </configuration>
                        <executions>
                            <execution>
                                <id>build-and-push-docker-image</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>build</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
