image: registry.us-east-1.build.socure.link/platform/gitlab-runner-maven-openjdk:3.8.6-1

Jib Build and Publish:
  stage: build
  script:
    - mvn -B package -DskipTests=true $MAVEN_EXTRA_ARGS
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

Jib Build:
  stage: build
  script:
    - echo "We need to decide on what we're doing when this is not running on the default branch."
    - echo "Nothing is happening here yet."
  rules:
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH


