stages:
  - build
  - update-config
  - deploy-dev
  - deploy-stage
  - deploy-dr
  - deploy-prod

include:
  # Build and publish Jib artifacts
  - local: 'spec/publish-jib.gitlab-ci.yml'
  # Update microservice configurations
  - local: 'spec/update-mscv.gitlab-ci.yml'
  # Deploy new artifacts via Helm
  - local: 'spec/deploy-helm.gitlab-ci.yml'

# Default job overrides
variables:
  CHART_NAME: socure-backend-service
  CHART_VERSION: 0.5.0
  AWS_REGION: us-east-1

