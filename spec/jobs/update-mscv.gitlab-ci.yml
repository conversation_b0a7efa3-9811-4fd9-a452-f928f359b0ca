### Update Microservice Configurations
# 
# If your application uses the Microservice Configuration bucket pattern, this job will 
# update your application's MSCV and publish it to your S3 buckets.
# 
# Note: This job will fail if the configuration version already exists.
# You must increment this configuration version in your project's .gitlab-ci.yml with changes.
#
# requirements:
#   - The following folder structure in the root of the project
#     - mscv/$ENVIRONMENT/configurations/${AWS_REGION}/${ENVIRONMENT}.conf
#     - mscv/$ENVIRONMENT/logs/${ENVIRONMENT}.groovy
#   - The configuration version as defined by $MSCV_VERISON.
#
# Valid $ENVIRONMENT values are ['dev', 'stage', 'dr', 'prod']
#
# required variables:
#   AWS_ASSUME_ROLE_ARN_{ENVIRONMENT}:
#   MSCV_BUCKET_NAME_{ENVIRONMENT}:
#   MSCV_VERSION:
#   SERVICE_NAME:

stages:
  - update-config

variables:
  SERVICE_NAME: OVERRIDE_ME
  MSCV_VERSION: OVERRIDE_ME

.update_mscv:
  image: registry.us-east-1.build.socure.link/platform/gitlab-runner-aws-cli:1.0.0
  stage: update-config
  before_script: 
    - aws sts get-caller-identity
    # Assume role in the target account
    - |
      export $(printf "AWS_ACCESS_KEY_ID=%s AWS_SECRET_ACCESS_KEY=%s AWS_SESSION_TOKEN=%s" \
        $(aws sts assume-role \
            --role-arn "${AWS_ASSUME_ROLE_ARN}" \
            --role-session-name MySessionName \
            --query "Credentials.[AccessKeyId,SecretAccessKey,SessionToken]" \
            --output text))
- aws sts get-caller-identity
    # Validate the version does not already exist, fail if it does
    - echo "Looking for S3 key ${MSCV_BUCKET_NAME}/${SVC}/${MSCV_VERSION}"
    - aws s3 ls ${MSCV_BUCKET_NAME}/${SVC}/${MSCV_VERSION} && echo "This microservice configuration version has already been published. Try bumping the version." && exit 1
    - echo "Looking for S3 key ${MSCV_BUCKET_NAME}/${SVC1}/${MSCV_VERSION}"
    - aws s3 ls ${MSCV_BUCKET_NAME}/${SVC1}/${MSCV_VERSION} && echo "This microservice configuration version has already been published. Try bumping the version." && exit 1

  script:
    # Get the KMS key used by the bucket for SSE
    - export S3_KMS_KEY=$(aws s3api get-bucket-encryption --bucket ${MSCV_BUCKET_NAME} | jq -r '.ServerSideEncryptionConfiguration.Rules[].ApplyServerSideEncryptionByDefault.KMSMasterKeyID')
    - |
      aws s3 sync \
        --sse aws:kms \
        --sse-kms-key-id ${S3_KMS_KEY} \
        mscv/${SVC}/${ENVIRONMENT} \
        s3://${MSCV_BUCKET_NAME}/${SVC}/${MSCV_VERSION}
    - | aws s3 sync \
        --sse aws:kms \
        --sse-kms-key-id ${S3_KMS_KEY} \
        mscv/${SVC1}/${ENVIRONMENT} \
        s3://${MSCV_BUCKET_NAME}/${SVC1}/${MSCV_VERSION}
rules:
    - if: $AWS_ASSUME_ROLE_ARN != '' && $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - mscv/**/*
      when: always
    - if: $AWS_ASSUME_ROLE_ARN != '' && $CI_PIPELINE_SOURCE == 'web'
      when: always
    - when: never

update-mscv-dev:
  extends:
    - .update_mscv
  variables:
    ENVIRONMENT: dev
    AWS_ASSUME_ROLE_ARN: $AWS_ASSUME_ROLE_ARN_DEV
    MSCV_BUCKET_NAME: $MSCV_BUCKET_NAME_DEV
    SVC: $SVC
    SVC1: $SVC1


#update-mscv-dr:
#  extends:
#    - .update_mscv
#  variables:
#    ENVIRONMENT: dr
#    AWS_ASSUME_ROLE_ARN: $AWS_ASSUME_ROLE_ARN_DR
#    MSCV_BUCKET_NAME: $MSCV_BUCKET_NAME_DR



