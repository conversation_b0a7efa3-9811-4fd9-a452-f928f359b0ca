### He<PERSON> Deploy Job
#
# This job will publish a project to an EKS cluster via Helm
#
# required variables:
#   PROJECT_NAME:       # Name of the top-level project
#   REGISTRY_USER:      # Inherited from Gitlab - credentials for publishing artifacts
#   REGISTRY_PASS:      # Inherited from Gitlab - credentials for publishing artifacts
#   SERVICE_NAME:       # 
#   SERVICE_VERSION:    # 
#
# optional variables:
#   AWS_REGION:         # Default is us-east-1 - Override if deploying resources into a different region
#   AWS_ASSUME_ROLE_*:  # Assume role ARNs for each environment must be provided
#   EKS_CLUSTER_NAME_*: # Required if AWS_ASSUME_ROLE_* is set. Name of the EKS cluster you will be connecting to
#   MSCV_BUCKET_*:      # Required if AWS_ASSUME_ROLE_* is set, and the project uses MSCV buckets. Name of the S3 bucket containing microservice configs
#   MSCV_VERSION:       # Required if the project uses MSCV buckets
#   HELM_TIMEOUT:       # Length of time to wait for timeouts on <PERSON><PERSON> commands

variables:
  REPOSITORY: registry.us-east-1.build.socure.link
  IMAGE: ${PROJECT_NAME}/${SERVICE_NAME}
  IMAGE1:  ${PROJECT_NAME}/${SVC1}
  HELM_TIMEOUT: 5m

.deploy_script:
  image: registry.us-east-1.build.socure.link/platform/gitlab-runner-helm:1.0.0
  before_script:
    - aws sts get-caller-identity
    - |
      export $(printf "AWS_ACCESS_KEY_ID=%s AWS_SECRET_ACCESS_KEY=%s AWS_SESSION_TOKEN=%s" \
        $(aws sts assume-role \
        --role-arn "${AWS_ASSUME_ROLE_ARN}" \
        --role-session-name MySessionName \
      --query "Credentials.[AccessKeyId,SecretAccessKey,SessionToken]" \
      --output text))
- aws sts get-caller-identity
    - aws eks update-kubeconfig --name ${EKS_CLUSTER_NAME} --region ${AWS_REGION}
    - |
      helm repo add socure https://nexus.us-east-1.build.socure.link/repository/socure-helm/ \
        --ca-file /socure/certs/socure-bundle.crt \
        --username ${REGISTRY_USER} \
        --password ${REGISTRY_PASS}
- helm repo update
    - kubectl get ns
  script:
    - |
      helm upgrade -i ${SVC} \
        -f helm/values-${SVC}.yaml \
        --set image.tag=${SERVICE_VERSION}-${CI_PIPELINE_ID} \
        --set image.repository=${REPOSITORY}/${IMAGE} \
        --version ${CHART_VERSION} \
        --create-namespace \
        --namespace ${NAMESPACE} \
        socure/${CHART_NAME} --wait \
        --timeout ${HELM_TIMEOUT}
     - | 
        helm upgrade -i ${SVC1} \
        -f helm/values-${SVC1}.yaml \
        --set image.tag=${SERVICE_VERSION}-${CI_PIPELINE_ID} \
        --set image.repository=${REPOSITORY}/${IMAGE1} \
        --version ${CHART_VERSION} \
        --create-namespace \
        --namespace ${NAMESPACE} \
        socure/${CHART_NAME} --wait \
        --timeout ${HELM_TIMEOUT}
environment:
    name: $ENVIRONMENT
  when: manual

deploy-dev:
  stage: deploy-dev
  extends:
    - .deploy_script
  variables:
    EKS_CLUSTER_NAME: $EKS_CLUSTER_NAME_DEV
    AWS_ASSUME_ROLE_ARN: $AWS_ASSUME_ROLE_ARN_DEV
    SVC: transaction-auditing-service
    SVC1:transaction-auditing-worker 
    ENVIRONMENT: dev
  rules:
    - if: $EKS_CLUSTER_NAME_DEV && $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  when: always

deploy-stage:
  stage: deploy-stage
  extends:
    - .deploy_script
  variables:
    EKS_CLUSTER_NAME: $EKS_CLUSTER_NAME_STAGE
    AWS_ASSUME_ROLE_ARN: $AWS_ASSUME_ROLE_ARN_STAGE
    ENVIRONMENT: stage
  rules:
    - if: $EKS_CLUSTER_NAME_STAGE && $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

deploy-dr:
  stage: deploy-dr
  extends:
    - .deploy_script
  variables:
    EKS_CLUSTER_NAME: $EKS_CLUSTER_NAME_DR
    AWS_ASSUME_ROLE_ARN: $AWS_ASSUME_ROLE_ARN_DR
    ENVIRONMENT: dr
  rules:
    - if: $EKS_CLUSTER_NAME_DR && $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

deploy-prod:
  stage: deploy-prod
  extends:
    - .deploy_script
  variables:
    EKS_CLUSTER_NAME: $EKS_CLUSTER_NAME_PROD
    AWS_ASSUME_ROLE_ARN: $AWS_ASSUME_ROLE_ARN_PROD
    ENVIRONMENT: prod
  rules:
    - if: $EKS_CLUSTER_NAME_PROD && $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH


